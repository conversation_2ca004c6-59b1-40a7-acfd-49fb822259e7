window.RUNTIME_ENV = {
    PUBLIC_URL: "#{DashboardCdnUrl}",
    REACT_APP_API_URL: "#{DashboardApiUrl}",
// The CMS url is different to the API_URL to allow the use of cloudfront to cache the cms content.
    REACT_APP_CDN_API_URL: "#{DashboardCmsApiUrl}",
    REACT_APP_CDN_CACHED_URL: "#{DashboardCmsCachedUrl}",
    REACT_APP_SIGNIN_URL: "#{SigninCdnUrl}",
    REACT_APP_ENV: "#{Octopus.Environment.Name}",
    REACT_APP_Logger_MAX_MESSAGES: "#{LoggerMaxMessages}",
    REACT_APP_Logger_ENABLED: "#{LoggerEnabled}",
    REACT_APP_Log_Level: "#{LogLevel}",
    REACT_APP_Proctoring_Script_URL: "https://cdn.proview.io/client/init.js",
    REACT_APP_VERSION: "#{Octopus.Release.Number}",
    REACT_APP_CMS_Manifest: "Cms.Manifest.Dashboard",
    REACT_APP_DEFAULT_LANGUAGES: "#{DefaultSiteLanguage}",
    REACT_APP_API_VERSION: "1.0"
};