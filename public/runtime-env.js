window.RUNTIME_ENV = {
    PUBLIC_URL: "https://localhost:3002",
    REACT_APP_API_URL: "https://testapi.kfassessment.com/candidate/api/",
// The CMS url is different to the API_URL to allow the use of cloudfront to cache the cms content.
    REACT_APP_CDN_API_URL: "https://testapi.kfassessment.com/candidate/api/",
    REACT_APP_CDN_CACHED_URL: "",
    REACT_APP_SIGNIN_URL: "http://localhost:3001/", // "https://test.kfassessment.com",
    REACT_APP_ENV: "local",
    REACT_APP_Logger_MAX_MESSAGES: "5",
    REACT_APP_Logger_ENABLED: true,
    REACT_APP_Log_Level: "5000",
    REACT_APP_Proctoring_Script_URL: "https://cdn.proview.io/client/init.js",
    REACT_APP_VERSION: "DEV-LOCAL-POINTED-TO-TEST-ENV-API",
    REACT_APP_CMS_Manifest: "Cms.Manifest.Dashboard",
    REACT_APP_DEFAULT_LANGUAGES: "16",
    REACT_APP_API_VERSION: "1.0"
};