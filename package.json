{"name": "kf-assessments-dashboard", "version": "0.1.0", "private": true, "scripts": {"build-css": "sass src:src", "watch-css": "sass --watch src", "vite": "vite", "start": "run-p watch-css vite", "start:server:dev": "servor ./dist index.html 3002 --reload --browse --secure", "build": "vite build", "build:dev": "dotenv -e .env.devlocal -- run-s build", "build:watch:dev": "nodemon --watch src --ext js,jsx,ts,tsx --exec \"npm run build:dev\"", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "env:local": "node scripts/set-env.js local", "env:prod": "node scripts/set-env.js prod", "env:list": "node -e \"console.log('Available environments: local, prod')\"", "start:local": "npm run env:local && npm start", "build:local": "npm run env:local && npm run build", "build:prod": "npm run env:prod && npm run build"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "dependencies": {"bootstrap": "^5.3.7", "classnames": "2.3.2", "html-react-parser": "^3.0.7", "http-status": "1.5.3", "isomorphic-fetch": "3.0.0", "jsnlog": "2.22.1", "query-string": "4.3.4", "react": "18.2.0", "react-device-detect": "^2.2.3", "react-dom": "18.2.0", "react-helmet": "6.1.0", "react-intl": "6.2.5", "react-modal": "3.16.3", "react-redux": "7.2.9", "react-router-dom": "6.6.1", "react-toastify": "9.1.1", "react-tooltip": "^5.28.0", "redux-catch": "1.3.1", "redux-thunk": "2.4.2", "sanitize-html": "^2.15.0", "string-template": "1.0.0"}, "devDependencies": {"@testing-library/jest-dom": "5.16.5", "@testing-library/react": "14.0.0", "@types/classnames": "2.3.1", "@types/jest": "29.4.0", "@types/node": "^24.1.0", "@types/query-string": "4.3.1", "@types/react": "18.0.26", "@types/react-dom": "18.0.10", "@types/react-helmet": "6.1.6", "@types/react-intl": "3.0.0", "@types/react-modal": "3.13.1", "@types/react-redux": "7.1.25", "@types/react-router": "5.1.20", "@types/react-router-dom": "5.3.3", "@types/react-test-renderer": "18.0.0", "@types/redux-logger": "3.0.0", "@types/redux-mock-store": "1.0.3", "@types/redux-thunk": "2.1.0", "@types/sanitize-html": "^2.13.0", "@types/string-template": "1.0.2", "@vitejs/plugin-react": "^4.6.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "dotenv-cli": "8.0.0", "jest-environment-jsdom": "^29.5.0", "jsdom": "^26.1.0", "nodemon": "3.1.9", "npm-run-all": "4.1.5", "react-test-renderer": "18.2.0", "redux-logger": "3.0.6", "redux-mock-store": "1.5.4", "sass": "^1.89.2", "servor": "4.0.2", "ts-jest": "29.0.5", "typescript": "4.9.4", "vite": "^6.1.0", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}, "engines": {"node": ">=20.0.0"}}