{"name": "kf-assessments-signin", "version": "0.1.0", "private": true, "scripts": {"build-css": "sass src:src", "watch-css": "sass --watch src", "start-app": "react-scripts start", "build-app": "react-scripts build", "start": "run-p watch-css start-app", "start:server:dev": "servor ./build index.html 3001 --reload --browse --secure", "build": "run-s build-css build-app", "build:dev": "dotenv -e .env.devlocal -- run-s build", "build:watch:dev": "nodemon --watch src --ext js,jsx,ts,tsx --exec \"npm run build:dev\"", "test": "react-scripts test --env=jsdom --watchAll=false"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "dependencies": {"es6-promise": "4.2.4", "html-react-parser": "3.0.7", "isomorphic-fetch": "3.0.0", "js-base64": "2.6.2", "jsnlog": "2.22.1", "query-string": "4.3.4", "react": "18.2.0", "react-async-script": "0.9.1", "react-dom": "18.2.0", "react-helmet": "6.1.0", "react-intl": "6.2.5", "react-load-script": "0.0.5", "react-recaptcha": "2.3.8", "react-redux": "7.2.9", "react-router-dom": "6.6.1", "react-style-tag": "3.0.1", "react-toastify": "9.1.1", "redux-catch": "1.3.1", "redux-form": "8.3.9", "redux-form-validators": "3.3.2", "redux-logger": "3.0.6", "redux-thunk": "2.4.2", "string-template": "1.0.0"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@types/jest": "29.4.0", "@types/node": "7.0.65", "@types/query-string": "4.3.1", "@types/react": "18.0.26", "@types/react-document-title": "2.0.5", "@types/react-dom": "18.0.10", "@types/react-helmet": "6.1.6", "@types/react-intl": "3.0.0", "@types/react-redux": "7.1.25", "@types/react-router": "5.1.20", "@types/react-router-dom": "5.3.3", "@types/react-test-renderer": "18.0.0", "@types/redux-form": "8.3.5", "@types/redux-logger": "3.0.6", "@types/redux-mock-store": "0.0.9", "@types/redux-thunk": "2.1.0", "@types/string-template": "1.0.2", "bootstrap": "5.2.3", "dotenv-cli": "8.0.0", "nodemon": "3.1.10", "npm-run-all": "4.1.5", "react-scripts": "5.0.1", "react-test-renderer": "18.2.0", "redux-mock-store": "1.2.3", "sass": "1.58.3", "ts-jest": "29.0.5", "typescript": "4.9.4"}}