{"name": "kfas-admin-site", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build --prod", "buildLocal": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "^7.0.4", "@angular/cdk": "^7.1.0", "@angular/common": "~7.0.0", "@angular/compiler": "~7.0.0", "@angular/core": "~7.0.0", "@angular/forms": "~7.0.0", "@angular/http": "~7.0.0", "@angular/material": "^7.1.0", "@angular/platform-browser": "~7.0.0", "@angular/platform-browser-dynamic": "~7.0.0", "@angular/router": "~7.0.0", "@auth0/angular-jwt": "^2.0.0", "class-transformer": "^0.2.0", "core-js": "^2.5.4", "file-saver": "^2.0.1", "hammerjs": "^2.0.8", "jszip": "^3.6.0", "material-design-icons": "^3.0.1", "ngx-color-picker": "^7.5.0", "reflect-metadata": "^0.1.12", "rxjs": "~6.3.3", "zone.js": "~0.8.26"}, "devDependencies": {"@angular-devkit/build-angular": "^0.12.1", "@angular/cli": "~7.0.3", "@angular/compiler-cli": "~7.0.0", "@angular/language-service": "~7.0.0", "@types/file-saver": "^2.0.0", "@types/jasmine": "~2.8.8", "@types/jasminewd2": "~2.0.3", "@types/node": "~8.9.4", "angular2-template-loader": "^0.6.2", "codelyzer": "~4.5.0", "html-webpack-plugin": "^3.2.0", "jasmine-core": "~2.99.1", "jasmine-spec-reporter": "~4.2.1", "karma": "~3.0.0", "karma-chrome-launcher": "~2.2.0", "karma-coverage-istanbul-reporter": "~2.0.1", "karma-jasmine": "~1.1.2", "karma-jasmine-html-reporter": "^0.2.2", "node-sass": "^4.0.0", "protractor": "~5.4.0", "raw-loader": "^0.5.1", "ts-loader": "^5.2.2", "ts-node": "~7.0.0", "tslint": "~5.11.0", "typescript": "~3.1.1"}}