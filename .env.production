# VITE_PUBLIC_URL is the base url for content references in the public/index.html, it will only be replaced for production build i.e. npm run build
# Update the url with the cloud front cdn url, as octopus can deploy to more than one environment this should be managed by octopus deploy.
VITE_PUBLIC_URL=#{DashboardCdnUrl}
VITE_DEFAULT_LANGUAGES=#{DefaultSiteLanguage}
VITE_API_URL=#{DashboardApiUrl}
# The CMS url is different to the API_URL to allow the use of cloudfront to cache the cms content.
VITE_CDN_API_URL=#{DashboardCmsApiUrl}
VITE_CDN_CACHED_URL=#{DashboardCmsCachedUrl}
VITE_SIGNIN_URL=#{SigninCdnUrl}
VITE_ENV=#{Octopus.Environment.Name}
VITE_VERSION=#{Octopus.Release.Number}
VITE_Logger_MAX_MESSAGES=#{LoggerMaxMessages}
VITE_Logger_ENABLED=#{LoggerEnabled}
VITE_Log_Level=#{LogLevel}
VITE_Proctoring_Script_URL=https://cdn.proview.io/client/init.js