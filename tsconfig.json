{"compilerOptions": {"jsx": "react-jsx", "outDir": "build/dist", "module": "esnext", "target": "ESNext", "types": ["vite/client"], "lib": ["dom", "dom.iterable", "esnext"], "sourceMap": true, "allowJs": true, "moduleResolution": "node", "rootDir": "src", "forceConsistentCasingInFileNames": true, "noImplicitReturns": true, "noImplicitThis": true, "noImplicitAny": true, "strictNullChecks": true, "noUnusedLocals": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true}, "exclude": ["node_modules", "build", "scripts", "acceptance-tests", "webpack", "jest", "jest.config.js", "vite.config.mjs"], "types": ["typePatches"]}