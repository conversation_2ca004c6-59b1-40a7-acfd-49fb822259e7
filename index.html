<!doctype html>
<html lang="en" translate="no">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="theme-color" content="#000000">
    <meta name="robots" content="noindex, nofollow">
    <!--
      manifest.json provides metadata used when your web app is added to the
      homescreen on Android. See https://developers.google.com/web/fundamentals/engage-and-retain/web-app-manifest/
    -->
    <link rel="manifest" href="/manifest.json">
    <link rel="shortcut icon" href="/favicon.ico?v=1">
    <!--
      In Vite, files in the /public folder are served at the root path.
      So you can use /favicon.ico or /robots.txt directly.
    -->
    <title>KFAS Portal</title>
    <script src="/runtime-env.js"></script>
  </head>
  <body>
    <noscript>
      You need to enable JavaScript to run this app.
    </noscript>
    <div id="root">
     
     </div>    
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
    <!--
      Vite requires explicitly defining the app entry point.
      This script is used during development; Vite replaces it with optimized output in production.
    -->
      <script type="module" src="/src/index.tsx"></script>
  </body>
</html>
