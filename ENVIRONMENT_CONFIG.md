# Environment-Specific Runtime Configuration

This project uses environment-specific runtime configuration files to manage different deployment environments. This approach allows you to:

- ✅ **Build once, deploy everywhere** - No need to rebuild for different environments
- ✅ **Runtime configuration** - Environment variables are loaded at runtime, not build time
- ✅ **Easy switching** - Simple scripts to switch between environments during development, with Template-based deployment with variable substitution

## 📁 Environment Files

| File | Purpose | Usage |
|------|---------|--------|
| `runtime-env.local.js` | Local development | Direct browser access |
| `runtime-env.prod.js` | Production environment | Live production deployment |
| `runtime-env.js` | **Active configuration** | Currently used by the application |

## 🚀 Quick Start

### **Local Development**
```bash
# Set to local environment and start development
npm run start:local

# Or manually switch environment
npm run env:local
npm start
```

### **Building for Different Environments**
```bash
# Build for specific environments
npm run build:local    # Local development build
npm run build:prod     # Production environment
```

### **Manual Environment Switching**
```bash
# Using NPM scripts
npm run env:local      # Switch to local
npm run env:prod       # Switch to production
npm run env:list       # Show available environments

# Using Node.js script directly
node scripts/set-env.js local
node scripts/set-env.js prod

# Using PowerShell (Windows)
.\scripts\set-env.ps1 local
.\scripts\set-env.ps1 prod
```

## 🔧 Environment Configuration Details

### **Local Development (`runtime-env.local.js`)**
- **API URL**: `https://testapi.kfassessment.com/candidate/api/`
- **Log Level**: `2000` (DEBUG) - Verbose logging for development
- **ReCaptcha**: Test site key
- **Resource Files**: Local `../resources/data` paths

### **Production Environment (`runtime-env.prod.js`)**
Contains Octopus Deploy variable placeholders:

```javascript
window.RUNTIME_ENV = {
    PUBLIC_URL: "#{SigninCdnUrl}",
    REACT_APP_API_URL: "#{SigninApiUrl}",
    REACT_APP_ENV: "#{Octopus.Environment.Name}",
    // ... other variables
};
```

## 🔍 Troubleshooting

### **Environment not switching?**
1. Check that `runtime-env.js` was updated: `cat public/runtime-env.js`
2. Verify the script ran successfully (look for ✅ success message)
3. Clear browser cache and reload

### **TypeScript errors?**
- All environment variables are properly typed in `src/types/global.d.ts`
- If you add new variables, update the `RuntimeEnv` interface

### **Build issues?**
- Ensure you have the correct environment set before building
- Check that all required variables are defined in your environment file

### **Octopus Deploy issues?**
- Verify all required variables are defined in Octopus Deploy
- Check variable scoping (ensure variables are available for your environment)
- Confirm template file syntax matches Octopus Deploy expectations

## 🛠 Adding New Environment Variables

1. **Add to TypeScript interface** (`src/types/global.d.ts`):
```typescript
export interface RuntimeEnv {
  // ... existing variables
  REACT_APP_NEW_VARIABLE: string;
}
```

2. **Update all environment files** with appropriate values

3. **Add to Octopus Deploy template** if needed:
```javascript
REACT_APP_NEW_VARIABLE: "#{NewVariable}",
```

4. **Use in code**:
```typescript
const newValue = window.RUNTIME_ENV?.REACT_APP_NEW_VARIABLE || 'default';
```

## 🎯 Best Practices

1. **Environment-Specific Values**: Use different values for each environment (URLs, keys, etc.)
2. **Secure Credentials**: Mark sensitive variables as "sensitive" in Octopus Deploy
3. **Defaults**: Always provide fallback values in your code
4. **Testing**: Test environment switching locally before deploying
5. **Documentation**: Update this file when adding new environment variables
6. **Validation**: Consider adding runtime validation for critical configuration values
