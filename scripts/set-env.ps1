# PowerShell script to set environment configuration
# Usage: .\scripts\set-env.ps1 [local|prod]

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("local", "prod")]
    [string]$Environment
)

$sourceFile = "public\runtime-env.$Environment.js"
$targetFile = "public\runtime-env.js"

try {
    # Check if source file exists
    if (-not (Test-Path $sourceFile)) {
        Write-Error "❌ Environment file not found: $sourceFile"
        exit 1
    }

    # Copy the environment-specific file
    Copy-Item $sourceFile $targetFile -Force
    
    Write-Host "✅ Successfully set environment to: $Environment" -ForegroundColor Green
    Write-Host "📄 Copied: runtime-env.$Environment.js -> runtime-env.js" -ForegroundColor Cyan
    
    # Show current configuration
    $content = Get-Content $targetFile -Raw
    if ($content -match 'REACT_APP_ENV:\s*"([^"]+)"') {
        $env = $matches[1]
        Write-Host "🌍 Environment: $env" -ForegroundColor Yellow
    }
    
    if ($content -match 'REACT_APP_API_URL:\s*"([^"]+)"') {
        $apiUrl = $matches[1]
        Write-Host "🔗 API URL: $apiUrl" -ForegroundColor Yellow
    }
    
} catch {
    Write-Error "❌ Error setting environment: $_"
    exit 1
}
