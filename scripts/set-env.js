#!/usr/bin/env node

/**
 * Script to copy environment-specific runtime configuration
 * Usage: node scripts/set-env.js [local|prod]
 */

const fs = require('fs');
const path = require('path');

const environment = process.argv[2];
const validEnvironments = ['local', 'prod'];

if (!environment || !validEnvironments.includes(environment)) {
    console.error('❌ Please specify a valid environment: local or prod');
    console.error('Usage: node scripts/set-env.js [local|prod]');
    process.exit(1);
}

const sourceFile = path.join(__dirname, `../public/runtime-env.${environment}.js`);
const targetFile = path.join(__dirname, '../public/runtime-env.js');

try {
    // Check if source file exists
    if (!fs.existsSync(sourceFile)) {
        console.error(`❌ Environment file not found: ${sourceFile}`);
        process.exit(1);
    }

    // Copy the environment-specific file
    fs.copyFileSync(sourceFile, targetFile);
    
    console.log(`✅ Successfully set environment to: ${environment}`);
    console.log(`📄 Copied: runtime-env.${environment}.js -> runtime-env.js`);
    
    // Show current configuration
    const content = fs.readFileSync(targetFile, 'utf8');
    const envMatch = content.match(/REACT_APP_ENV:\s*"([^"]+)"/);
    const apiMatch = content.match(/REACT_APP_API_URL:\s*"([^"]+)"/);
    
    if (envMatch && apiMatch) {
        console.log(`🌍 Environment: ${envMatch[1]}`);
        console.log(`🔗 API URL: ${apiMatch[1]}`);
    }
    
} catch (error) {
    console.error('❌ Error setting environment:', error.message);
    process.exit(1);
}
