@import "./styles/bootstrap";
@import "./styles/common/common";
@import "./components/components";
@import "./styles/rtl/rtl";
@import "./styles/common/resize";
@import "../node_modules/react-toastify/dist/ReactToastify";

body {
    font-family: $font-proxima;
    color: $text-color;
}

.bodyArial {
    font-family: Arial, sans-serif !important;
    color: $text-color;
    * {
        font-family: Arial, sans-serif !important;
    }
}

/* Globally shared styles  TODO : delete once styles are in place */
.error {
    @extend .text-danger;
}

.warning {
    @extend .text-warning;
}

.disabled-link {
    pointer-events: none;
}

a {
    text-decoration: none;
    &:hover {
        opacity: 0.95;
        text-decoration: underline;
    }
}
.form-control, .form-field-input input, .signinpanel-explanation input {
    border: 1px solid $input-border-color;
}

@media (prefers-reduced-motion: no-preference) {
    html {
        scroll-behavior: smooth;
        scroll-padding-top: 8rem;
        scroll-padding-bottom: 8rem;
    }
}

*:focus {
    scroll-margin-top: 8rem;
    scroll-margin-bottom: 8rem;
}