declare global {
  interface Window {
    RUNTIME_ENV?: {
      PUBLIC_URL?: string;
      REACT_APP_API_URL?: string;
      REACT_APP_CDN_API_URL?: string;
      REACT_APP_CDN_CACHED_URL?: string;
      REACT_APP_SIGNIN_URL?: string;
      REACT_APP_ENV?: string;
      REACT_APP_Logger_MAX_MESSAGES?: string;
      REACT_APP_Logger_ENABLED?: string | boolean;
      REACT_APP_Log_Level?: string | number;
      REACT_APP_Proctoring_Script_URL?: string;
      REACT_APP_VERSION?: string;
      REACT_APP_CMS_Manifest?: string;
      REACT_APP_DEFAULT_LANGUAGES?: string;
      REACT_APP_API_VERSION?: string;
    };
  }
}

export {};
