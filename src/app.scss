// Root sass stylesheet for the whole application.

// ALL @use RULES FIRST (files that don't need global mixins)
@use "./assets/fonts/fonts";
// Moved to @import section to access Bootstrap modal classes
@use "./components/session-timeout-warning/sessionTimeoutWarningModal";
@use "./components/redirect-warning/redirectWarningModal";
@use "./components/common/loading/loading";
@use "./components/common/environment-console/environment-console";
@use "./styles/common/resize";
@use "./components/session-timeout-page/sessionTimeoutContainer";
@use "./components/proctoring/assessmentPlayer";
@use "./components/proctoring/unsupportedDeviceWarningModal";
@use "../node_modules/react-tooltip/dist/react-tooltip";
@use "../node_modules/react-toastify/dist/ReactToastify";

// ALL @import RULES AFTER (files needing global mixins)  
@import "./styles/bootstrap";
@import "./styles/index";
@import "./styles/modal";
@import "./styles/reuseDialogs";
@import "./components/common/site-language";
@import "./components/common/step-progress-bar/step-progress-bar";
@import "./components/dynamic-feedback-overlay/dynamicFeedbackOverlay";
@import "./components/common/error-page/error-page";
@import "./components/proctoring/dataCollectionDisclosureModal";
@import "./components/header/header";
@import "./components/footer/footer";
@import "./pages/dashboard";
@import "./styles/rtl/rtl";

body {
    font-family: ProximaNova;
}

.bodyArial * {
    font-family: Arial !important;
}

@media (prefers-reduced-motion: no-preference) {
    html {
        scroll-behavior: smooth;
        scroll-padding-top: 8rem;
        scroll-padding-bottom: 8rem;
    }
}

*:focus {
    scroll-margin-top: 8rem;
    scroll-margin-bottom: 8rem;
}
