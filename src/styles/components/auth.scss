:host {
    flex: 1 1 auto;

    display: flex;
    flex-direction: column;

    .login-content {
        flex: 1 1 auto;
        display: flex;
        background: url('assets/images/home/<USER>') no-repeat #02717a;
        background-size: cover;

        .login-section {
            width: 41.5%;
            min-width: 456px;
            background: rgba(255, 255, 255, .95);
            background-position: left top;
            background-size: cover;

            .login-box {
                width: 288px;
                padding: 20% 0;
                margin-left: 35%;

                display: flex;
                flex-direction: column;

                .login-header {
                    img {
                        height: 21px;
                    }

                    hr {
                        margin-top: 20px;
                        margin-bottom: 20px;
                        border: 0;
                        border-top: 1px solid #eee;
                    }
                }

                .login-form {
                    h2.welcome {
                        letter-spacing: 1.5px;
                        line-height: 38px;
                        text-transform: uppercase;
                    }

                    .recaptcha {
                        margin-top: 26px;
                        transform: scale(.95);
                        transform-origin: left;
                    }

                    .form-field {
                        margin-top: 20px;

                        label {
                            display: inline-block;
                            font-size: 12px;
                            line-height: 18px;
                            letter-spacing: .2px;
                            margin-bottom: 5px;
                            text-transform: capitalize;
                        }

                        input {
                            display: block;
                            width: 100%;

                            &.ng-touched.ng-invalid {
                                border-color: #d32534;
                                background: #f6e6e7;
                            }

                            &.ng-untouched+.mat-errors {
                                display: none;
                            }
                        }

                        .mat-error {
                            display: block;
                            margin-top: 6px;
                            font-size: 12px;
                            line-height: 18px;
                        }

                        .btn-primary {
                            display: block;
                            width: 100%;
                            margin-top: 26px;
                        }

                        a {
                            &:hover {
                                text-decoration: underline;
                            }
                        }
                    }
                }
            }
        }
    }
}

@media (min-width: 1320px) {
    :host {
        .login-content {
            .login-section {
                .login-box {
                    margin-left: 47%;
                }
            }
        }
    }
}