.mat-table {
  font: unquote($proxima-font);
  overflow-y: auto;

  .mat-row {
    border-bottom-width: 0;

    &:nth-child(even) {
      background-color: $primary--grey-light;
    }

    &:last-child {
      border-bottom-width: 1px;
    }
  }

  .mat-header-row {
    position: sticky;
    top: 0;
    background-color: inherit;
  }
}

.mat-cell,
.mat-header-cell {
  box-sizing: border-box;
}

.mat-cell.number {
  justify-content: flex-end;
}

.mat-header-cell,
.mat-sort-header-button {
  @extend .utility;
  text-transform: uppercase;
  font-weight: 100;
}

.mat-header-cell {
  white-space: nowrap;
  padding: 10px;

  &.boolean {
    justify-content: center;
  }
}

.mat-sort-header-arrow {
  color: $primary--blue;
}

.mat-column-displayOrder {
  max-width: 100px !important;
}

.mat-cell {
  padding: 10px;
  font: unquote($proxima-font);

  .mat-chip-list-wrapper .mat-standard-chip,
  .mat-chip-list-wrapper input.mat-input-element {
    margin: 3px;
  }

  .mat-standard-chip {
    padding: 1px 7px;
    min-height: 22px;
    font-size: 13px;
  }

  &.boolean,
  &.rowOptions {
    &:not(.custom) {
      justify-content: center;
    }
  }

  &.array {
    align-items: flex-start;
  }
}

.empty {
  color: #757575;
  background-color: #fff;
  padding: 30px 15px;
  text-align: center;
}

// Specific class for tables that need wide content display
.mat-table.wide-content {
  .mat-row {
    display: flex; 
    width: max-content;
    min-width: 100%;
  }
}
