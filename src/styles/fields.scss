$shadow: 0 0 5px 1px lighten($primary--blue, 0.8);

input:not([type=submit]):not([type=button]), select {
  box-sizing: border-box;
  background-color: white;
  outline: none;
  color: #000;
  border: 1px solid rgba(145, 145, 145, 0.3);
  padding: 14px 16px;
  font: unquote($proxima-font);
  font-size: 14px;
  line-height: 18px;

  &:hover {
    border-color: $primary--blue-light;
  }

  &:focus,
  &:focus-within {
    border-color: $primary--blue-light;
    -webkit-box-shadow: $shadow;
    -moz-box-shadow: $shadow;
    box-shadow: $shadow;
  }

  &[disabled],
  &:disabled {
    background-color: $disabled-color;
    border-color: rgba(0, 0, 0, 0.1);
    color: $primary--grey;
  }
}

.mat-form-field-infix {
  display: flex;
  flex-direction: column;

  label+input.mat-input-element {
    margin-top: 8px;
  }
}

.mat-form-field-underline {
  display: none;
}

.error{
  color: $secondary--red
}
