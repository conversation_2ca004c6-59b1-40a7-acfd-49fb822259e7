$primary--blue: #007da4;
$primary--blue-light: #b3d8e4;
$primary--blue-medium: #007bc7;
$primary--blue-dark: #005184;

$primary--grey: #919191;
$primary--grey-light: #eef1f3;
$primary--grey-medium: #d8d8d8;
$primary--grey-dark: #757575;

$primary--green: #02717a;

$secondary--red: #d32534;
$secondary--orange: #ff9d3d;
$secondary--yellow: #ffcd00;
$secondary--green: #92cd4c;
$secondary--green-dark: #00aa43;
$secondary--blue-gradient: linear-gradient(to right, #00d6df, #00adbb);

$action-color: rgba(0, 89, 113, 0.7);
$disabled-color: rgba(145, 145, 145, 0.05);
$weak-glass-color: rgba(255, 255, 255, 0.33);
$glass-color: rgba(255, 255, 255, 0.66);
$strong-glass-color: rgba(255, 255, 255, 0.9);

$palette-colors: (
  primary--blue: $primary--blue,
  primary--blue-light: $primary--blue-light,
  primary--blue-medium: $primary--blue-medium,
  primary--blue-dark: $primary--blue-dark,
  primary--grey: $primary--grey,
  primary--grey-light: $primary--grey-light,
  primary--grey-medium: $primary--grey-medium,
  primary--grey-dark: $primary--grey-dark,
  primary--green: $primary--green,
  secondary--red: $secondary--red,
  secondary--orange: $secondary--orange,
  secondary--yellow: $secondary--yellow,
  secondary--green: $secondary--green,
  secondary--green-dark: $secondary--green-dark,
  secondary--blue-gradient: $secondary--blue-gradient,
  action-color: $action-color,
  disabled-color: $disabled-color
);

@function color($key) {
  @if map-has-key($palette-colors, $key) {
    @return map-get($palette-colors, $key);
  }
  @error "Unknown `#{$key}` in $palette-colors.";
  @return null;
}

@mixin color-classes($colors-map: $palette-colors) {
  @each $name, $color in $colors-map {
    .color-#{$name} {
      color: $color;
    }
    .bg-#{$name} {
      background-color: $color;
    }
  }
}
