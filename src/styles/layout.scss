html,
body {
  box-sizing: border-box;
  height: 100%;
  background-color: #eef3f6;
}

a {
  margin: 0;
  padding: 0;
}

.form-group {
  display: flex;
}

.flex-container {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  justify-content: flex-start;
}

.float-right {
  float: right;
}

.float-left {
  float: left;
}

.container {
  width: 90%;
  margin: 0 auto;
  max-width: 1280px;

  &.narrow {
    max-width: 720px;
  }
}

.page-content {
  margin: 0 64px 48px 64px;
}

.details-page-content {
  margin: 0 64px 48px 64px;
  max-width: 1200px;
  padding: 48px;
}

.details-page-fit-content {
  margin: 0 64px 48px 64px;
  max-width: 1200px;
  width: fit-content;
  padding: 48px;
}

mat-dialog-container.mat-dialog-container {
  min-width: 400px;
}

mat-dialog-container.mat-dialog-container {
  min-width: 400px;
}
