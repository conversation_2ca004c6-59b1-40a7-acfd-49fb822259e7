@import 'colors.scss';

$proxima-font: '400 14px/20px ProximaNova, Helvetica, Arial, sans-serif';

%base {
    font: unquote($proxima-font);
    font-size: 14px;
    line-height: 20px;
    letter-spacing: 0.4px;
    text-align: left;
}

%small {
    font: unquote($proxima-font);
    font-size: 12px;
    line-height: 18px;
    letter-spacing: 0.2px;
    text-align: left;
}

%h {
    font: unquote($proxima-font);
    font-weight: 700;
    text-align: left;
    letter-spacing: 0.5px;
}

%link {
    color: $primary--blue;
    text-decoration: none;
}

.white,
.light {
    color: white;
}

.black,
.dark {
    color: black;
}

.grey,
.faded {
    color: $primary--grey;
}

.blue,
.primary,
.link {
    @extend %link;
}

body {
    @extend %base;
    color: black;
}

a {
    @extend %link;
}

label,
.small {
    @extend %small;
}

b,
label,
strong,
.semibold,
.bold {
    font: unquote($proxima-font);
    font-size: inherit;
    font-weight: 600;
}

h1,
.h1,
h2,
.h2,
h3,
.h3,
h4,
.h4 {
    @extend %h;
}

h1,
.h1,
h2,
.h2,
h3,
.h3 {
    margin-top: 20px;
    margin-bottom: 10px;
    transform: scaleY(.9);
}

h1,
.h1 {
    font-size: 36px;
    line-height: 40px;
}

h2,
.h2 {
    font-size: 30px;
    font-weight: 700;
}

h3,
.h3 {
    font-size: 24px;
    line-height: 40px;
}

h4,
.h4 {
    font-size: 16px;
    line-height: 20px;
}

.uppercase {
    text-transform: uppercase;
}

.utility {
    @extend %small;
    font: unquote($proxima-font);
    font-size: 12px;
    letter-spacing: 1.5px;

    &-small {
        font-size: 10px;
        letter-spacing: 1.25px;
    }
}

* {

  /* width */
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  /* Track */
  &::-webkit-scrollbar-track {
    background: $primary--grey-light;
  }

  /* Handle */
  &::-webkit-scrollbar-thumb {
    background: $primary--grey-medium;
  }

  /* Handle on hover */
  &::-webkit-scrollbar-thumb:hover {
    background: $primary--grey-dark;
  }
}
