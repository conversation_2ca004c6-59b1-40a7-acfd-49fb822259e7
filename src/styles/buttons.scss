%btn {
  font-family: ProximaNova;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 160px;
  background: transparent;
  outline: none;
  border: 1px solid;
  padding: 12px 32px;
  font-size: 14px;
  font-weight: 600;
  line-height: 18px;
  text-decoration: none;
  text-align: center;
  color: inherit;
  letter-spacing: 0.5px;
  cursor: pointer;
  text-transform: capitalize;
  display: inline-block;
  margin-right: 6px;

  .mat-icon {
    margin-right: 6px;
  }
}

@mixin btn-classes-builder($className, $background, $border, $color) {
  .#{$className} {
    @extend %btn;
    background-color: $background;
    border-color: #{$border};
    color: #{$color};
  }
}

@include btn-classes-builder(
  "btn-primary",
  $primary--blue-medium,
  $primary--blue-medium,
  white
);
@include btn-classes-builder(
  "btn-secondary",
  white,
  $action-color,
  $primary--blue
);
@include btn-classes-builder(
  "btn-disabled",
  $disabled-color,
  rgba(0, 0, 0, 0.1),
  $primary--grey
);

.btn-primary {
  $shadow: 0px 0px 1px 0px white;

  &:focus,
  &--focused {
    -webkit-box-shadow: $shadow;
    -moz-box-shadow: $shadow;
    box-shadow: $shadow;
  }

  &:active,
  &--active,
  &--pressed {
    background-color: $primary--blue-medium;
    border-color: $primary--blue-medium;
  }

  &:hover,
  &--hovered {
    background-color: $primary--blue-dark;
    border-color: $primary--blue-dark;
  }
}

.btn-secondary {
  $shadow: 0px 0px 1px 0px $primary--blue-light;

  &:focus,
  &--focused {
    -webkit-box-shadow: $shadow;
    -moz-box-shadow: $shadow;
    box-shadow: $shadow;
  }

  &:active,
  &--active,
  &--pressed {
    background-color: lighten($color: $primary--blue-light, $amount: 60);
    border-color: $primary--blue;
    color: $primary--blue-medium;
  }

  &:hover,
  &--hovered {
    border-color: $primary--blue-dark;
    color: $primary--blue-dark;
  }
}

.btn-disabled {
  cursor: auto;
  background-color: $disabled-color;
  border-color: rgba(0, 0, 0, 0.1);
  color: $primary--grey;
}

.btn {
  @extend %btn;

  &[disabled],
  &:disabled {
    @extend .btn-disabled;
  }
}

@at-root .mat-menu-panel {
  max-width: fit-content !important;
  width: fit-content !important;
}

@at-root .mat-menu-content {
  padding: 0 !important;
}

@at-root .mat-menu-item {
  font: unquote($proxima-font);
  font-size: 1.1em;
  &.active {
    font-weight: 600;
  }
  .mat-icon {
    margin-right: 6pt;
  }
}
