import { LaunchAssessmentResult, AssessmentReuseRequest, AssessmentReuseResult, AssessmentStatus, AssessmentStatusUpdateModel } from '../models/assessment';
import { CandidateModel, CandidateUpdateModel } from '../models/candidate';
import { getObject, postObject } from './api-service';

/**
 * Gets all candidate data for the current authenticated candidate.
 * @returns A CandidateModel that contains all current information on the server.
 */
export const getData = (): Promise<CandidateModel> => getObject('candidate', (json) => <CandidateModel> json);

/**
 * Updates the candidate preferred language after the site language has changed.
 * @param languageId The new preferred language
 */
export const updateCandidateLanguage = (languageId: number) => postObject<CandidateUpdateModel, string>('candidate', { languageId: languageId }, (result) => '');

/**
 * Gets the launch url to start a Reflex assessment by redirecting the browser to the provided url.
 * @param assessmentId The id of the assessment to launch.
 * @param language The language of the assessment.
 */
export const getLaunchAssessmentUrl = (assessmentId: number, languageId: number) =>
    getObject(
        'assessment/',
        (json) => <LaunchAssessmentResult> json,
        { id: assessmentId, language: languageId });

export const reuseAssessment = (assessmentId: number, reuseAssessmentId: number) =>
    postObject<AssessmentReuseRequest, AssessmentReuseResult>(
        'AssessmentReuse',
        { assessmentId: assessmentId, reuseAssessmentId: reuseAssessmentId },
        (result) => <AssessmentReuseResult> result);

/**
 * Gets candidate data for preview, data is stored locally within json file (misc/PreviewCandidate.json') 
 * @export
 * @returns {Promise<CandidateModel>} 
 */
export async function GetDataPreviewCandidate(brandingName: string): Promise<CandidateModel> {
    try {
        const candidateData = await import('../misc/PreviewCandidate.json');
        let candidate: CandidateModel = {
            ...candidateData.default,
            personal: {
                ...candidateData.default.personal,
                email: "<EMAIL>",
                candidateId: candidateData.default.personal.id
            },
            state: {
                ...candidateData.default.state,
                demographicsReusable: false
            },
            assessments: candidateData.default.assessments.map((assessment: any) => ({
                ...assessment,
                jobProfileName: '',
                name: '',
                description: '',
                reuseAssessmentAvailable: false,
                reuseAssessmentId: null,
                reuseAssessmentDateTime: null,
                latestProjectDeadline: null,
                isProctored: false,
                proctoringToken: null,
                isProctoringLocal: false,
                scope: null,
                completed: null
            })),
            reports: candidateData.default.reports.map((report: any) => ({
                ...report,
                date: new Date(report.date),
                languageId: 1,
                blendedReportId: 0,
                format: 'PDF'
            })),
            candidateContactInfo: {
                ...candidateData.default.candidateContactInfo,
                contactNumber: parseInt(candidateData.default.candidateContactInfo.contactNumber, 10)
            },
            brandingStyles: {
                ...candidateData.default.brandingStyles,
                errorText: ""
            },
            learningContents: [],
            successProfilePublished: true
        };
        candidate.brandingStyles.name = brandingName;
        return candidate;
    } catch (e) {
        throw e;
    }
}

export const updateAssessmentStatus = async (assessmentId: number, status: AssessmentStatus, scope: string) => {
    return postObject<AssessmentStatusUpdateModel, any>('status', { assessmentId: assessmentId, status: status, scope: scope }, (result) => result);
}