import 'isomorphic-fetch';
import { AppService } from './app-service';
import * as Models from '../misc/models';

/* Service to get branding for a client */
export namespace BrandingService {

    export function GetBranding(branding: string | undefined, isPreview: boolean = false): Promise<Models.BrandingResult> {
        if (branding) {
            let params: AppService.GetParam[] = [
                new AppService.GetParam('name', branding),
                new AppService.GetParam('isPreview', String(isPreview))
            ];
            
            // We need to get the version from the server so that if the branding gets updated, CloudFront
            // will get the new version and cache it

            // directly return the result of the branding/version endpoint
            return AppService.GetThenToObject('branding/version', params, Models.BrandingResult);
        }

        return Promise.resolve(new Models.BrandingResult());
    }
}