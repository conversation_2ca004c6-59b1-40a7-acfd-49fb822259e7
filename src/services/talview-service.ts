/**
 * Talview Proctoring Manager
 */
import AppConstants from "../constants/app";
import { 
    ProctoringSession, 
    ProctoringAlertTypeIds 
} from "../models/proctoring";
import { postObject } from './api-service';
import { logErrorSilent } from '../misc/error';
import { updateAssessmentStatus} from './candidate-service';
import { AssessmentStatus } from '../models/assessment';
import * as BrowserUtils from "../misc/browser";

const proctoringStatuses = AppConstants.Proctoring.Statuses

export default class TalviewProctoringManager {
    status: any;
    config: any;
    terminated: boolean;

    constructor(config: any) {
        this.config = config;
        this.status = proctoringStatuses.STOPPED;
        this.terminated = false;
    }

    async init() {
        return new Promise<void>((resolve, reject) => {
            console.debug("%c*** Initializing Talview proctoring manager", "color: orange");
            BrowserUtils.interceptWindowBeforeUnloadListeners();
            resolve();
        });
    }

    async start() {

        const {
            token,
            profileId,
            session,
            sessionTitle,
            sessionType,
            scriptUrl,
            additionalInstructions,
            enforceTSB
        } = this.config;

        let self = this;
        const isScreenProtectionEnabled = enforceTSB; 

        return new Promise<string>((resolve, reject) => {
            const sessionDetails = {
                token: token,
                profileId: profileId,
                session: session,
            };
            
            console.debug("%c*** Starting Talview proctoring session", "color: orange", sessionDetails);
            
            // Entire code block needs to be together, 
            // can't load script first, then call tv
            (function (i, s, o, g, r, a, m) {
                // @ts-ignore
                i['TalviewProctor'] = r;
                // @ts-ignore
                i[r] = i[r] || function () {
                    // @ts-ignore
                    (i[r].q = i[r].q || []).push(arguments)
                },
                    // @ts-ignore
                    i[r].l = 1 * new Date(); 
                
                // @ts-ignore
                a = s.createElement(o),
                    // @ts-ignore
                    m = s.getElementsByTagName(o)[0];
                // @ts-ignore
                a.async = 1;
                // @ts-ignore
                a.src = g;
                 // @ts-ignore
                m.parentNode.insertBefore(a, m)
            })(window, document, 'script', scriptUrl, 'tv');

            /* @ts-ignore */
            tv('init', token, {
                profileId: profileId,
                session: session,
                sessionTitle: sessionTitle,
                sessionType: sessionType,
                additionalInstruction: additionalInstructions,
                enforceTSB: enforceTSB,
                isScreenProtectionEnabled: isScreenProtectionEnabled,
                initCallback: (error: any, uuid: string) => {
                    if (error) {
                        reject(
                            new Error(
                                `Error initializing proctoring session: ${ error.message || error }` + 
                                `; Error Details: ${ JSON.stringify(sessionDetails, null, 4) }`
                            )
                        );
                    } else {
                        self.initListeners(self.config);
                        self.status = proctoringStatuses.STARTED;
                        resolve(uuid);
                    }
                },
                errorCallback: (error: any, source: any) => {
                    logErrorSilent(
                        `Error during proctoring session: ${ error.message || error }` +
                        `; Source: ${ source }` +
                        `; Error Details: ${ JSON.stringify(sessionDetails, null, 4) }`
                    );
                },
                networkDisconnectionCallback: () => {
                    logErrorSilent(`Network disconnected, session pause; Details: ${ JSON.stringify(sessionDetails, null, 4) }`);
                }
            });

            this.checkProctorClient3Initialized(this.config);

            const stopProctoring = (redirectPath?: string) => {
                // @ts-ignore
                ProctorClient3.stop(() => {
                    console.debug('%cProctoring stopped', 'color: yellow');
                    this.status = proctoringStatuses.STOPPED;
                    if (redirectPath) {
                        console.debug("%cRedirecting to", "color: green", redirectPath);
                        history.replaceState({}, '', redirectPath);
                        history.go();
                    }
                })
            }

            const pauseProctoring = (redirectPath?: string) => {
                // @ts-ignore
                ProctorClient3.pause();
                
                this.status = proctoringStatuses.PAUSED;

                if (redirectPath) {
                    setTimeout(() => {
                        console.debug('%cProctoring paused', 'color: yellow');
                        console.debug("%cRedirecting to", "color: green", redirectPath);
                        history.replaceState({}, '', redirectPath);
                        history.go();    
                    }, 3000);    
                }                
            }

            // Create IE + others compatible event handler
            // @ts-ignore
            const eventMethod = window.addEventListener ? "addEventListener" : "attachEvent";
            // @ts-ignore
            const eventer = window[eventMethod];
            const messageEvent = eventMethod == "attachEvent" ? "onmessage" : "message";
            const MessageEventActions = AppConstants.Proctoring.MessageEventActions;

            // Listen to message from child window
            eventer(messageEvent, (event: any) => {
                const key = event.message ? "message" : "data";
                const data = event[key];
                if (data.action) {
                    if (data.action === MessageEventActions.PAUSE) {
                        console.debug("%cReceived PAUSE proctoring message", "color: lightgreen");
                        pauseProctoring(data.redirectLocationHref);
                    }
                    if (data.action === MessageEventActions.STOP) {
                        console.debug("%cReceived STOP proctoring message", "color: lightgreen");
                        stopProctoring(data.redirectLocationHref);
                    }
                }
            }, false);
        });
    }

    async stop() {
        const status = this.getStatus();
        return new Promise<void>((resolve, reject) => {
            if (status !== proctoringStatuses.STOPPED) {
                try {
                    console.debug("%c*** Stopping Talview proctoring session", "color: orange");
                    // @ts-ignore
                    window.ProctorClient3.stop(() => {
                        this.status = proctoringStatuses.STOPPED;
                        resolve();
                    });
                } catch (error) {
                    reject(`Error stopping proctoring session: ${ error.message || error }`);
                }
            } else {
                resolve();
            }
        });
    }

    async pause(callback?: () => void) {
        return new Promise<void>(async (resolve, reject) => {
            try {
                
                console.debug("%c*** Pausing Talview proctoring session", "color: orange");

                // @ts-ignore
                window.ProctorClient3.pause();

                this.status = proctoringStatuses.PAUSED;
                    
                if (callback) {
                    callback();
                }
                
                resolve();
            } catch (error) {
                console.error("Error pausing proctoring session", error);
                reject(error);
            }
        });
    }

    cleanUp() {
        return new Promise<void>((resolve) => {
            console.debug("%c*** Cleaning up the Talview Proctoring Manager", "color: yellow");
            // @ts-ignore
            window.removeAllBeforeUnloadListeners();
            resolve();
        });
    }

    terminate(config: any) {
        return new Promise<void>(async (resolve, reject) => {
            const { assessmentId, session } = config;

            try {

                console.debug("%c*** Terminating Talview proctoring session", "color: orange");
            
                // Marking assessment as terminated
                this.terminated = true;
                
                // Updating assessment status to Terminated
                await updateAssessmentStatus(assessmentId, AssessmentStatus.Terminated, session);

                // Cleaning up the proctoring manager
                await this.cleanUp();

                // Redirecting to dashboard page
                window.location.replace("/");
                
                resolve();
                
            } catch (error) {
                const errorMessage = 
                    `Error terminating participant: ${ error.message || error }, ` +
                    `assessmentId: ${ assessmentId }, ` + 
                    `session: ${ session }`;
                console.error(errorMessage);
                logErrorSilent(errorMessage);
                reject(error);
            }
        });
    }

    checkProctorClient3Initialized(config: any) {
        let interval = setInterval(() => {
             // @ts-ignore
            if (window.ProctorClient3) {
                this.initListeners(config);
                clearInterval(interval);
            }
        }, 50);
    }

    initListeners(config: any) {
        // @ts-ignore
        window.ProctorClient3.on('log:event:all', async (data: any) => {
            const { alert_type_id } = data;
            
            if (alert_type_id === ProctoringAlertTypeIds.FaceAuthenticationFailed) {
                await this.terminate(config);
                console.debug("%cThe participant has been removed from the assessment.", "color: lightgreen");
            }
            if (config.debug) {
                console.debug("%cTalview event", "color: yellow", JSON.stringify(data, null, 4));
            }
        });
    }

    getStatus() {
        return this.status;
    }

    async saveProctoringSession(proctoringSession: ProctoringSession) {
        return postObject<ProctoringSession, any>('proctoringSession', proctoringSession, (result) => result);
    }
}