import { getBlobAndDownload, postObject } from './api-service';

/**
 * Gets a candidate report and downloads it to the client.
 * @param id The ID of the project (hub) or assessment (legacy) to get the report for
 */

interface ReportDownloadParams {
    id: number;
    reportType?: string;
    reportLanguage?: number;
    reportOptions?: string;
    successProfileId?: number;
}
export const getReport = ({
    id,
    reportType = '',
    reportLanguage = 0,
    reportOptions,
    successProfileId
}: ReportDownloadParams): Promise<Blob> => {
    if (!reportType) return getBlobAndDownload('report/' + id, null);

    const params: Record<string, any> = {
        reportType,
        reportLanguage,
        reportOptions
    };

if (typeof successProfileId === 'number' && !isNaN(successProfileId)) {
    params.successProfileId = successProfileId;
}

    return getBlobAndDownload('report/' + id, params);
};

export interface DFRRequest {
    projectId: number;
    reportLocale: string;
    reportType: string
}

export const launchDynamicFeedback = (
    projectId: number,
    reportType: string = "",
    reportLocale: string = "en-US"
) => {
    
    const params = { projectId, reportType, reportLocale };
    return postObject<DFRRequest, string>(
        "dfr/launch",
        params,
        (result) => <string>result
    );
};