import * as React from 'react';
import { connect, MapDispatchToProps } from 'react-redux';
import { EmptyObject } from '../misc/common';
import State from '../reducers/state';
import Header from './header/header';
import BrandingWrapper from './branding/branding';
const ToastContainer = require('react-toastify').ToastContainer;
import FooterContainer from '../containers/footer';
import IntlProviderWrapper from './intl-provider-container';
import * as BrandingActions from '../actions/branding';
import { Loading } from './loading/loading';
import { changeLanguage } from '../actions/signin-box';
import { AppService } from '../services/app-service';
import { logErrorWithAlert } from '../misc/error';
import { CmsService } from '../services/cms-service';
import { Dispatch } from 'redux';
import ErrorPage from './error-page/error-page';
import { withRouter } from '../hooks/withRouter';
import { NavigateFunction, Location } from 'react-router-dom';

type ContentWrapperOwnProps = {
    languageId?: number;
    children?: React.ReactNode;
};

/**
 * State props for the Wrapper contentWrapper component
 */
export type ContentWrapperStateProps = {
    children?: React.ReactNode;
    brandedLogoUrl: string;
    brandingLoaded: boolean;
    brandingLoading: boolean;
    cmsLoaded: boolean;
    cmsLoading: boolean;
    cmsError: AppService.ResponseError | undefined;
};

type ContentWrapperDispatchProps = {
    getBranding: (brandingName: string | undefined, isPreview: boolean) => void;
    loadLanguage: (languageId: number | undefined) => void;
};

const mapStateToProps = (state: State.All) => ({
    brandedLogoUrl: state.branding.logoUrl,
    brandingLoaded: state.branding.isLoaded,
    brandingLoading: state.branding.isLoading,
    cmsLoaded: state.signinbox.isLoaded,
    cmsLoading: state.signinbox.fetchingText,
    cmsError: state.signinbox.lastError
});

const mapDispatchToProps: MapDispatchToProps<ContentWrapperDispatchProps, EmptyObject> = (dispatch: Dispatch<any>) => ({
    getBranding: (brandingName: string | undefined, isPreview: boolean) => {
        dispatch(BrandingActions.getBranding(brandingName, isPreview));
    },
    loadLanguage: (languageId: number | undefined) => {
        dispatch(changeLanguage(languageId));
    }
});

type ReactRouterProps = {
    router?: {
        location: Location, 
        navigate: NavigateFunction,
        params: {
            brandingName?: string;
            isPreview?: boolean;
        };
    };
};

type ContentWrapperAllProps = ContentWrapperOwnProps 
    & ContentWrapperStateProps 
    & ContentWrapperDispatchProps 
    & ReactRouterProps;

/**
 * Wrapper around most pages that includes header and footer
 * Ensures that branding and cms content are loaded before displaying
 * any content reliant upon them or the child components
 */
class ContentWrapper extends React.Component<ContentWrapperAllProps, EmptyObject> {
    componentDidMount() {
        this.updateContent();
    }

    componentDidUpdate() {
        this.updateContent();
    }

    render() {
        const { 
            brandingLoaded, 
            cmsLoaded, 
            cmsError,
            children
         } = this.props;

        if (!brandingLoaded) {
            return <Loading />;
        }

        if (!cmsLoaded) {
            if (cmsError) {
                return <ErrorPage />;
            }
            return <Loading />;
        }

        return (
            <div className={CmsService.GetSiteFontStyle()}>
                <IntlProviderWrapper>
                    <div className="App">
                        <Header logoUrl={this.getLogoUrl()}
                                brandName={this.getBrandName()} />
                        <main className="background-large">
                            <div className="hero-image" />
                            <div className="main-center">
                                <div className="panel-body signinpanel">
                                    <BrandingWrapper />
                                        { children }
                                    <ToastContainer />
                                </div>
                            </div>
                        </main>
                        <FooterContainer />
                    </div>
                </IntlProviderWrapper>
            </div>
        );
    }

    private getLogoUrl() {
        return this.props.brandedLogoUrl || '/images/logo-kornferry.svg';
    }

    private getBrandName() {
        const brandingName = this.props.router?.params?.brandingName;
        return brandingName ?? 'Korn Ferry';
    }

    private readonly updateContent = () => {
        const { 
            router, 
            brandingLoaded, 
            brandingLoading, 
            cmsLoading,
            cmsLoaded, 
            cmsError, 
            loadLanguage,
            languageId,
            getBranding
        } = this.props;
        
        const { brandingName, isPreview } = router?.params ?? {};

        if (!brandingLoaded) {
            if (!brandingLoading) {
                getBranding(brandingName, Boolean(isPreview));
            }
        } else if (!cmsLoaded) {
            // Do not attempt to load cms until branding is loaded - it relies upon branding to determine
            // whether to load a themed manifest 
            if (cmsError) {
                logErrorWithAlert('Unable to load language', cmsError.message ? cmsError.message : 'Unable to load language');
            } else if (!cmsLoading) {
                loadLanguage(languageId);
            }
        }
    }
}

export default withRouter(connect(
    mapStateToProps,
    mapDispatchToProps
)(ContentWrapper));