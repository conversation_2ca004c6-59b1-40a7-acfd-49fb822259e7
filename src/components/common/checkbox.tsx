/**
 * Checkbox props
 */
export interface CheckBoxProps {
    isChecked: boolean;
    label: string;
    handleCheckBoxChange: (checked: boolean) => void;
}

/**
 * Generic checkbox component
 */
export function CheckBox (props: CheckBoxProps) {
    return (
      <label>
            <input type="checkbox" value={props.label} checked={props.isChecked} onChange={(event) => props.handleCheckBoxChange(event.target.checked)} />
            {props.label}
      </label>
    );
}