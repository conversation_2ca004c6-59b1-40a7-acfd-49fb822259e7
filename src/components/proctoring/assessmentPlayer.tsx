import IntlProviderContainer from '../../components/intl-provider-container';
import * as Containers from "../redux-containers";
import { AssessmentModel, AssessmentStatus } from '../../models/assessment';
import { getLaunchAssessmentUrl } from '../../services/candidate-service';
import { RouterMetadata } from '../higher-order/with-router';
import { useEffect, useRef, useState } from 'react';
import { logErrorSilent } from '../../misc/error';
import { CmsText } from '../../misc/cms-text';
import { Loading } from '../common/loading/loading';
import TalviewProctoringManager from '../../services/talview-service';
import { PersonModel } from '../../models/candidate';
import { ProctoringModel } from '../../models/proctoring';

/**
 * State props for the CompletedAssessmentsMessage component
 */
export interface AssessmentPlayerState {
    assessments: AssessmentModel[],
    personal: PersonModel,
    proctoring: ProctoringModel,
    text: CmsText
}

export interface AssessmentPlayerProps {
    router: RouterMetadata
}

type AssessmentPlayerAllProps = AssessmentPlayerState & AssessmentPlayerProps;

/**
 * Method to return elements for dashboard page
 */
export const AssessmentPlayer = (props: AssessmentPlayerAllProps): JSX.Element => {
    console.debug(`%c*** Successfully loaded Assessment Player!`, 'color: orange', props);
    
    const { text, router, assessments, personal, proctoring } = props;
    const { assessmentId, languageId } = router.params;
    
    const [ assessmentUrl, setAssessmentUrl ] = useState('');
    const [ loading, setLoading ] = useState(true);
    const [ error, setError ] = useState(false);
    const iframeRef = useRef<HTMLIFrameElement>(null);
    const debug = Boolean(router.queryString.get('debug'));
    const assessment = assessments.find(a => a.assessmentId === Number(assessmentId));

    useEffect(() => {

        const additionalInstructions = text.get(
            'proctoring.preflight.additionalInstructions', 
            'Preflight additional instructions are missing'
        );

        // Flag to prevent participant from taking screen shots or recording the screen.
        const isScreenProtectionEnabled = proctoring?.enforceTSB; 

        const proctoringManager = new TalviewProctoringManager(
            {
                token: assessment?.proctoringToken,
                profileId: personal.candidateId?.toString(),
                assessmentId: assessmentId,
                session: assessment?.scope,
                sessionTitle: assessment?.proctoringSessionTitle ?? assessment?.scope,
                sessionType: 'ai_proctor',
                scriptUrl: window.RUNTIME_ENV?.REACT_APP_Proctoring_Script_URL,
                additionalInstructions,
                enforceTSB: proctoring?.enforceTSB,
                isScreenProtectionEnabled,
                debug
            }
        );

        const launchProctoredAssessment = async (assessmentId?: string, languageId?: string) => {
        
            // Initialize proctoring manager
            await proctoringManager.init();

            // Start proctoring session
            const proctoringSessionId = await proctoringManager.start();

            // Persisting proctoring sessionUuid 
            await proctoringManager.saveProctoringSession(
                { 
                    sessionUuid: proctoringSessionId, 
                    assessmentId: Number(assessmentId),
                    proctoringConfigurationId: assessment?.proctoringConfigurationId ?? null
                }
            );
            console.debug('%c>>> Successfully persisted proctoring sessionUUID', 'color: orange');
            

            //Getting assessment launch url
            const result = await getLaunchAssessmentUrl(Number(assessmentId), Number(languageId));
            console.debug('%c>>> Successfully received assessment launch URL!', 'color: orange');

            if (!result) {
                throw new Error('Unable to launch assessment, no assessment launch details found');
            } else if (result.status === AssessmentStatus.Completed) {
                throw new Error('Unable to launch assessment, assessment complete');
            } else if (!result.launchUrl) {
                throw new Error('Unable to launch assessment, no url found');
            } else {

                setAssessmentUrl(result.launchUrl);
                setLoading(false);
                
                return result;
            }
        }
        
        launchProctoredAssessment(assessmentId, languageId).then(() => {
            console.debug('%c>>> Successfully launched Proctored Assessment!', 'color: orange');
        }).catch(error => {
            const canNotLaunchErrorText = text.get('launchAssessment.canNotLaunchError', 'Cannot launch assessment');
            const errorMessage = `${ canNotLaunchErrorText }: ${ error.message }`;
            logErrorSilent(errorMessage);
            console.error(errorMessage);
            setError(true);
        });
        
        return () => {
            const cleanUp = async () => {
                await proctoringManager.cleanUp();
                window.location.replace("/");
            }
            cleanUp();
        }

    }, [error]);

    return (loading) ? <Loading /> : (
        <IntlProviderContainer>
            <div className="main-layout proctored">
                <iframe
                    id='proctoredAssessmentPlayer'
                    ref={ iframeRef }
                    src={ assessmentUrl }
                    className='proctored-assessment-player'
                />
            </div>
        </IntlProviderContainer>
    );
};

export default Containers.createStateWithProps<AssessmentPlayerState, AssessmentPlayerProps>(AssessmentPlayer, (state, props) => ({
    assessments: state.candidate.assessments!,
    personal: state.candidate.personal!,
    proctoring: state.candidate.proctoring!,
    text: new CmsText(state.language.alltext, 'AssessmentPlayer', 'dashboard'),
    router: props.router
}))
