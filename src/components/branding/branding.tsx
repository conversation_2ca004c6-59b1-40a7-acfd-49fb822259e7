import { connect, MapStateToProps } from 'react-redux';
import { EmptyObject } from '../../misc/common';
import * as  State from '../../reducers/state';
import { Helmet } from 'react-helmet';
import { makeGetUrl } from '../../services/api-service';

/**
 * State props for the branding component
 */

export type BrandingWrapperProps = {
    branding: State.Branding
};
const mapStateToProps: MapStateToProps<BrandingWrapperProps, EmptyObject> = (state: State.All) => ({
    branding: state.branding
});

const BrandingWrapper = (props: BrandingWrapperProps) => {
    const { name, version } = props.branding;
    const isBrandingAvailable = (name ?? '').length > 0 && (version ?? '').length > 0;
    const styleUrl = makeGetUrl('branding/css', { name, version });
    
    return (isBrandingAvailable) ? (
        <Helmet>
            <link rel="stylesheet" href={ styleUrl } />
        </Helmet>
    ) : null
};

export default connect(
    mapStateToProps
)(BrandingWrapper);
