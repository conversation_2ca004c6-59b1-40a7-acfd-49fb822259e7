import * as React from 'react';
import { connect, MapStateToProps } from 'react-redux';
import State from '../../reducers/state';
import { Helmet } from 'react-helmet';
import { AppService } from '../../services/app-service';
import { EmptyObject } from '../../misc/common';

/**
 * State props for the branding component
 */

export type BrandingWrapperProps = {
    branding: State.Branding;
};
const mapStateToProps: MapStateToProps<BrandingWrapperProps, EmptyObject, State.All> = (state: State.All) => ({
    branding: state.branding
});

const BrandingWrapper = (props: BrandingWrapperProps) => {
    const { name, version } = props.branding;
    const isBrandingAvailable = (name ?? '').length > 0 && (version ?? '').length > 0;
    const styleUrl = AppService.makeGetUrl('branding/css', { name, version });

    return (isBrandingAvailable) ? (
        <Helmet>
            <link rel="stylesheet" href={ styleUrl } />
        </Helmet>
    ) : null;
}

export default connect(
    mapStateToProps
)(BrandingWrapper);