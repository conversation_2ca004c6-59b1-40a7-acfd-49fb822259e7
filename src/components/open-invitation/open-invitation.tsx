import React from 'react';
import { EmptyObject } from '../../misc/common';
import { Field, reduxForm, InjectedFormProps, Form } from 'redux-form';
import { renderInputField, PasswordValidator, PasswordValidationComponent } from '../form-field';
import Re<PERSON>aptcha from '../recaptcha/recaptcha';
import { Navigate } from 'react-router';
import { CmsText } from '../../misc/cms-text';
import format from 'string-template';
import { OpenInvitationStage, KeyValuePair } from '../../misc/models';
import { CmsManifestMetaData } from '../../misc/cms-manifest-metadata';
import renderHTML from 'html-react-parser';
import { ThunkAction } from 'redux-thunk';
import { AnyAction } from 'redux';
import State from '../../reducers/state';

const validators = require('redux-form-validators');

/**
 * The route parameters for the parent routing page. Must match what is in App.tsx path.
 * Needs to be used within child components.
 */
export type OpenInvitationRouteProps = {
    /**
     * The self-service settings id
     */
    id: string,
    /**
     * Whether or not the privacy policy has been previously accepted
     */
    ppaccepted: boolean | string,
    /**
     * Any external metadata specified in the querystring
     */
    externalmetadata: KeyValuePair[]
};

/**
 * Names of the form fields (created with <Field>)
 * Must match the 'name' given to the fields
 * This will be available on form submit
 */
export type OpenInvitationFormFields = {
    forename: string,
    middlename: string,
    surname: string,
    email: string,
    confirmEmail: string,
    password: string,
    confirmPassword: string,
    captchaResponse: string,
    privacyPolicyCustomCheckboxAccepted: boolean | null,
    externalMetadata: KeyValuePair[],
    manifestMetaData: CmsManifestMetaData
};

export type OpenInvitationStateProps = {
    hasSettings: boolean;
    isActive: boolean;
    privacyPolicyRequired: boolean;
    privacyPolicyClientId: number;
    showCaptcha: boolean;
    hasSuccessMessage: boolean;
    errorMessage: string;
    registerError: number;
    componentText: any;
    docMetaData: any;
    captchaLanguageCulture: string;
    privacyPolicyCustomCheckboxAccepted: boolean | null;
    stage: OpenInvitationStage;
    email: string;
    isProjectExpired: boolean;
    isParticipantEnteredPassword: boolean;
    passwordValidationText: any;
};

type OpenInvitationInternalState = {
    isCaptchaComplete: boolean;
    isButtonEnabled: boolean;
    isPasswordValid: boolean;
    isEmailValid: boolean;
    showCaptchaError: boolean;
};

export type OpenInvitationProps = 
    OpenInvitationRouteProps 
    & OpenInvitationDispatchProps 
    & OpenInvitationStateProps;

export type OpenInvitationDispatchProps = {
    getSettings(id: string): void;
    submitOpenInvitation(values: OpenInvitationFormFields): ThunkAction<Promise<void>, State.All, unknown, AnyAction>;
    captchaComplete(formName: string, response: string): void;
    formFieldChange(formName: string, fieldName: string, value: any): void;
};


class OpenInvitation extends React.Component<OpenInvitationProps & InjectedFormProps<OpenInvitationFormFields, OpenInvitationProps>, OpenInvitationInternalState> {
    constructor(props: OpenInvitationProps & InjectedFormProps<OpenInvitationFormFields, OpenInvitationProps>) {
        super(props);
        this.state = {
            isCaptchaComplete: !props.showCaptcha,
            isButtonEnabled: false,
            isPasswordValid: false,
            isEmailValid: false,
            showCaptchaError: false
        };
        // Bind the method to this class
        this.handleValidationChange = this.handleValidationChange.bind(this);
        this.handleEmailValidationChange = this.handleEmailValidationChange.bind(this);
        this.captchaComplete = this.captchaComplete.bind(this);
        this.handleCaptchaExpired = this.handleCaptchaExpired.bind(this);
    }
    // Method to handle validation changes
    handleValidationChange(isValid: boolean) {
        this.setState({ isButtonEnabled: isValid });
        this.setState({ isPasswordValid: isValid });
    }

    componentDidMount() {
        this.props.initialize({ email: this.props.email, confirmEmail: this.props.email });

        // Set the values of form fields that are not displayed. This enables values to be available on submit
        this.props.formFieldChange(OpenInvitationFormName, 'privacyPolicyCustomCheckboxAccepted', this.props.privacyPolicyCustomCheckboxAccepted);
        this.props.formFieldChange(OpenInvitationFormName, 'externalMetadata', this.props.externalmetadata);
        this.props.formFieldChange(OpenInvitationFormName, 'manifestMetaData', new CmsManifestMetaData(this.props.docMetaData));
    }

    handleEmailValidationChange(meta: any) {
        const isEmailValid = !meta.error && meta.valid;
        this.setState({ isEmailValid });
    }

    captchaComplete(response: string) {
        this.props.captchaComplete(OpenInvitationFormName, response);
        this.setState({
            isCaptchaComplete: true,
            showCaptchaError: false
        });
    }

    handleCaptchaExpired() {
        this.setState({ isCaptchaComplete: false }); // Update state to mark CAPTCHA as incomplete
    }

    onSubmit = (event: React.FormEvent) => {
        event.preventDefault();
        
        // Check for CAPTCHA if it's required and all other fields are valid
        if (this.props.showCaptcha && !this.state.isCaptchaComplete && this.props.valid) {
            this.setState({ showCaptchaError: true }, () => {
                // Focus on the reCAPTCHA iframe
                const recaptchaFrame = document.querySelector('iframe[title="reCAPTCHA"]') as HTMLIFrameElement;
                if (recaptchaFrame) {
                    recaptchaFrame.focus();
                }
                // Announce error
                const errorContainer = document.getElementById('captcha-error');
                if (errorContainer) {
                    errorContainer.setAttribute('aria-live', 'assertive');
                }
            });
            return;
        }

        this.props.handleSubmit(async (data: OpenInvitationFormFields) => {
            if (!this.props.invalid) {
                await this.props.submitOpenInvitation(data);
            }
            return undefined;
        })(event);
    };

    render() {        
        let cmsText: CmsText = new CmsText(this.props.componentText, 'Open Invitation');
        let passwordValidationCmsText = new CmsText(this.props.passwordValidationText, 'passwordValidation');
        let disableSubmit = this.props.submitting;
        if (this.props.hasSuccessMessage) {
            return (
                <Navigate replace to={`/${this.props.registerError}`} />
            );
        } else {
            return (
                <div className="content">
                    <div className="panel-info">
                        <div className="form-control-static signinpanel-greeting">
                            <h1 className="page-heading signinpanel-greeting-accent">
                                {cmsText.get('personalDetails', 'Personal details')}
                            </h1>
                        </div>
                        <p className="signinpanel-statement">
                            {renderHTML(format(cmsText.get('alreadyRegistered', 'already Registered <a {signInhref}>sign in</a>'), {
                                'signInhref': 'href=\'/\'className=\'signinpanel-statement-link\' aria-describedby="signInLinkAdditionalInfo"'
                            }))}
                        </p>
                        <span id="signInLinkAdditionalInfo" className="signinpanel-no-display">
                        : {cmsText.get('signInAccessibilityInstruction', 'Press Enter to sign in to Assessments')}
                        </span>
                        <p className="signinpanel-statement">
                            {cmsText.get('enterDetails', 'Please enter your name and email address below, then click the save button.')}
                        </p>
                        <div className="panel-border" />
                        <div className="mandatory-note">
                            <span className="mandatory-asterisk">*</span> Mandatory fields
                        </div>
                    </div>
                    <Form 
                        onSubmit={this.onSubmit}
                        className="signin-form" 
                        autoComplete="off"
                        noValidate={true}
                        role="form"
                        aria-label={cmsText.get('personalDetails', 'Personal details')}
                    >
                        {this.props.errorMessage.length > 0 && (
                            <p 
                                id="form-error"
                                className="form-error" 
                                role="alert"
                                tabIndex={-1}
                                aria-live="polite">
                                {this.props.errorMessage}
                            </p>
                        )}
                        {this.state.showCaptchaError && (
                            <p 
                                id="captcha-error"
                                className="form-error" 
                                role="alert"
                                tabIndex={-1}
                                aria-live="polite">
                                {cmsText.get('captchaRequired', 'Please complete the CAPTCHA verification')}
                            </p>
                        )}
                        <Field
                            id="oiForename"
                            className="form-field-input"
                            name="forename"
                            label={cmsText.get('firstName', 'First name') + '*'}
                            placeholder={cmsText.get('firstName', 'First name')}
                            component={renderInputField}
                            type="text"
                            validate={[
                                validators.required({ msg: cmsText.get('firstNameRequired', 'required') })
                            ]}
                            autoComplete="given-name"
                            required={true}
                        />
                        <Field
                            id="oiMiddlename"
                            className="form-field-input"
                            name="middlename"
                            label={cmsText.get('middleName', 'Middle name')}
                            component={renderInputField}
                            type="text"
                            autoComplete="additional-name"
                        />
                        <Field
                            id="oiSurname"
                            className="form-field-input"
                            name="surname"
                            label={cmsText.get('lastName', 'Last name') + '*'}
                            placeholder={cmsText.get('lastName', 'Last name')}
                            component={renderInputField}
                            type="text"
                            validate={[
                                validators.required({ msg: cmsText.get('lastNameRequired', 'required') })
                            ]}
                            autoComplete="family-name"
                            required={true}
                        />
                        <Field
                            id="oiEmail"
                            className="form-field-input"
                            name="email"
                            label={cmsText.get('email', 'Email') + '*'}
                            placeholder={cmsText.get('email', 'Email')}
                            component={renderInputField}
                            type="text"
                            validate={[
                                validators.required({ msg: cmsText.get('emailRequired', 'required') }),
                                validators.email({ msg: cmsText.get('invalidEmail', 'Invalid email address') }),
                                validators.email({ domainBlacklist: ['*.ru', '*.cu', '*.kp', '*.ir', '*.sy'], msg: cmsText.get('embargoedCountriesSanctionsText', 'Unfortunately, due to current sanctions, Korn Ferry cannot provide services to any individuals located in Cuba, Iran, North Korea, Russia, Syria.') })
                            ]}
                            autoComplete="email"
                            onValidationChange={this.handleEmailValidationChange} 
                            required={true}
                            
                        />
                        <Field
                            id="oiConfirmEmail"
                            className="form-field-input"
                            name="confirmEmail"
                            label={cmsText.get('confirmEmail', 'Confirm email') + '*'}
                            placeholder={cmsText.get('confirmEmail', 'Confirm email')}
                            component={renderInputField}
                            type="text"
                            validate={[
                                validators.required({ msg: cmsText.get('confirmEmailRequired', 'required') }), 
                                validators.confirmation({ field: 'email', msg: cmsText.get('emailMismatch', 'Email addresses do not match') })
                            ]}
                            autoComplete="email"
                            disabled={!this.state.isEmailValid} 
                            required={true}
                            
                        />
                        { this.props.isParticipantEnteredPassword && <Field
                                                        id="resetPassword"
                                                        className="form-field-input"
                                                        name="password"
                                                        label={cmsText.get('password', 'Password') + '*'}
                                                        placeholder={cmsText.get('password', 'Password')}
                                                        component={PasswordValidationComponent}
                                                        type="password"
                                                        validate={[
                                                            validators.required({ msg: cmsText.get('passwordRequired', 'Password required') }), 
                                                        ]}
                                                        onValidationChange={this.handleValidationChange} // Pass the callback}
                                                        autoComplete="off"
                                                        componentText={this.props.passwordValidationText}
                                                        showPasswordReuseNote={false} 
                                                        required={true}
                                                    />
                        }
                        { this.props.isParticipantEnteredPassword &&  <Field
                                                        id="resetPasswordRepeated"
                                                        className="form-field-input"
                                                        name="confirmPassword"
                                                        label={cmsText.get('confirmPassword', 'Confirm password') + '*'}
                                                        placeholder={cmsText.get('confirmPassword', 'Confirm password')}
                                                        component={renderInputField}
                                                        type="password"
                                                        validate={[
                                                            validators.required({ msg: cmsText.get('confirmPasswordRequired', 'Confirm Password required') }), 
                                                            validators.confirmation({ field: 'password', msg: cmsText.get('passwordMismatch', 'Passwords do not match') })
                                                        ]}
                                                        autoComplete="off"
                                                        disabled={!this.state.isPasswordValid}
                                                        required={true}
                                                    />
                        }
                        <div className="form-control-static form-field-input">
                            {/* key is per language because we want a *new* component per language, rather than the same updated component. */}
                            { this.props.showCaptcha && <ReCaptcha 
                                                            captchaComplete={this.captchaComplete} 
                                                            languageCulture={this.props.captchaLanguageCulture} 
                                                            key={'recaptcha' + this.props.captchaLanguageCulture} 
                                                            onCaptchaExpired={this.handleCaptchaExpired}
                                                        />
                            }
                        </div>
                        <div className="form-button">
                            <button
                                className="accept" 
                                type="submit"
                                disabled={disableSubmit}
                                aria-label={cmsText.get('save', 'Save')}
                                aria-disabled={disableSubmit}
                                >
                                {cmsText.get('save', 'Save')}
                            </button>
                        </div>
                    </Form>
                </div>
            );
        }
    }
}

export const OpenInvitationFormName = 'openinvitation';

const OpenInvitationForm = reduxForm<OpenInvitationFormFields, OpenInvitationProps>({
    form: OpenInvitationFormName,
    onSubmitFail: (errors, dispatch, submitError, props: any) => {
        // Field mapping for error handling in form
        const fieldOrder = [
            { name: 'forename', inputId: 'oiForename', feedbackId: 'oiForename-feedback' },
            { name: 'surname', inputId: 'oiSurname', feedbackId: 'oiSurname-feedback' },
            { name: 'email', inputId: 'oiEmail', feedbackId: 'oiEmail-feedback' },
            { name: 'confirmEmail', inputId: 'oiConfirmEmail', feedbackId: 'oiConfirmEmail-feedback' },
            { name: 'password', inputId: 'resetPassword', feedbackId: 'resetPassword-feedback' },
            { name: 'confirmPassword', inputId: 'resetPasswordRepeated', feedbackId: 'resetPasswordRepeated-feedback' }
        ];

        // Helper function to focus field and set accessibility attributes
        const focusFieldWithError = (inputId: string, feedbackId: string) => {
            setTimeout(() => {
                const field = document.getElementById(inputId);
                if (field) {
                    field.focus();
                    const errorSpan = document.getElementById(feedbackId);
                    if (errorSpan) {
                        errorSpan.setAttribute('role', 'alert');
                        errorSpan.setAttribute('aria-live', 'assertive');
                    }
                }
            }, 100); // Small delay ensures DOM is ready
        };

        // Check each field in order
        for (const { name, inputId, feedbackId } of fieldOrder) {
            if (errors && name in errors && errors[name as keyof typeof errors]) {
                focusFieldWithError(inputId, feedbackId);
                return;
            }
        }

        // Check CAPTCHA last
        if (props.showCaptcha && !props.form[OpenInvitationFormName]?.values?.captchaResponse) {
            setTimeout(() => {
                const captchaError = document.getElementById('captcha-error');
                if (captchaError) {
                    captchaError.focus();
                    captchaError.setAttribute('role', 'alert');
                    captchaError.setAttribute('aria-live', 'assertive');
                }
            }, 100);
        }
    }
})(OpenInvitation);

export default OpenInvitationForm;