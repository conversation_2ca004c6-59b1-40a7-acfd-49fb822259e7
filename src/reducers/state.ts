import { AssessmentModel } from '../models/assessment';
import { CandidateStateModel, PermissionsModel, PersonModel, CandidateContactInfo } from '../models/candidate';
import { ResponseError } from "../services/api-service";
import { ReportModel } from "../models/report";
import { eLearningModel } from '../models/elearning';
import { ProctoringModel } from '../models/proctoring';

/**
 * Branding object contains css for branding 
 */
export type Branding = {
    /**
     * Specifies if branding is being loaded     
     */
    loadingBrandingUrl: boolean;

    /**
     * Name of the branding
     */
    name: string | undefined;

    /**
     * Branding styles    
     */
    logoUrl: string;

    hasDashboardManifest: boolean;

    /**
     *  Specifies version of the branding
     */
    version: string;
};

/**
 * State related to header of page
 */
export type Header = {
    /**
     * Specifies if the hamburger should be visible    
     */
    showMenu: boolean;
}


/**
 * State for the language selection
 */
export type Language = {
    /**
     * The KFAS language id of the current site, used to retrieve the site text from the CMS service.
     */
    languageId: number;

    /**
     * A culture code version of the languageId, used for react-intl.
     */
    code: string;

    /**
     * All of the site text as a object tree, that matches the structure in the source tree.
     */
    alltext: any,

    /**
     * Flag to indicate text is loaded
     */
    isLoaded: boolean,

    /**
     * Flag to indicate text is being retrieved from the CMS service.
     */
    fetchingText: boolean,

    /**
     * Gets the last error returned from the server if the last request failed.
     */
    lastError?: ResponseError;

    /**
     * Flag to indicate if the language is right to left.
     *     
     */
    isRightToLeft: boolean
};

/**
 * State of candidate reset
 */
export type ResetCandidate = {
    /**
     * Status of candidate reset process    
     */
    resettingCandidate: boolean;

    /**
     * Error message from the server if candidate reset failed.     
     */
    lastError?: ResponseError;
};

/**
 * State for candidate data.
 */
export type Candidate = {
    /**
     * Determines if the current authenticated candidate is loaded from the server yet.
     */
    isLoaded: boolean;

    /**
     * Determines if current authenticated candidate's data is partially refreshing.    
     */
    isRefreshing: boolean;

    /**
     * Gets the last error returned from the server if the last request failed.
     */
    lastError?: ResponseError;

    /**
     * Gets the personal details for this candidate.
     */
    personal?: PersonModel;

    /**
     * Gets the permissions for this candidate.
     */
    permissions?: PermissionsModel;

    /**
     * Gets the current state of this candidate.
     */
    state?: CandidateStateModel;

    /**
     * Gets the list of assessments this candidate will take or has completed.
     */
    assessments?: AssessmentModel[];

    /**
     * Indicates if reports are being prepared on the server but not yet ready to download
     */
    reportsPreparing: boolean;

    /**
     * Gets the list of reports this candidate can download
     */
    reports?: ReportModel[];

    /**
     * Gets the list of eLearning this candidate has access to
     */
    eLearnings?: eLearningModel[];

    /**
     * Gets the candidate contact information.
     */
    candidateContactInfo?: CandidateContactInfo;
    
    /**
     * Specifies if dashboard is being accessed to preview branding styles    
     */
    isPreviewMode: boolean;

    /**
     * Indicates if Success Profile was published or not. The warning message is 
     * displayed on the dashboard instead of available reports if the success 
     * profile is not published.   
     */
    successProfilePublished?: boolean;

    /**
     * Gets the overall proctoring information for the candidate, 
     * whether to launch the assessment in a secure browser (TSB).
     */
    proctoring?: ProctoringModel;
}

export type Session = {
    /**
     * Show the session timeout warning dialog
     */
    showTimeoutWarning: boolean;

    /**
     * The session timeout period in minutes
     */
    timeoutMinutes: number | null;

    /**
     * The time the session expires
     */
    expiry: Date | null;
}

export type Demographics = {
    /**
     * Show the reuse demographics dialog
     */
    showReuseDemographicsDialog: boolean;
}


export type RedirectWarning = {
    /**
     * Show the reuse redirect warning dialog
     */
    showRedirectWarningModal: boolean;
    onRedirectConfirmButtonClick?: () => void | null;
}

export type AssessmentReuse = {
        /**
     * Specifies if show reuse assessment dialog is currently displayed
     */
    showReuseAssessmentDialog: boolean;

    /**
     * Assessment currently being offered for reuse by the dialog
     */
    assessment: AssessmentModel | null;
}

export type DynamicFeedback = {
    /**
     * Show the dynamic feedback overlay
     */
    showDynamicFeedbackOverlay: boolean;
    /**
     * Dynamic feedback launch URL
     */
    dynamicFeedbackLink?: string;
}

/**
 * State related to Proctoring
 */
export type Proctoring = {
    /**
     * Specifies if the proctoring is enabled 
     */
    enabled?: boolean;
}

export type DataCollectionDisclosureWarning = {
    /**
     * Show the data collection disclosure warning dialog
     */
    showDataCollectionDisclosureWarningModal: boolean;
    onProceedButtonClick?: () => void;
}

export type UnsupportedDeviceWarning = {
    /**
     * Show the unsupported device warning dialog
     */
    showUnsupportedDeviceWarningModal: boolean;
}

/**
 * Main state to be used with redux, see app.ts for how this is bound to the reducers.
 */
export type All = {
    candidate: Candidate, 
    branding: Branding,
    language: Language,
    resetCandidate: ResetCandidate,
    assessmentReuse: AssessmentReuse,
    header: Header,
    session: Session,
    demographics: Demographics,
    redirectWarning: RedirectWarning,
    dynamicFeedback: DynamicFeedback,
    proctoring: Proctoring,
    dataCollectionDisclosureWarning: DataCollectionDisclosureWarning,
    unsupportedDeviceWarning: UnsupportedDeviceWarning
};