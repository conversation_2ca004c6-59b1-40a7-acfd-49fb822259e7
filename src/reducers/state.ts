import * as Models from '../misc/models';
import { LanguageModel } from '../misc/models';
import { AppService } from "../services/app-service";

namespace State {
    /**
     * State for the signinbox
     */
    export type SigninBox = {
        language: LanguageModel,
        alltext: any,
        isLoaded: boolean,
        fetchingText: boolean,
        lastError?: AppService.ResponseError,
    };

    export type Signin = {
        errorReason: Models.ErrorReason,
        errorMessage: string,
        acceptedPrivacyPolicy: boolean,
        privacyPolicyClientId: number,
        signInSuccess: boolean,
        redirectUrl: string,
        privacyPolicyToken: string | null,
        userName: string,
    };

    export type Branding = {
        loadingBrandingUrl: boolean,
        logoUrl: string,
        name: string | undefined,
        hasSignInManifest: boolean,
        isLoaded: boolean,
        isLoading: boolean,
        isPreview: boolean,
        version: string
    };

    export type PasswordResetRequest = {
        result: string
    };

    export type PasswordReset = {
        username: string;
        token: string;
        error: Models.ErrorReason;
        errorMessage: string;
        /**
         * Whether or not the token has been validated
         */
        tokenValidated: boolean;
        /**
         * Whether or not the token is valid
         */
        tokenIsValid: boolean;
        /**
         * Whether or not the actual password reset was a success
         */
        isSuccess: boolean;
    };

    export type OpenInvitation = {
        /**
         * Whether or not settngs have been retrieved
         */
        hasSettings: boolean;
        isActive: boolean;
        privacyPolicyRequired: boolean;
        privacyPolicyClintId: number;
        showCaptcha: boolean;
        /**
         * Id as used in the url
         */
        id: string;
        /**
         * If there is a success message, this should be shown on the login page (using the error)
         */
        hasSuccessMessage: boolean;
        errorMessage: string;
        error: Models.ErrorReason;
        captchaLanguageCulture: string;
        stage: Models.OpenInvitationStage,
        /**
         * A preset email address. Only used if the user has been asked for email address previously (i.e. Sso email gate)
         */
        email: string;
        isProjectExpired: boolean;
        isParticipantEnteredPassword: boolean;
    };

    export type PrivacyPolicy = {
        hasCheckedForCustomPolicy: boolean,
        customPrivacyHeader: string,
        customPrivacyStatement: string
        customCheckboxAccepted: boolean | null,
        customCheckboxStatement: string | null,
        customTransferringDataStatement: string | null,
        customAcceptButtonStatement: string | null,
        customCancelButtonStatement: string | null,
        hasError: boolean
    };

    export type PrivacyPolicyResponse = {
        customCheckboxAccepted: boolean | null;
        hasError: boolean;
    };

    export type LoginLinkExpired = {
        sendedSuccessful: boolean,
        alltext: any
    };

    /**
     * State for the whole signin app
     */
    export type All = {
        signinbox: SigninBox,
        signin: Signin,
        passwordresetrequest: PasswordResetRequest,
        passwordreset: PasswordReset,
        openinvitation: OpenInvitation,
        branding: Branding,
        privacypolicy: PrivacyPolicy,
        loginLinkExpired: LoginLinkExpired,
        form: any
    };
}

export default State;