import State from './state';
import * as BrandingActions from '../actions/branding';

/**
 * The default initial state
 */
export const BrandingInitialState: State.Branding = {
    loadingBrandingUrl: false,
    logoUrl: '',
    name: undefined,
    hasSignInManifest: false,
    isPreview: false,
    isLoaded: false,
    isLoading: false,
    version: ''
};

/**
 * Reducer for Branding
 */
const BrandingReducer = (state: State.Branding = BrandingInitialState, action: BrandingActions.Actions): State.Branding => {
    switch (action.type) {
        case BrandingActions.GetBrandingStartType:
            return {
                ...state,
                loadingBrandingUrl: true,
                isLoading: true,
                isLoaded: false
            };
        case BrandingActions.GetBrandingFinishType:
            return {
                ...state,
                loadingBrandingUrl: false,
                logoUrl: action.payload.logoUrl,
                name: action.payload.name,
                hasSignInManifest: action.payload.hasSignInManifest,
                isLoaded: true,
                isLoading: false,
                version: action.payload.version
            };
        case BrandingActions.SetPreviewModeType:
            return {
                ...state,
                isPreview: Boolean(action.payload)
            };
        default:
            return state;
    }
};

export default BrandingReducer;