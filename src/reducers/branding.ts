import * as State from './state';
import * as Actions from '../actions/branding';

/**
 * The default initial state
 */
export const BrandingInitialState: State.Branding = {
    loadingBrandingUrl: false,
    logoUrl: '',
    name: undefined,
    hasDashboardManifest: false,
    version: ''
};

/**
 * Reducer for Branding
 */
export const BrandingReducer = (state: State.Branding = BrandingInitialState, action: Actions.BrandingActions): State.Branding => {
    if (action.type === Actions.GetBrandingFinishType) {
        return {
            ...state,
            loadingBrandingUrl: false,
            logoUrl: action.payload.logoUrl,
            name: action.payload.name,
            hasDashboardManifest: action.payload.hasDashboardManifest,
            version: action.payload.version
        };
    }
    return state;
};
