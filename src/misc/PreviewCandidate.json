{"personal": {"firstName": "preview", "lastName": "candidate", "displayName": "preview candidate", "id": 11}, "permissions": {"canResetAll": false, "canResetAssessments": false, "viewCandidateReport": false}, "state": {"languageId": 1, "showGPP": true, "acceptedGPP": true, "showDemographics": true, "completedDemographics": true, "completedBestPractice": true, "isInBlendedProject": true}, "assessments": [{"assessmentId": 111, "type": 3, "subTestId": 0, "status": 1, "isEnabled": true, "isLocked": false, "languageId": 0, "completed": null, "needsMouse": true, "needsNotepad": false, "needsCalculator": false, "needsSound": false, "timeLengthMinutes": 30, "timeExtraMinutes": 0, "compatability": 1, "supportedLanguageIds": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]}, {"assessmentId": 112, "type": 2, "subTestId": 0, "status": -1, "isEnabled": true, "isLocked": false, "languageId": 0, "completed": null, "needsMouse": true, "needsNotepad": true, "needsCalculator": true, "needsSound": false, "timeLengthMinutes": 16, "timeExtraMinutes": 1, "compatability": 1, "supportedLanguageIds": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]}, {"assessmentId": 113, "type": 5, "subTestId": 0, "status": 0, "isEnabled": true, "isLocked": false, "languageId": 0, "completed": null, "needsMouse": true, "needsNotepad": false, "needsCalculator": false, "needsSound": false, "timeLengthMinutes": 16, "timeExtraMinutes": 1, "compatability": 1, "supportedLanguageIds": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]}, {"assessmentId": 115, "type": 10, "subTestId": 0, "status": -1, "isEnabled": true, "isLocked": false, "languageId": 0, "completed": null, "needsMouse": true, "needsNotepad": false, "needsCalculator": false, "needsSound": false, "timeLengthMinutes": 15, "timeExtraMinutes": 0, "compatability": 1, "supportedLanguageIds": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]}], "reports": [{"projectId": 110621, "reportType": "Development-configurable", "date": "2020-12-08T11:46:38.26", "name": "Development report"}, {"projectId": 112635, "reportType": "ParticipantFeedback-configurable", "date": "2020-12-08T09:49:50.58", "name": "Participant Feedback report"}], "reportsPreparing": false, "candidateContactInfo": {"contactName": "support", "contactNumber": "12345678", "contactEmail": "<EMAIL>", "hasInfo": true}, "brandingStyles": {"name": "", "logoUrl": "", "styles": "", "version": "", "hasSignInManifest": true, "hasDashboardManifest": true}}