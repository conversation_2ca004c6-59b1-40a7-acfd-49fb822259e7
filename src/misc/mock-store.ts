import { BrandingInitialState } from '../reducers/branding';
import { initialResetCandidate } from '../reducers/reset-candidate';
import { headerInitial } from '../reducers/header';

import configureMockStore from 'redux-mock-store';
import thunk from 'redux-thunk';
import * as State from "../reducers/state";


export let allTextFake: any = require('../__tests__/data/text.json');
let siteText = allTextFake.cmsText;
siteText.allLanguages = allTextFake.allLanguages;

let language: State.Language = {
  languageId: 1,
  code:"en-GB",
  alltext: siteText,
  fetchingText: false,
  isRightToLeft: false,
  isLoaded: true
};

 const middlewares = [thunk];
 const mockStore = configureMockStore<State.All>(middlewares);
  export const store = mockStore({
    candidate: {
      isLoaded: false
    },  
    branding: BrandingInitialState,
    language:  language,
    resetCandidate: initialResetCandidate,
    header:headerInitial
  } as State.All);
  