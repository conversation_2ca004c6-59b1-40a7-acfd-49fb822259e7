import * as logger from './logger';

import { toast } from 'react-toastify';

/**
 * General error logger and message display to the user.
 * @param userMessage Message to display to the user
 * @param logMessage Message to store in the error logs
 * @param exception exception details.
 */
export const logErrorWithAlert = (userMessage: string, logMessage: string, exception?: any) => {
    logError(logMessage, exception);
    toast.error(userMessage, toastErrorOptions);
};

/**
 * Options object for toaster error alert see.https://www.npmjs.com/package/react-toastify#demo
 */
const toastErrorOptions = {
    autoClose: 2000,
    type: toast.TYPE.INFO,
    hideProgressBar: false,
    position: toast.POSITION.TOP_RIGHT
};

/**
 * General error logger and display alert on browser window (using toastify plugin)
 * @param logMessage Message to store in the error logs
 * @param exception exception details.
 */
export const logError = (logMessage: string, exception?: any) => {
    toast.error('There was an error please contact your administrator, more details are available in the error log', toastErrorOptions);
    logger.logError({ logMessage, exception });
};

/**
 * Method to log errors with redux.see redux-catch for more info.
 * @param error The error message
 * @param getState Method to get current state 
 * @param lastAction Action that triggered error
 * @param dispatch Handle to dispatch, can be used to dispatch an action due to the error using the dispatch parameter 
 */
export const logReduxError = (error: any, getState: any, lastAction: any, dispatch: any) => {
    logger.logFatal('fatal error redux', { error, lastAction });
    toast.error('System Error');
};

/**
 * General error logger without alerting the user.
 * @param logMessage Message to store in the error logs
 * @param exception exception details.
 */
export const logErrorSilent = (logMessage: string, exception?: any) => {
    logger.logError({ logMessage, exception });
};