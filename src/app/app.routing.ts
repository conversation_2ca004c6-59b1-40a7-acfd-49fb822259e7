import { RouterModule, Routes } from '@angular/router';
import {
  BrandingAssetsComponent,
  CandidateDetailsComponent,
  CandidateLogComponent,
  CandidateManageAssessmentsComponent,
  CandidateReportStatusComponent,
  ClientCandidatesComponent,
  ClientProjectsComponent,
  ClientSearchComponent,
  DataExtractsUploadComponent,
  LoginComponent,
  NewReportComponent,
  PortalBrandingComponent,
  ProjectCandidatesComponent,
  ProjectDetailsComponent,
  ReportBrandingComponent,
  ReportListComponent,
  ReportManagementComponent,
  ReportPreviewDownloadComponent,
  UsersComponent,
  ClientNormsComponent,
  UsageReportComponent,
  ClientCustomisationsComponent,
  ClientCustomProjectTypesComponent,
  SystemLogComponent
} from './components/pages';
import { ClientDetailsComponent } from './components/pages/clients/client-details/client-details.component';
import { NormDetailsComponent } from './components/pages/clients/norm-details/norm-details.component';
import { ProjectEmailSchedulesComponent } from './components/pages/clients/project-email-schedules/project-email-schedules.component';
import { AuthGuard, SessionGuard } from './shared/guards';
import { ProjectGuard } from './shared/guards/project.guard';
import { AuthRolesList, AuthRole } from './shared/models';
import {
  AssessmentsResolver,
  ClientParticipantsSearchResolver,
  ClientProjectsResolver,
  ClientResolver,
  NewReportOptionsResolver,
  NotificationHeadersResolver,
  ParamsResolver,
  ParticipantDetailsResolver,
  ParticipantsSearchResolver,
  PortalBrandingResolver,
  ProjectReportsResolver,
  ProjectResolver,
  ProjectSearchMetadataResolver,
  ReportBrandingResolver,
  ReportPreviewResolver,
  ReportResolver,
  ReportsResolver,
  ReportStatusResolver,
  UploadAssetsResolver,
  CustomProjectTypeResolver
} from './shared/resolvers';
import { NormResolver } from './shared/resolvers/norm.resolver';
import { ProjectEmailSchedulesResolver } from './shared/resolvers/project-email-schedules.resolver';
import { CustomProjectTypeDetailsComponent } from './components/pages/clients/custom-project-type-details/custom-project-type-details.component';
import { ClientTilesComponent } from './components/pages/clients/client-tiles/client-tiles.component';
import { StakeholderResolver } from './shared/resolvers/sps-stakeholders.resolver';
import { SpsCollaborationComponent } from './components/pages/clients/sps-collaboration/sps-collaboration.component';
import { SpsCollaborationDetailsComponent } from './components/pages/clients/sps-collaboration-details/sps-collaboration-details.component';
import { SpsCollaborationDetailsResolver } from './shared/resolvers/sps-collaboration-details.resolver';
import { SpsStakeholderLogComponent } from './components/pages/clients/sps-stakeholder-log/sps-stakeholder-log.component';
import { SpsCollaborationStakeholdersComponent } from './components/pages/clients/sps-collaboration-stakeholders/sps-collaboration-stakeholders.component';
import { SpsStakeholderDetailsComponent } from './components/pages/clients/sps-stakeholder-details/sps-stakeholder-details.component';

const appRoutes: Routes = [
  {
    path: 'login',
    data: {
      title: 'Login',
    },
    component: LoginComponent,
  },
    {
    path: 'system-log',
    component: SystemLogComponent,
    canActivate: [AuthGuard, SessionGuard],
    data: {
      allowedRoles: ['admin','systemAdmin'] as AuthRole[],
      title: 'System',
      displayName: 'System',
    },
  },
  {
    path: 'users',
    component: UsersComponent,
    canActivate: [AuthGuard, SessionGuard],
    data: {
      allowedRoles: ['admin'] as AuthRole[],
      title: 'Registered Users',
    },
  },
  {
    path: 'usage-report-all-clients',
    component: UsageReportComponent,
    canActivate: [AuthGuard, SessionGuard],
    data: {
      allowedRoles: ['admin', 'usageReporting'] as AuthRole[],
      title: 'Usage Report (All Clients)',
    },
    resolve: {
      client: ClientResolver,
    },
  },
  {
    path: 'reports',
    component: ReportListComponent,
    canActivate: [AuthGuard, SessionGuard],
    data: {
      allowedRoles: ['admin', 'reportManagement'] as AuthRole[],
      title: 'All reports',
      displayName: 'All reports',
    },
    resolve: {
      reports: ReportsResolver,
    },
    children: [
      {
        path: 'new',
        component: NewReportComponent,
        canActivate: [AuthGuard, SessionGuard],
        data: {
          allowedRoles: ['admin', 'reportManagement'] as AuthRole[],
          title: 'New report',
          displayName: 'New report',
        },
        resolve: {
          newReportOptions: NewReportOptionsResolver,
        },
      },
      {
        path: ':reportId/edit',
        component: NewReportComponent,
        canActivate: [AuthGuard, SessionGuard],
        data: {
          allowedRoles: ['admin', 'reportManagement'] as AuthRole[],
          title: 'Edit report',
          displayName: 'Edit report',
        },
        resolve: {
          report: ReportResolver,
          newReportOptions: NewReportOptionsResolver,
        },
      },
    ],
  },

  {
    path: 'client-tiles',
    component: ClientTilesComponent,
    canActivate: [AuthGuard, SessionGuard],
    data: {
      allowedRoles: AuthRolesList,
      title: 'Client Search',
    },
    resolve: {
      client: ClientResolver,
    }
  },
  {
    path: 'clients',
    component: ClientSearchComponent,
    canActivate: [AuthGuard, SessionGuard],
    data: {
      allowedRoles: AuthRolesList,
      title: 'Client Search',
    },
    children: [
      {
        path: 'client-details',
        component: ClientDetailsComponent,
        canActivate: [AuthGuard, SessionGuard],
        data: {
          allowedRoles: AuthRolesList,
          title: 'Client Details',
        },
        resolve: {
          client: ClientResolver,
          notifications: NotificationHeadersResolver,
        },
      },
      {
        path: 'client-projects',
        component: ClientProjectsComponent,
        canActivate: [AuthGuard, SessionGuard],
        data: {
          allowedRoles: ['admin', 'productDelivery', 'projectParticipantManagement', 'dataScientist', 'participantExtract'] as AuthRole[],
          title: 'Client Projects',
        },
        resolve: {
          client: ClientResolver,
          projectSearchMetadata: ProjectSearchMetadataResolver,
        },
      },
      {
        path: 'project-candidates',
        component: ProjectCandidatesComponent,
        canActivate: [AuthGuard, SessionGuard],
        data: {
          allowedRoles: ['admin', 'productDelivery', 'projectParticipantManagement'] as AuthRole[],
          title: 'Project Participants',
        },
        resolve: {
          client: ClientResolver,
          project: ProjectResolver,
          searchMetadata: ParticipantsSearchResolver,
          params: ParamsResolver,
        },
      },
      {
        path: 'project-email-schedules',
        component: ProjectEmailSchedulesComponent,
        canActivate: [AuthGuard, SessionGuard],
        data: {
          allowedRoles: ['admin', 'productDelivery', 'projectParticipantManagement'] as AuthRole[],
          title: 'Project Email Schedules',
        },
        resolve: {
          client: ClientResolver,
          project: ProjectResolver,
          schedules: ProjectEmailSchedulesResolver,
        },
      },
      {
        path: 'project-details',
        component: ProjectDetailsComponent,
        canActivate: [AuthGuard, SessionGuard],
        data: {
          allowedRoles: ['admin', 'productDelivery', 'projectParticipantManagement'] as AuthRole[],
          title: 'Project Details',
        },
        resolve: {
          client: ClientResolver,
          project: ProjectResolver,
        },
      },
      {
        path: 'report-management',
        component: ReportManagementComponent,
        canActivate: [AuthGuard, SessionGuard],
        resolve: {
          client: ClientResolver,
        },
        data: {
          allowedRoles: ['admin', 'reportManagement'] as AuthRole[],
          title: 'Report Management',
        },
      },
      {
        path: 'report-branding',
        component: ReportBrandingComponent,
        canActivate: [AuthGuard, SessionGuard],
        data: {
          allowedRoles: ['admin', 'productDelivery', 'reportManagement'] as AuthRole[],
          title: 'Report Branding',
        },
        resolve: {
          client: ClientResolver,
          branding: ReportBrandingResolver,
          assets: UploadAssetsResolver,
        },
      },
      {
        path: 'portal-branding',
        component: PortalBrandingComponent,
        canActivate: [AuthGuard, SessionGuard],
        data: {
          allowedRoles: ['admin', 'productDelivery', 'reportManagement'] as AuthRole[],
          title: 'Participant Portal Branding',
        },
        resolve: {
          client: ClientResolver,
          branding: PortalBrandingResolver,
          assets: UploadAssetsResolver,
        },
      },
      {
        path: 'branding-assets',
        component: BrandingAssetsComponent,
        canActivate: [AuthGuard, SessionGuard],
        resolve: {
          client: ClientResolver,
          assets: UploadAssetsResolver,
        },
        data: {
          allowedRoles: ['admin', 'productDelivery', 'reportManagement'] as AuthRole[],
          title: 'Branding Assets',
          displayName: 'Assets',
        },
      },
      {
        path: 'report-preview-download',
        component: ReportPreviewDownloadComponent,
        canActivate: [AuthGuard, SessionGuard],
        resolve: {
          client: ClientResolver,
          reportPreviewOptions: ReportPreviewResolver,
        },
        data: {
          allowedRoles: ['admin', 'reportManagement'] as AuthRole[],
          title: 'Report Preview',
          displayName: 'Preview',
        },
      },
      {
        path: 'data-extracts-upload',
        component: DataExtractsUploadComponent,
        canActivate: [AuthGuard, SessionGuard, ProjectGuard],
        data: {
          allowedRoles: ['admin', 'productDelivery', 'participantExtract', 'dataScientist'] as AuthRole[],
          title: 'Participant Data Extract',
        },
        resolve: {
          client: ClientResolver,
          project: ProjectResolver,
          searchMetadata: ParticipantsSearchResolver,
        },
      },
      {
        path: 'candidate-log',
        component: CandidateLogComponent,
        canActivate: [AuthGuard, SessionGuard],
        data: {
          allowedRoles: ['admin', 'productDelivery', 'projectParticipantManagement'] as AuthRole[],
          title: 'Participant Logs',
        },
        resolve: {
          client: ClientResolver,
          participant: ParticipantDetailsResolver,
        },
      },
      {
        path: 'candidate-manage-assessments',
        component: CandidateManageAssessmentsComponent,
        canActivate: [AuthGuard, SessionGuard],
        data: {
          allowedRoles: ['admin', 'productDelivery', 'projectParticipantManagement'] as AuthRole[],
          title: 'Manage Assessments',
        },
        resolve: {
          client: ClientResolver,
          project: ProjectResolver,
          participant: ParticipantDetailsResolver,
          assessments: AssessmentsResolver,
        },
      },
      {
        path: 'client-candidates',
        component: ClientCandidatesComponent,
        canActivate: [AuthGuard, SessionGuard],
        data: {
          allowedRoles: ['admin', 'productDelivery', 'projectParticipantManagement'] as AuthRole[],
          title: 'Client Participants',
        },
        resolve: {
          client: ClientResolver,
          metadata: ClientParticipantsSearchResolver,
        },
      },
      {
        path: 'candidate-details',
        component: CandidateDetailsComponent,
        canActivate: [AuthGuard, SessionGuard],
        data: {
          allowedRoles: ['admin', 'productDelivery', 'projectParticipantManagement'] as AuthRole[],
          title: 'Participant Details',
        },
        resolve: {
          client: ClientResolver,
          candidate: ParticipantDetailsResolver,
        },
      },
      {
        path: 'candidate-report-status',
        component: CandidateReportStatusComponent,
        canActivate: [AuthGuard, SessionGuard],
        data: {
          allowedRoles: ['admin', 'productDelivery', 'projectParticipantManagement'] as AuthRole[],
          title: 'Report Status',
        },
        resolve: {
          reportStatus: ReportStatusResolver,
          availableReports: ProjectReportsResolver,
          project: ProjectResolver,
          params: ParamsResolver,
          client: ClientResolver,
          candidate: ParticipantDetailsResolver,
        },
      },
      {
        path: 'usage-report',
        component: UsageReportComponent,
        canActivate: [AuthGuard, SessionGuard],
        data: {
          allowedRoles: ['admin', 'usageReporting'] as AuthRole[],
          title: 'Usage Report',
        },
        resolve: {
          client: ClientResolver,
        },
      },
      {
        path: 'client-norms',
        component: ClientNormsComponent,
        canActivate: [AuthGuard, SessionGuard],
        data: {
          allowedRoles: ['admin', 'productDelivery'] as AuthRole[],
          title: 'Custom norms',
          displayName: 'Custom norms'
        },
        resolve: {
          client: ClientResolver,
        },
      },
      {
        path: 'norm-details',
        component: NormDetailsComponent,
        canActivate: [AuthGuard, SessionGuard],
        data: {
          allowedRoles: ['admin', 'productDelivery'] as AuthRole[],
          title: 'Norm details',
        },
        resolve: {
          client: ClientResolver,
          norm: NormResolver
        },
      },
      {
        path: 'client-customisations',
        component: ClientCustomisationsComponent,
        canActivate: [AuthGuard, SessionGuard],
        data: {
          allowedRoles: ['admin', 'reportManagement', 'usageReporting', 'productDelivery', 'projectParticipantManagement', 'dataScientist', 'participantExtract'] as AuthRole[],
          title: 'Client Customisations',
        },
        resolve: {
          client: ClientResolver
        },
      },
      {
        path: 'client-custom-project-types',
        component: ClientCustomProjectTypesComponent,
        canActivate: [AuthGuard, SessionGuard],
        data: {
          allowedRoles: ['admin', 'reportManagement', 'usageReporting', 'productDelivery', 'projectParticipantManagement', 'dataScientist', 'participantExtract'] as AuthRole[],
          title: 'Custom Project Types',
        },
        resolve: {
          client: ClientResolver
        },
      },
      {
        path: 'custom-project-type-details',
        component: CustomProjectTypeDetailsComponent,
        canActivate: [AuthGuard, SessionGuard],
        data: {
          allowedRoles: ['admin', 'reportManagement', 'usageReporting', 'productDelivery', 'projectParticipantManagement', 'dataScientist', 'participantExtract'] as AuthRole[],
          title: 'Custom Project Types',
        },
        resolve: {
          client: ClientResolver,
          projectType: CustomProjectTypeResolver
        },

      },
      {
        path: 'sps-collaboration-list',
        component: SpsCollaborationComponent,
        canActivate: [AuthGuard, SessionGuard],
        data: {
          title: 'COLLABORATION',
        }
      },
      {
        path: 'sps-collaboration-details',
        component: SpsCollaborationDetailsComponent,
        canActivate: [AuthGuard, SessionGuard],
        data: {
          title: 'COLLABORATION DETAILS',
        },
        resolve: {
         collaboration: SpsCollaborationDetailsResolver,
        },
      },
      {
        path: 'sps-stakeholder-log',
        component: SpsStakeholderLogComponent,
        canActivate: [AuthGuard, SessionGuard],
        data: {
          title: 'STAKEHOLDER LOG',
        }
      },
      {
        path: 'sps-stakeholder-details',
        component: SpsStakeholderDetailsComponent,
        canActivate: [AuthGuard, SessionGuard],
        data: {
          title: 'STAKEHOLDER DETAILS',
        },
        resolve: {
          stakeholders: StakeholderResolver,
        },

      },
      {
        path: 'sps-collaboration-stakeholder',
        component: SpsCollaborationStakeholdersComponent,
        canActivate: [AuthGuard, SessionGuard],
        data: {
          title: 'COLLABORATION STAKEHOLDERS',
        },
      }
    ],
  },
  // otherwise redirect to home
  { path: '**', redirectTo: '/client-tiles' },
];

export const routing = RouterModule.forRoot(appRoutes);
