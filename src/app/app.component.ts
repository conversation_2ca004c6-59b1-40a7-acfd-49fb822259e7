﻿import { Component } from '@angular/core';
import {
  Event as RouterEvent,
  NavigationCancel,
  NavigationEnd,
  NavigationError,
  NavigationStart,
  ResolveEnd,
  Router,
} from '@angular/router';
import {
  AuthenticationService,
  PageHeaderService,
  SpinnerService,
} from './services';
import { AuthRole, AuthRoles, AuthRolesList, getAliasForAuthRole, getDisplayNameForAuthRole, getDisplayOrderForAuthRole, PageHeaderSubtitle } from './shared/models';
import { User } from './shared/models/user';
import { SharedService } from './shared/services/shared.service';

@Component({
  selector: 'app',
  templateUrl: 'app.component.html',
  styleUrls: ['app.component.scss'],
})
export class AppComponent {
  loading = false;
  snapshot: any;
  currentUser: User;
  headerNavLinks = [
    //need to remove admmin role from System for testing added
    {
      label: "System",
      routerLink: "/system-log",
      allowedRoles: ['admin','systemAdmin'] as AuthRole[],
      tooltip: "System",
    },
    {
      icon: "home",
      label: "Home",
      routerLink: "/client-tiles",
      allowedRoles: AuthRolesList,
      tooltip: "Home",
    },
    {
      icon: "people",
      label: "Users",
      routerLink: "/users",
      allowedRoles: ['admin'] as AuthRole[],
      tooltip: "Manage users registered on Support Portal",
    },
    {
      icon: "assignment",
      label: "Reports",
      routerLink: "/reports",
      allowedRoles: ['admin', 'reportManagement'] as AuthRole[],
      tooltip: "Manage reports existing in the system for all clients (Create, View Details or Edit)",
    },
    {
      icon: "business",
      label: "Usage Report",
      routerLink: "/usage-report-all-clients",
      allowedRoles: ['admin', 'usageReporting'] as AuthRole[],
      tooltip: "Request the Usage Report (for all clients)",
    },
  ];
  personTooltip: string;

  constructor(
    private router: Router,
    private spinnerService: SpinnerService,
    private pageHeaderService: PageHeaderService,
    private authenticationService: AuthenticationService, private sharedService: SharedService
  ) {
    router.events.subscribe((event) => this.navigationInterceptor(event));
    this.sharedService.$currentUser.subscribe((user = new User()) => {
      this.currentUser = user;

      if (user && user.roles) {
        const roles = user.roles.sort((a, b) => getDisplayOrderForAuthRole(getAliasForAuthRole(a)) - getDisplayOrderForAuthRole(getAliasForAuthRole(b)))
        const rolelist = roles.map(getAliasForAuthRole).map(getDisplayNameForAuthRole).join("\n  • ");

        this.personTooltip = roles.length > 1 ? `You are participant of next groups: \n  • ${rolelist}` : `You are participant of '${rolelist}' group` ;
      }
    })
  }

  // shows and hides the loading spinner during RouterEvent changes
  private navigationInterceptor(event: RouterEvent): void {
    if (event instanceof NavigationStart) {
      this.spinnerService.activate();
    }
    if (event instanceof ResolveEnd) {
      this.snapshot = event.state.root;
    }
    if (
      event instanceof NavigationEnd ||
      event instanceof NavigationCancel ||
      event instanceof NavigationError
    ) {
      this.spinnerService.deactivate();
      this.updatePageHeaderTitle(this.snapshot);
    }
  }

  subscribeRouterEvents() {
    this.router.events.subscribe((event) => {});
  }

  updatePageHeaderTitle(routeSnapshot) {
    if (!routeSnapshot) {
      const defaultTitle = 'SupportPortal';
      this.pageHeaderService.setTitle(defaultTitle);
      return;
    }

    while (routeSnapshot.firstChild) {
      routeSnapshot = routeSnapshot.firstChild;
    }

    const subtitles = this.extractSubtitlesFromRouteData(routeSnapshot.data);
    const showBrandingNavigation = routeSnapshot.routeConfig.path.indexOf(
      'branding'
    ) >= 0;
    this.pageHeaderService.setTitle(routeSnapshot.data.title, subtitles, showBrandingNavigation);
  }

  extractSubtitlesFromRouteData(routeData) {
    return {
      client: routeData.client && {
        name: routeData.client.name,
        id: routeData.client.id,
      },
      project: routeData.project && {
        name: routeData.project.name,
        id: routeData.project.projectId,
        productType: routeData.project.productType === 'ASSESS' ? 'TM' : 'TA',
        projectType: routeData.project.projectType,
        showAruLabel: routeData.project.allowAssessmentReuse,
      },
      candidate: (routeData.candidate || routeData.participant) && {
        name: (routeData.candidate || routeData.participant).displayName,
        id: (routeData.candidate || routeData.participant).participantId,
      },
    } as PageHeaderSubtitle;
  }

  get showRibbon() {
    return (
      !this.router.url.includes('/login') &&
      !this.router.url.includes('/register') &&
      !this.router.url.includes('/forgotPassword') &&
      !this.router.url.includes('/updatePassword')
    );
  }

  get showNavigation() {
    return (
      !this.router.url.includes('/home') &&
      !this.router.url.includes('/login') &&
      !this.router.url.includes('/register') &&
      !this.router.url.includes('/forgotPassword') &&
      !this.router.url.includes('/updatePassword')
    );
  }

  logout() {
    this.authenticationService.logout();
    this.router.navigate(['/login']);
  }
}
