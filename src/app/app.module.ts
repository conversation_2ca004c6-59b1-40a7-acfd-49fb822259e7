﻿import { DragDropModule } from '@angular/cdk/drag-drop';
import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

import { ColorPickerModule } from 'ngx-color-picker';
import { AppComponent } from './app.component';
import { AppMaterialModule } from './app.material.module';
import { routing } from './app.routing';
import { ControlsModule } from './components/controls/controls.module';
import { DialogsModule } from './components/dialogs/dialogs.module';
import { PagesModule } from './components/pages/pages.module';
import { ErrorInterceptor } from './shared/interceptors/error.interceptor';
import { JwtInterceptor } from './shared/interceptors/jwt.interceptor';
import {
  AssessmentsResolver,
  ClientParticipantsSearchResolver,
  ClientProjectsResolver,
  ClientResolver,
  NewReportOptionsResolver,
  NotificationHeadersResolver,
  ParamsResolver,
  ParticipantDetailsResolver,
  ParticipantResolver,
  ParticipantsSearchResolver,
  PortalBrandingResolver,
  ProjectReportsResolver,
  ProjectResolver,
  ProjectSearchMetadataResolver,
  ReportBrandingResolver,
  ReportPreviewResolver,
  ReportResolver,
  ReportsResolver,
  ReportStatusResolver,
  UploadAssetsResolver,
  ProjectEmailSchedulesResolver,
  NormResolver,
  CustomProjectTypeResolver
} from './shared/resolvers';
import { SharedModule } from './shared/shared.module';
import { SpsCollaborationDetailsResolver } from './shared/resolvers/sps-collaboration-details.resolver';
import { StakeholderResolver } from './shared/resolvers/sps-stakeholders.resolver';

@NgModule({
  imports: [
    BrowserModule,
    DragDropModule,
    ReactiveFormsModule,
    FormsModule,
    HttpClientModule,
    routing,
    BrowserAnimationsModule,
    AppMaterialModule,
    ColorPickerModule,
    SharedModule,
    PagesModule,
    DialogsModule,
    ControlsModule,
  ],
  declarations: [AppComponent],
  entryComponents: [],
  providers: [
    AssessmentsResolver,
    ClientParticipantsSearchResolver,
    ClientProjectsResolver,
    ClientResolver,
    NewReportOptionsResolver,
    NotificationHeadersResolver,
    ParamsResolver,
    ParticipantDetailsResolver,
    ParticipantResolver,
    ParticipantsSearchResolver,
    PortalBrandingResolver,
    ProjectReportsResolver,
    ProjectEmailSchedulesResolver,
    ProjectResolver,
    NormResolver,
    ProjectSearchMetadataResolver,
    ReportBrandingResolver,
    ReportPreviewResolver,
    ReportResolver,
    ReportsResolver,
    ReportStatusResolver,
    UploadAssetsResolver,
    CustomProjectTypeResolver,
    SpsCollaborationDetailsResolver,
    StakeholderResolver,
    { provide: HTTP_INTERCEPTORS, useClass: JwtInterceptor, multi: true },
    { provide: HTTP_INTERCEPTORS, useClass: ErrorInterceptor, multi: true },
    [],
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}
