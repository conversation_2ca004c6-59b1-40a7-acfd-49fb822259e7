﻿<div class="flex-container">
  <mat-toolbar-row class="header" *ngIf="showRibbon">
    <div class="header-content">
      <img src="assets/images/logo.svg" alt="Kornferry" />
      <button
        *ngFor="let navLink of headerNavLinks"
        mat-icon-button
        class="hover-blue"
        routerLinkActive="active"
        [matTooltip]="navLink.tooltip"
        [routerLink]="navLink.routerLink"
        [allowedRoles]="navLink.allowedRoles"
        [disableRipple]="true"
        [routerLinkActiveOptions]="{ exact: true }"
      >
        <mat-icon>{{ navLink.icon }}</mat-icon> {{ navLink.label }}
      </button>
      <div class="user-panel grey">
        <button mat-icon-button [disableRipple]="true" [matTooltip]="personTooltip">
          <mat-icon>person</mat-icon>
          {{ currentUser?.username }}
        </button>
      </div>
      <button
        mat-icon-button
        class="hover-red"
        [disableRipple]="true"
        (click)="logout()"
        matTooltip="Logout"
      >
        <mat-icon>logout</mat-icon>
      </button>
    </div>
  </mat-toolbar-row>

  <router-outlet></router-outlet>
  <kf-loading></kf-loading>
  <kf-footer></kf-footer>
</div>
