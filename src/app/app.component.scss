@import "typography";

:host {
  background-color: #fff;

  .flex-container {
    background-attachment: fixed;
    background-size: cover;
    background-image: url("assets/images/home/<USER>");
  }

  mat-toolbar-row.header {
    height: 42px;

    .header-content {
      display: flex;
      flex-direction: row;
      justify-content: stretch;
      align-items: center;
      padding: 0 12px;

      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      height: 42px;
      background-color: #fff;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      z-index: 1000;

      .mat-icon-button {
        width: auto;
        height: auto;

        line-height: 9px;
        font-size: 9px;
        font-weight: bold;
        font-style: normal;
        font-stretch: normal;
        letter-spacing: 1.2px;
        text-transform: uppercase;
        border-radius: 0;
        padding: 6px;

        mat-icon {
          font-size: 1.2rem;
        }

        &:not(:last-child) {
          padding: 12px;
        }

        &:hover {
          opacity: 1;
        }

        &.hover-red:hover {
          mat-icon {
            background-color: rgba($secondary--red, .7);
            border-radius: 3px;
            color: white;
          }
        }

        &.hover-blue:hover, &.hover-blue.active {
          color: $primary--blue;
          mat-icon {
            color: $primary--blue;
            opacity: .7;
          }
        }
      }

      .user-panel {

        .mat-icon-button {
          border-left: solid thin rgba(0, 0, 0, 0.1);
          padding: 12px 0 12px 12px;
          text-transform: none;
          line-height: 0.75rem;
          letter-spacing: 0.6px;
          font-size: 0.75rem;
          font-weight: 100;
        }
      }

      img {
        margin-right: auto;
      }

      mat-icon {
        color: rgba(0, 0, 0, 0.6);
      }
    }
  }
}

::ng-deep .mat-tooltip  {
  white-space: pre-line !important;
}
