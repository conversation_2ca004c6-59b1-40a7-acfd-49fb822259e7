import { FilterOverrideRequest, ReportFilter } from '@/shared/models';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';

/**
 * Class to handle report filters.
 */
@Injectable({
  providedIn: 'root',
})
export class ReportFilterService {
  /**
   *Creates an instance of ReportFilterService.
   */
  constructor(private apiService: ApiService) {}

  /**
   * Delete a report filter.
   * @param filter report filter to be deleted.
   */
  deleteFilter(filterId: number): Observable<object> {
    return this.apiService.delete<number>('reportFilter', filterId);
  }

  /**
   * Adds a report filter.
   * @param filter report filter to be deleted.
   */
  AddFilter(filter: ReportFilter): Observable<number> {
    return this.apiService.postWithBody<ReportFilter, number>(
      'reportFilter',
      (result) => result as number,
      filter
    );
  }

  /**
   * Updates a report filter.
   * @param filter report filter to be deleted.
   */
  UpdateFilter(filter: ReportFilter): Observable<number> {
    return this.apiService.put<ReportFilter, number>(
      'reportFilter',
      (result) => result as number,
      filter
    );
  }

  /**
   * Method to override filters that were inherited from parent client.
   * @param filterOverrideRequest  the override request.
   */
  OverideInheritedFilters(
    filterOverrideRequest: FilterOverrideRequest
  ): Observable<object> {
    return this.apiService.postWithBody<FilterOverrideRequest, object>(
      'reportFiltersOveride',
      (json) => json,
      filterOverrideRequest
    );
  }
}
