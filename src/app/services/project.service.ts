import { SampleEmailSchedules } from "@/shared/mocks/SampleEmailSchedules";
import {
  ClientProjects,
  ParticipantMetadata,
  ProjectDetails,
  SearchOptionData,
  GetNormResponse,
  RegionalNorm,
  ProjectAssessments,
  ProjectUpdateRequestModel,
  ProjectLocation,
  ProjectAssessmentCustomNorm,
} from '@/shared/models';
import { ProjectEmailSchedule } from "@/shared/models/ProjectEmailSchedule";
import { SearchProjectParticipantsData, CandidateReportLog } from '@/shared/models/projects/participants';
import { Injectable } from '@angular/core';
import { throwToolbarMixedModesError } from '@angular/material';
import { Observable, ReplaySubject, of } from 'rxjs';
import { catchError } from "rxjs/operators";
import { AlertService } from "./alert.service";
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root',
})
export class ProjectService {
  private projectsSubject = new ReplaySubject<ClientProjects>();

  constructor(private apiService: ApiService, private alertService: AlertService) {}

  /**
   * Observable that emit when ClientPro<PERSON> has been loaded.
   */
  projectsUpdated: Observable<any> = this.projectsSubject.asObservable();

  /**
   * Method to search for clients based on passed in name.
   * @param clientId Id of the client
   * @returns  Observable<ClientProjects>
   */
  getProjects(
    clientId: number,
    filters: any = {},
    searchTerm = '',
    pageIndex?:number, 
    pageSize?:number,
    sortColumn: string = '',
    sortBy: string = ''
   ): Observable<any> {
    const filterBy = Object.keys(filters).join('|');
    const filterValues = Object.keys(filters)
      .map((key) => filters[key].join(';'))
      .join('|');
    const query = this.apiService.get(
      `projectSearch/${clientId}?KFASClientId=${clientId}&SearchString=${searchTerm}&clientId=${clientId}&FilterBy=${filterBy}&FilterValues=${filterValues}&pageIndex=${pageIndex}&pageSize=${pageSize}&sortColumn=${sortColumn}&sortBy=${sortBy}`,
      (json) => json
    );

    return query;
  }

  /**
   * Method to get project details
   * @param clientId Id of the client
   * @param projectId Id of the project
   * @returns  Observable<ClientProjects>
   */
  getProjectDetails(
    clientId: number,
    projectId: number
  ): Observable<ProjectDetails> {
    // TODO API is not ready yet, so returning project summary
    return this.apiService.get<ProjectDetails>(
      `projectDetails/${projectId}`,
      (json) => <ProjectDetails>json,
      null
    );
  }

  /**
   * Method to get all available norms and locations
   */
   getNorms(projectId:number) {
    return this.apiService.get<GetNormResponse>(
      `projectnorm/locationsandnorms?projectId=${projectId}`,
      (json) => <GetNormResponse>json,
      null
    );
  }

  /**
   * Method to get new drivers and/or traits for a new norm
   */
  calculateNewAssessmentsForNewNorm(projectId: number, successProfileId: number, newNorm: RegionalNorm){
    const body = {
      projectId,
      successProfileId,
      newNorm
    };
    return this.apiService.postWithBody<{}, ProjectAssessments>(
      `projectnorm/calculatenewassessments`,
      (json) => <ProjectAssessments>json,
      body
    );
  }

  /**
   * Method to update project's regional norm and/or location
   */
  updateRegionalData(projectId: number,
    newNorm?: RegionalNorm,
    newLocation?: ProjectLocation){

    const model = new ProjectUpdateRequestModel(projectId, newNorm, newLocation, null);
    return this.update(model);
  }

  /**
   * Method to update norm for an assessment
   */
  updateAssessmentNorm(projectId: number, newAssessmentCustomNorm?: ProjectAssessmentCustomNorm){
    const model = { projectId, newAssessmentCustomNorm } as ProjectUpdateRequestModel;
    return this.update(model);
  }

  update(model: ProjectUpdateRequestModel){
    return this.apiService.postWithBody<{}, any>(
      `projectDetails/${model.projectId}`,
      (json) => json,
      model
    );
  }

  getProjectSearchMetadata(clientId: number): Observable<any> {
    return this.apiService.get<ClientProjects>(
      `projectSearchMetadata/${clientId}`,
      (json) => <ClientProjects>json,
      null
    );
  }

  /**
   * Method to get metadata for participants search.
   * @returns  Observable<ParticipantMetadata>
   */
  getParticipantMetadata(): Observable<ParticipantMetadata> {
    return this.apiService.get<ParticipantMetadata>(
      `project/participantSearchMetaData`,
      (json) => <ParticipantMetadata>json,
      null
    );
  }

  /**
   * Method to regenerate reports for single or many participants.
   * @param projectId Project ID
   * @param participantIds Participant ID or IDs for whom reports should be regenerated
   */
  regenerateAllReports(
    projectId: number,
    participantIds: number[],
    languageIds: number[],
    regenerateAll: boolean
  ) {
    const body = {
      projectId,
      participantIds,
      languageIds,
      regenerateAll
    };
    return this.apiService.postWithBody<{}, string>(
      `project/reports/regenerate`,
      (json) => <string>json,
      body
    );
  }

  /**
   * Method to regenerate reports for single or many participants.
   * @param projectId Project ID
   * @param participantIds Participant ID or IDs for whom reports should be regenerated
   */
  regenerateReports(
    projectId: number,
    participantId: number,
    reportKeys: string[],
    languageIds: number[]
  ) {
    const body = {
      projectId,
      participantId,
      reportKeys,
      languageIds,
    };
    return this.apiService.postWithBody<{}, string>(
      `participant/reports/regenerate`,
      (json) => <string>json,
      body
    );
  }

  /**
   * Method to get participants (lazy) with search/sorting/filtering/paging options.
   * @param projectId the project id
   * @param searchOptions options for background search/sort/filtering/paging
   * @returns Observable<SearchProjectParticipantsData>
   */
  getParticipants(
    projectId: number,
    searchOptions: SearchOptionData
  ): Observable<SearchProjectParticipantsData> {
    const queryMap = new Map<string, string>();
    for (const prop of Object.keys(searchOptions)) {
      queryMap.set(prop, searchOptions[prop]);
    }
    return this.apiService.get(
      `project/participantSearch/${projectId}`,
      (json) => <SearchProjectParticipantsData>json,
      queryMap
    );
  }

  getEmailSchedules(projectId: number): Observable<ProjectEmailSchedule[]> {
    return this.apiService.get(
      `emailsetting/project/${projectId}`,
      (json) => <ProjectEmailSchedule[]>json
    );
  }

  getReportStatus(participantId, projectId) {
    return this.apiService.get(
      `participant/reports/Status/${participantId}?projectId=${projectId}`,
      (json) => <SearchProjectParticipantsData>json,
      null
    );
  }

  getParticipantReportLogs(participantId, projectId, reportKey){
    return this.apiService.get(
      `participant/log/report?candidateUserId=${participantId}&projectId=${projectId}&reportKey=${reportKey}`,
      (json) => <CandidateReportLog[]>json,
      null
    );
  }

  getProjectReports(projectId: number) {
    return this.apiService.get(
      `project/reports?projectId=${projectId}`,
      (json) => json,
      null
    );
  }

  getNotificationTemplate(id: any, clientId: any) {
    return this.apiService.get<any>(
      `notification/details?clientId=${clientId}&TemplateId=${id}`,
      (json) => <any>json,
      null
    );
  }

  getEmailLog(userId: any, startDate: Date, endDate: Date) {
    `emails/log?userId=${userId}&startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`;
    return this.apiService.get<any>(
      `emails/log?userId=${userId}&startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`,
      (json) => <any>json,
      null
    );
  }

  getCandidateLog(CandidateUserId: any, startDate: Date, endDate: Date) {
    return this.apiService.get<any>(
      `participant/log?candidateUserId=${CandidateUserId}&startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`,
      (json) => <any>json,
      null
    );
  }

  getCandidateJobs(CandidateUserId: any, startDate: Date, endDate: Date) {
    return this.apiService.get<any>(
      `participant/jobs?candidateUserId=${CandidateUserId}&startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`,
      (json) => <any>json,
      null
    );
  }

  getCandidateJobLogs(CandidateUserId: any, jobId: any) {
    return this.apiService.get<any>(
      `participant/joblogs?candidateUserId=${CandidateUserId}&jobId=${jobId}`,
      (json) => <any>json,
      null
    );
  }

  getCandidateJobDetails(CandidateUserId: any, jobId: any) {
    return this.apiService.get<any>(
      `participant/job/details?candidateUserId=${CandidateUserId}&jobId=${jobId}`,
      (json) => <any>json,
      null
    );
  }

  getCandidateLogDetails(CandidateUserId: any, logId: any, date: any) {
    return this.apiService.get<any>(
      `participant/log/details?candidateUserId=${CandidateUserId}&logId=${logId}&logDate=${date}`,
      (json) => <any>json,
      null
    );
  }

  retryCandidateJob(jobId: number, requestorUserId: number) {
    return this.apiService.postWithBodyRaw<any, any>(
      `participant/jobs/retry`,
      (json) => <any>json,
      { JobId: jobId, RequestorUserId: requestorUserId }
    );
  }

  sendExtractRequest(request: any) {
    return this.apiService.postWithBodyWithFileResponse(
      `Extracts/request`,
      request
    );
  }

  downloadReport(pdfLink: string, fileName: string): Observable<Blob> {
    const url = `participant/reports/download?downloadLink=${pdfLink.replace(/\\/g, '/')}&fileName=${fileName}`;
    return this.apiService.getWithFileResponse(url);
  }

  deleteProject(projectId: number): Observable<any> {
    return this.apiService.delete('projectDelete', projectId);
  }

  getSystemLogs(params: any) {
    const query = this.apiService.buildRelativeUrlWithParams('system/log', params);
    return this.apiService.get(query, (json) => json);
  }

  getSystemLogDetails(params: { logId: string; logDate: string }) {
    const query = `logId=${params.logId}&logDate=${encodeURIComponent(params.logDate)}`;
    return this.apiService.get<any>(`/system/log/details?${query}`, (json) => json);
  }
}
