import { PageHeaderSubtitle } from '@/shared/models';
import { Injectable } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class PageHeaderService {
  title = new BehaviorSubject('No header');
  subtitles = new BehaviorSubject(new PageHeaderSubtitle());
  showBrandingNavigation = new BehaviorSubject(false);
  spsTitle = new BehaviorSubject('No header');
  spsSubTitle = new BehaviorSubject('');
  constructor(public titleService: Title) {}

  setTitle(title: string, subtitles = new PageHeaderSubtitle(), showBrandingNavigation = false) {
    this.title.next(title);
    this.subtitles.next(subtitles);
    this.showBrandingNavigation.next(showBrandingNavigation);

    this.titleService.setTitle(
      [
        'SupportPortal',
        title,
        subtitles && subtitles.candidate && subtitles.candidate.name,
        subtitles && subtitles.project && subtitles.project.name,
        subtitles && subtitles.client && subtitles.client.name,
      ]
        .filter((item) => item)
        .join(' - ')
    );
  }

  setSPSDetails(title:string, subtitle?:string){
    this.spsTitle.next(title);
    this.spsSubTitle.next(subtitle);
  }
}
