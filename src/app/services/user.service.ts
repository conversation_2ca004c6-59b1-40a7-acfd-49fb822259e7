﻿import {
  UpdatePasswordRequest,
  UserRegisterRequest,
} from '@/shared/models';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';

/**
 * Service to handle site user.
 */
@Injectable({ providedIn: 'root' })
export class UserService {
  constructor(private apiService: ApiService) {}

  /**
   * Get all the registered users for admin site.
   */
  getAll(paginationToken, searchKey) {
    const options = new Map<string, string>();
    if (paginationToken) {
      options.set('paginationToken', paginationToken.replaceAll(/\+/g, '%2B'));
    } else if (searchKey) {
      options.set('searchKey', searchKey);
    }
    return this.apiService.get<any>(
      `users`,
      (json) => json,
      options
    );
  }

  /**
   * Confirm user account
   * @param userName User name to be confirmed
   */
  confirmUser(userName: string) {
    return this.apiService.postUrlFormEncoded<any, any>(
      `user/confirmUser?username=${userName}`,
      (json) => json,
      null
    );
  }

  /**
   * Get all user groups
   */
  getAllGroups() {
    return this.apiService.get<string[]>(
      `user/allGroups`,
      (json) => <string[]>json,
      null
    );
  }

  /**
   * Get groups applied for single user
   * @param userName Username which required to get groups
   */
  getUserGroups(userName: string) {
    return this.apiService.get<string[]>(
      `user/groups?username=${userName}`,
      (json) => <string[]>json,
      null
    );
  }

  /**
   * Add single user to group
   * @param UserName Username which will be added to group
   * @param GroupName Group name
   */
  addUserToGroup(UserName: string, GroupName: string) {
    return this.apiService.postUrlFormEncoded<any, string>(
      `user/addToGroup`,
      (json) => <string>json,
      {
        GroupName,
        UserName,
      }
    );
  }

  /**
   * Remove user from group
   * @param UserName Username which will be removed from group
   * @param GroupName Group name
   */
  removeUserFromGroup(UserName: string, GroupName: string) {
    return this.apiService.postUrlFormEncoded<any, string>(
      `user/removeFromGroup`,
      (json) => <string>json,
      {
        GroupName,
        UserName,
      }
    );
  }

  /**
   * Registers a user.
   * @param user The user to be registered.
   */
  register(user: UserRegisterRequest) {
    return this.apiService.postUrlFormEncoded<UserRegisterRequest, string>(
      'User',
      (json) => <string>json,
      user
    );
  }

  /**
   * Send reset password request.
   * @param userName The user for whom password needs to be reset.
   */
  resetPassword(userName: string): Observable<string> {
    return this.apiService.get<string>(
      `user/resetPassword?username=${userName}`,
      (json) => json as string,
      null
    );
  }

  /**
   * Updates a user Password.
   * @param updatePasswordRequest  The password update request object.
   */
  updatePassword(
    updatePasswordRequest: UpdatePasswordRequest
  ): Observable<string> {
    return this.apiService.postWithBody<UpdatePasswordRequest, string>(
      'user/updatePassword',
      (json) => json as string,
      updatePasswordRequest
    );
  }
}
