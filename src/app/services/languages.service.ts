import { ALL_LANGUAGES } from '@/shared/mocks/languages.data';
import { Injectable } from '@angular/core';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root',
})
export class LanguagesService {
  readonly LANGUAGES_STORAGE_ITEM = 'languages';

  constructor(private apiService: ApiService) {
    this.retrieveLanguages();
  }

  getLanguages() {
    const cached = localStorage.getItem(this.LANGUAGES_STORAGE_ITEM);
    const cachedLangs = JSON.parse(cached);
    return cachedLangs && (cachedLangs.length >= ALL_LANGUAGES.length) ? cachedLangs : ALL_LANGUAGES;
  }

  getLanguageByLocale(locale) {
    return (
      this.getLanguages().find((x: any) => x.locale.toLowerCase() === locale.toLowerCase()) ||
      this.getLanguages().find((x: any) => x.locale.toLowerCase() === locale.split('-')[0].toLowerCase())
    );
  }

  getLanguageNameByLocale(locale) {
    const lang = this.getLanguageByLocale(locale);
    return lang ? lang.name : locale;
  }

  retrieveLanguages() {
    const url = `languages`;
    this.apiService
      .get(url, (langs) => langs)
      .subscribe((langs) => {
        localStorage.setItem(
          this.LANGUAGES_STORAGE_ITEM,
          JSON.stringify(langs)
        );
        console.log('languages have been retrieved', langs);
      });
  }
}
