import { NewReportOptions } from '@/shared/models/reports/newReportOptions';
import { Report } from '@/shared/models/reports/report';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root',
})
export class ReportService {
  /**
   * Creates an instance of newReportOptions.
   * @param  apiService to handle http calls.
   */
  constructor(private apiService: ApiService) {}

  /**
   * Method to get options required for report creation.
   * @returns  Observable<ReportPreviewOptions>
   */
  getOptions(): Observable<NewReportOptions> {
    return this.apiService.get<NewReportOptions>(
      `/report/reportOptions`,
      (json) => <NewReportOptions>json,
      null
    );
  }

  getAllReports(): Observable<Report[]> {
    return this.apiService.get<Report[]>(
      `/report/getAllReports`,
      (json) => <Report[]>json,
      null
    );
  }

  createOrUpdateReport(data: any): Observable<any> {
    if (!data.reportId) {
      delete data.reportId;
      return this.apiService.put('report', (x) => x, data);
    }
    return this.apiService.postWithBody('report', (x) => x, data);
  }
}
