
import { Injectable } from "@angular/core";
import { ApiService } from './api.service';
import { Observable } from "rxjs";
import { CollaborationDetails } from "@/shared/models/sps-client/collaboration-details";

@Injectable({
  providedIn: "root",
})
export class SpsCollaborationDetailsService {
  constructor(private apiService: ApiService) {}

  getCollaborationDetails(clientId:any , collabId:any) : Observable<CollaborationDetails>{
    return this.apiService.getSPS(`getcollaborationdetailsbycollabid?collabId=${collabId}&clientId=${clientId}`)
  }



}