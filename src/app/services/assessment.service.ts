import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class AssessmentService {
  private readonly traits = {
    FO: 'Focus',
    PE: 'Persistence',
    CR: 'Credibility',
    NA: 'Need for Achievement',
    SO: 'Sociability',
    TR: 'Trust',
    HU: 'Humility',
    CP: 'Composure',
    EM: 'Empathy',
    OP: 'Optimism',
    OD: 'Openness to Differences',
    AF: 'Affiliation',
    CU: 'Curiosity',
    CF: 'Confidence',
    AD: 'Adaptability',
    SS: 'Situational Self-Awareness',
    TA: 'Tolerance of Ambiguity',
    IN: 'Influence',
    AS: 'Assertiveness',
    RI: 'Risk-Taking',
  };

  private readonly drivers = {
    STRC: 'Structure',
    CHAL: 'Challenge',
    COLL: 'Collaboration',
    BALA: 'Balance',
    POWR: 'Power',
    INDY: 'Independence',
  };

  private readonly behavioralCompetencies = {
    SDV: 'Self-Development',
    NNE: 'Builds Networks',
    MCO: 'Manages Conflict',
    DQU: 'Decision Quality',
    IPS: 'Interpersonal Savvy',
    NLE: 'Nimble Learning',
    CFO: 'Customer Focus',
    OWP: 'Optimizes Work Processes',
    BRE: 'Being Resilient',
    RSF: 'Resourcefulness',
    VDI: 'Values Differences',
    BST: 'Balances Stakeholders',
    COM: 'Communicates Effectively',
    COL: 'Collaborates',
    ITR: 'Instills Trust',
    EAC: 'Ensures Accountability',
    AEX: 'Plans and Aligns',
    SAD: 'Situational Adaptability',
    ACO: 'Action Oriented',
    DRE: 'Drives Results',
    BET: 'Builds Effective Teams',
    EIN: 'Drives Engagement',
    DWO: 'Directs Work',
    DVP: 'Drives Vision and Purpose',
    GPE: 'Global Perspective',
    CIN: 'Cultivates Innovation',
    SVI: 'Strategic Mindset',
    DTA: 'Develops Talent',
    ORS: 'Organizational Savvy',
    PER: 'Persuades',
    ATT: 'Attracts Top Talent',
    MAB: 'Manages Ambiguity',
    COU: 'Courage',
    MCX: 'Manages Complexity',
  };

  constructor() {}

  getNameOfTraitsByCode(code) {
    return this.traits[code] || code;
  }

  getNameOfDriversByCode(code) {
    return this.drivers[code] || code;
  }

  getNameOfCompetenciesByCode(code) {
    return this.behavioralCompetencies[code] || code;
  }
}
