import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import { StakeholderLogResponse } from '@/shared/models/sps-client/stakeholder-log';

@Injectable({
  providedIn: 'root'
})
export class SpsStakeholderLogService {

  constructor(private apiService: ApiService) { }

  getStakeholderLogData(clientId: string,pageIndex?:number, pageSize?:number): Observable<StakeholderLogResponse> {
    return this.apiService.getSPS(`getallstakeholderbyclientid?clientId=${clientId}&pageIndex=${pageIndex}&pageSize=${pageSize}`)
  }
}  