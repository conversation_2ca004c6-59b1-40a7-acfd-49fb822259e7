﻿import { environment } from "@/shared/environments/environment";
import { User, UserPermissions, Auth<PERSON>ole, AuthRolesList, getAliasForAuthRole } from "@/shared/models";
import { HttpHeaders } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { JwtHelperService } from "@auth0/angular-jwt";
import { BehaviorSubject, Observable } from "rxjs";
import { map, mergeMap, tap } from "rxjs/operators";
import { ApiService } from "./api.service";
import { SharedService } from "@/shared/services/shared.service";

/**
 * User authentication service.
 */
@Injectable({ providedIn: "root" })
export class AuthenticationService {

  constructor(private apiService: ApiService, private sharedService: SharedService) {
  }

  /**
   * Current signed-in user.
   */
  public get currentUser(): User {
    return this.sharedService.$currentUser.value;
  }

  signIn(username: string, password: string): Observable<User> {
    const headers = new HttpHeaders();
    headers.append("Content-Type", "application/x-www-form-urlencoded");

    const urlSearchParams = new URLSearchParams();
    urlSearchParams.set("grant_type", "password");
    urlSearchParams.set("username", username);
    urlSearchParams.set("password", password);

    const body = urlSearchParams.toString();
    return this.apiService.postWithBody<string, User>(
      environment.ADMIN_API_TOKEN_URL,
      this.convertor,
      body
    );
  }

  getPolicies(): Observable<UserPermissions> {
    return this.apiService.get<UserPermissions>(
      "user/policies",
      (json) => <UserPermissions>json
    );
  }

  /**
   * Method to log a user.
   * @param username userName to use
   * @param password user password.
   */
  login(username: string, password: string): Observable<User> {
    return this.signIn(username, password).pipe(
      mergeMap((user) => {
        user.username = username;
        this.storeUser(user);

        return this.getPolicies();
      }),
      map((policies) => {
        this.currentUser.roles = policies.roles;
        this.storeUser(this.currentUser);

        return this.currentUser;
      })
    );
  }

  /**
   * Method to check if user has a specific role
   */
  hasRole(authRole: AuthRole) {
    try {
      return this.currentUser.roles.map(getAliasForAuthRole).indexOf(authRole) >= 0;
    } catch {
      return false;
    }
  }

  isInAllowedRoles(allowedRoles: AuthRole[] = AuthRolesList): boolean {
    return allowedRoles.some((role) => this.hasRole(role));
  }

  get projectsAllowedOnlyForExtract(): boolean {
    return (
      this.isInAllowedRoles(['dataScientist', 'participantExtract'] as AuthRole[]) &&
      !this.isInAllowedRoles(['admin', 'prodcutDelivery', 'projectParticipantManagement'] as AuthRole[])
    );
  }

  get canRequestPersonalData(): boolean {
    const personalDataRoles = ['admin', 'productDelivery', 'dataScientist'] as AuthRole[];
    return this.isInAllowedRoles(personalDataRoles);
  }

  /**
   * Removes a user from local storage and sets current user subject to null.
   */
  logout() {
    // remove user from local storage to log user out
    localStorage.removeItem("currentUser");
    localStorage.removeItem("expires");
    localStorage.removeItem("languages");
    localStorage.removeItem("ucpApiAccessToken");
    localStorage.removeItem("selectedClient");
    localStorage.removeItem("stakeholderDetailsData");
    localStorage.removeItem("spsCollabDetails");
    this.sharedService.$currentUser.next(null);
    
  }

  getSessionRemainingTime() {
    const expires = parseInt(localStorage.getItem("expires"), 10);

    // remaining time before logout (in ms)
    return expires * 1000;
  }

  /**
   * Saves a user to local storage .
   * @param user  The current signed in user.
   */
  storeUser(user: User) {
    localStorage.setItem("expires", user.expires.toString());
    localStorage.setItem("currentUser", JSON.stringify(user));
    this.sharedService.$currentUser.next(user);
  }

  /**
   * Converts the access token result to user object.
   */
  private convertor = (result: any): User => {
    const helper = new JwtHelperService();
    if (result && result.access_token) {
      const decodeUser = helper.decodeToken(result.access_token);
      const mappedUser = new User();
      mappedUser.access_token = result.access_token;
      mappedUser.firstName = decodeUser.name;
      mappedUser.lastName = decodeUser.family_name;
      mappedUser.token = result.access_token;
      mappedUser.expires = result.expires_in;
      mappedUser.username = decodeUser.given_name;
      return mappedUser;
    }
  };
}
