// The front-cover preview text data
export const report = {
  title: 'Participant development report',
  subTitle: 'Pat sample',
  solutionName: 'Solution Name',
  info: [
    {
      name: 'Company',
      value: 'HayGroup',
    },
    {
      name: 'Success profile',
      value: 'Account Manager Commercial Banking I',
    },
    {
      name: 'Assessed',
      value: 'Apr 30, 2018',
    },
    {
      name: 'Created',
      value: 'May 10, 2018',
    },
  ],
  textInputs: ['Interviewed By', 'Interviewed Date'],
};

// The options for cover image position
export const bgPositionOptions = [
  {
    label: 'Upper 1/3',
    value: 'UpperThird',
  },
  {
    label: 'Lower 1/3',
    value: 'LowerThird',
  },
  {
    label: 'Lower 2/3',
    value: 'LowerTwoThirds',
  },
  {
    label: 'Full Page',
    value: 'FullPage',
  },
  {
    label: 'No Image',
    value: 'NoImage',
  },
];

// The options for client logo position
export const logoPositionOptions = [
  {
    label: 'Right',
    value: 'Right',
  },
  {
    label: 'Left',
    value: 'Left',
  },
];

export const assets = [
  {
    fileName: 'dark.jpg',
    fileUrl: 'assets/images/<EMAIL>',
  },
  {
    fileName: 'logo-fonterra.png',
    fileUrl: 'assets/images/logo-fonterra.png',
  },
];

export const response = {
  id: 0,
  clientId: 11434,
  name: 'AMG Test-ipdated',
  created: '2018-11-01T11:47:38.047',
  createdBy: 2985138,
  enabled: true,
  settings: {
    coverImagePath: 'assets/images/logo-fonterra.png',
    clientLogoPath: 'assets/images/logo-fonterra.png',
    coverImageFormat: 'LowerTwoThirds',
    clientLogoPosition: 'Right',
    coBranded: true,
  },
};

export const options = {
  // Enabled: indicates whether custom branding is enabled or not.
  enabled: true,

  // Name: the name of this branding set to help describe what it is for.
  name: 'test branding',

  // coverImageFormat:  Cover image position
  coverImageFormat: 'LowerTwoThirds',

  // CoverImagePath : Absolute url of cover image.
  coverImagePath: '',

  // LogoImagePath: absolute url of logo Image.
  logoImagePath: '',

  // СlientLogoPosition:  Client logo position
  clientLogoPosition: 'Right',

  // ReportTitleTextColour:  Report title text colour
  reportTitleTextColour: '#ffa7a7',

  // FooterBrandBarColourKey:  Colour for the footer brand bar.
  footerBrandBarColourKey: '#ffffff',

  // ReportInfoColour : the report info text colour on front page.
  reportInfoColour: '#510000',

  // SolutionTextColour : the solution name text colour on front page.
  solutionTextColour: '#000000',

  // ParticipantNameTextColour: The participant name text colour.
  participantNameTextColour: '#c00000',

  // FrontPageVerticalLineColour: The front page vertical line colour.
  frontPageVerticalLineColor: '#ffa7a7',

  // CoBranded:  KF logo are hidden for client branded reports , and shown for co branded reports
  coBranded: true,
};
