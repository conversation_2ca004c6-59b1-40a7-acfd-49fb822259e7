import { Injectable } from "@angular/core";
import { Observable, of } from "rxjs";
import {
  CustomProjectTypeObj,
  CustomProjectTypeHistory,
  Months
} from "@/shared/models/customProjectTypeDetails";
import { ApiService } from './api.service';

@Injectable({
  providedIn: "root",
})
export class CustomProjectTypeService {
  constructor(private apiService: ApiService) {}

  getCustomProjectTypes(clientId: number): Observable<CustomProjectTypeObj[]> {
    return this.apiService.get<CustomProjectTypeObj[]>(
      `customprojecttype/${clientId}`,
      (json) => <CustomProjectTypeObj[]>json,
      null
    );
  }

  getCustomProjectTypeDetails(projectTypeId): Observable<CustomProjectTypeObj> {
    return this.apiService.get<CustomProjectTypeObj>(
      `customprojecttype/details/${projectTypeId}`,
      (json) => <CustomProjectTypeObj>json,
      null
    );
  }

  createCustomProjectType(payload) {
    const url = `customprojecttype`;
    return this.apiService.postWithBodyRaw(url, json => json, payload);
  }

  updateCustomProjectType(payload) {
    const url = `customprojecttype`;
    return this.apiService.put(url, json => json, payload);
  }

  getCustomProjectTypeHistory(projectTypeId): Observable<CustomProjectTypeHistory[]> {
    return this.apiService.get<CustomProjectTypeHistory[]>(
      `customprojecttype/history/${projectTypeId}`,
      (json) => <CustomProjectTypeHistory[]>json,
      null
    );
  }

  getMonthName(month) {
    return Months[month];
  }

  getUserGroups(clientId: number) {
        return this.apiService.get<any>(
        `client/userGroups/${clientId}`,
        (json) => <any>json,
        null
      );
  }
}
