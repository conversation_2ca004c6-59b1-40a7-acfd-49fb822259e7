import { Injectable } from "@angular/core";
import { ApiService } from './api.service';
import { Observable } from "rxjs";

@Injectable({
  providedIn: "root",
})
export class CollaboratorStakeholderService {
  constructor(private apiService: ApiService) {}

  getCollaborationStakeholderDetails(collabId: number, pageIndex:any, pageSize: any) : Observable<any> {
    return this.apiService.getSPS(`getallcollaboratorsbycollabid?pageIndex=${pageIndex}&pageSize=${pageSize}&collabId=${collabId}`);
  }
} 