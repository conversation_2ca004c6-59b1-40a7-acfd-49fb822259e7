import { ClientDeta<PERSON>, ClientSearchR<PERSON>ult, ClientSpsSearchResult } from "@/shared/models";
import { NotificationHeader } from "@/shared/models/NotificationHeader";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { ApiService } from "./api.service";
import { environment } from "@/shared/environments/environment";

@Injectable({
  providedIn: "root",
})
export class ClientSearchService {
  constructor(private apiService: ApiService) {}

  getClients(name: string): Observable<ClientSearchResult> {
    const encodedClientName = encodeURIComponent(name); 
    return this.apiService.get<ClientSearchResult>(
      `clientSearch?clientName=${encodedClientName}`,
      (json) => <ClientSearchResult>json,
      null
    );
  }

  getClientById(clientId: number): Observable<ClientDetails> {
    return this.apiService.get<ClientDetails>(
      `client/${clientId}`,
      (json) => <ClientDetails>json,
      null
    );
  }

  getClientNotificationHeaders(
    clientId: number
  ): Observable<NotificationHeader[]> {
    return this.apiService.get<NotificationHeader[]>(
      `notification/headers?KFASClientId=${clientId}`,
      (json) => <NotificationHeader[]>json,
      null
    );
  }

  updateClient(updatedClient: ClientDetails) {
    return this.apiService.postWithBodyRaw<any, any>(
      `client/update`,
      (json) => <any>json,
      {
        DataProtectionAutoAcknowledge:
          updatedClient.dataProtectionAutoAcknowledge,
        DefaultReminderTemplateId: updatedClient.defaultReminderTemplateId,
        DefaultReportReleaseNotificationId: updatedClient.defaultReportReleaseNotificationId,
        DefaultSsoSetting: updatedClient.defaultSsoSetting,
        DefaultSsoInvitationTemplateId: updatedClient.defaultSsoInvitationTemplateId,
        DefaultSsoReminderTemplateId: updatedClient.defaultSsoReminderTemplateId,
        DefaultTemplateId: updatedClient.defaultTemplateId,
        HideBioDataPage: updatedClient.hideBioDataPage,
        UseCustomReflexDomains: updatedClient.useCustomReflexDomains,
        Id: updatedClient.id,
        isProctoringEnabled: updatedClient.isProctoringEnabled,
        DefaultProctoringConfigurationId: updatedClient.defaultProctoringConfigurationId,
      }
    );
  }

  getSPSClient(name:string):Observable<ClientSpsSearchResult>{
    return this.apiService.getSPS(`getclientlistbyclientname?clientName=${name}`);  
  }
}
