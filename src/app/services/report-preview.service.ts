import { ProjectAssessmentOptions } from '@/shared/models/configurable-reports/projectAssessment';
import { ReportPreviewOptions } from '@/shared/models/configurable-reports/reportPreviewOptions';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root'
})
export class ReportPreviewService {

  /**
   * Creates an instance of reportPreviewService.
   * @param  apiService to handle http calls.
   */
  constructor(private apiService: ApiService) {}

  /**
   * Method to get options required for report preview.
   * @param clientId Id of the client
   * @returns  Observable<ReportPreviewOptions>
   */
  getOptions(clientId: number): Observable<ReportPreviewOptions> {
    return this.apiService.get<ReportPreviewOptions>(
      `ReportPreview/Options/${clientId}`,
      json => <ReportPreviewOptions>json,
      null
    );
  }

  /**
   * Method to get assessments binding with selected project type, language and success profile.
   * @param clientId Id of the client
   * @param projectType Id of the project type
   * @param languageId Id of the language
   * @param successProfileId Id of the successProfile (optional)
   * @returns  Observable<ProjectAssessmentOptions>
   */
  getAssessments(clientId: number, projectType: string, languageId: number, successProfileId: number = null)
    : Observable<ProjectAssessmentOptions> {
    return this.apiService.get<ProjectAssessmentOptions>(
      `ReportPreview/ProjectOptions/${clientId}?projectType=${projectType}&successprofileId=${successProfileId}&languageId=${languageId}`,
      json => <ProjectAssessmentOptions>json,
      null
    );
  }

  getPreviewUrl(params: string) {
    return this.apiService.buildActionUrl(`ReportPreview?${params}`);
  }

  getPdf(input: Map<string, string>) {
    return this.apiService.postUrlFormEncodedWithFileResponse('ReportPreview', input);
  }
}
