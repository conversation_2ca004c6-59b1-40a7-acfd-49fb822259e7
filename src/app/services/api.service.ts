import { environment } from "@/shared/environments/environment";
import { QueryOptions } from "@/shared/query-builders/query-options";
import {
  HttpBackend,
  HttpClient,
  HttpEvent,
  HttpHeaders,
  HttpRequest,
} from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { map, tap } from "rxjs/operators";
import * as _ from "lodash";
import { SharedService } from "@/shared/services/shared.service";

/**
 * NOTE: TODO: complete this interface , as this is work in progress.
 * Represents options to control a request to the server.
 */
export interface RequestOptions {
  /**
   * Have every request include authorization headers.
   */
  authorize: boolean;
}

export interface Serializer {
  fromJson(json: any): any;
  toJson(resource: any): any;
}

/**
 * Api service class to make api calls , it acts as a wrapper for http client.
 */
@Injectable({
  providedIn: "root",
})
export class ApiService {
  constructor(
    private httpClient: HttpClient,
    private queryOptions: QueryOptions, private sharedService: SharedService, private handler: HttpBackend
  ) { }

  /**
   * Api request options.
   */
  DefaultRequestOptions: RequestOptions = {
    authorize: true,
  };

  /**
   * Creates the full url using the environment variables of the api method to call.
   * Note: The action name might already be the full url, so just pass through as is
   * @param action The api action name to build the url with.
   */
  public buildActionUrl(action: string): string {
    // Absolute path, no need to build with the existing configuration.
    if (action.substr(0, 4).toLowerCase() === "http") {
      return action;
    }

    return this.buildActionUrlFromParts(
      environment.ADMIN_API_BASE_URL,
      environment.ADMIN_APP_API_VERSION,
      action
    );
  }

  /**
   * Builds an encoded relative URL with given params ignoring null and undefined values.
   * @param action the action name
   * @param params an object which will be transformed into urlparams string, like `a=1&b=2&c=3` (ignoring null and undefined values)
   * @returns a string as encoded relative path with given params
   */
  buildRelativeUrlWithParams(action: string, params: any = {}) {
    const urlParams = _.chain(params).pickBy((param) => param != null).toPairs().map((kvp) => kvp.join("=")).join("&");
    const url = [action, urlParams].join('?');
    return encodeURI(url);
  }

  /**
   * Builds a complete url with the parts required for the api call. e.g. http://api.site.com/v1.0/action
   * @param apiUrlBase Base http path for the url
   * @param apiVersionNumber The version to use for the api
   * @param action The action name
   */
  buildActionUrlFromParts(
    apiUrlBase: string,
    apiVersionNumber: string,
    action: string
  ) {
    return `${apiUrlBase}v${apiVersionNumber}/${action}`;
  }

  /**
   * Get URL for preview the branding for Participant Portal Sign-in page
   */
  getPortalSigninPreviewUrl() {
    return environment.PP_SIGNIN_PREVIEW_URL;
  }

  /**
   * Get URL for preview the branding for Participant Portal Dashboard page
   */
  getPortalDashboardPreviewUrl() {
    return environment.PP_DASHBOARD_PREVIEW_URL;
  }

  /**
   * Send a GET request with query string parameters ( queryMap) and convert the result into another object.
   * @template TOutput output type.
   * @param  action the api action.
   * @param  converter A function that accepts JSON (any data) and converts it to the required type.
   * @param [queryMap] api request query string parameters.
   * @param [options=this.DefaultRequestOptions] Api request options.
   * @returns  Returns observable result of type TOutput.
   *
   */
  get<TOutput>(
    action: string,
    converter: (json: {}) => TOutput,
    queryMap?: Map<string, string>
  ): Observable<TOutput> {
    return this.httpClient
      .get(
        `${this.buildActionUrl(action)}${this.queryOptions.toQueryString(
          queryMap
        )}`,
        { headers: this.createHeaders() }
      )
      .pipe(map((item: any) => converter(item) as TOutput));
  }

  /**
   * Send a GET request to get file response.
   * @param  action the api action to call.
   * @returns observable of type TOutput.
   */
  getWithFileResponse(action: string): Observable<any> {
    return this.httpClient.get(this.buildActionUrl(action), {
      headers: this.createHeaders(),
      responseType: "blob",
    });
  }

  /**
   * Send a POST request with payload in body to get file response.
   * @param  action the api action to call.
   * @param  input post request body.
   * @returns observable of type TOutput.
   */
  postWithBodyWithFileResponse(action: string, input: any): Observable<any> {
    return this.httpClient.post(this.buildActionUrl(action), input, {
      headers: this.createHeaders(),
      responseType: "blob",
    });
  }

  /**
   * Send a Url form encoded type POST request.
   * @param  action the api action to call.
   * @param  input post input.
   * @returns observable of type Blob.
   */
  postUrlFormEncodedWithFileResponse(action: string, input: Map<string, any>) {
    const body = new URLSearchParams();

    const headers = new HttpHeaders()
      .set("Content-Type", "application/x-www-form-urlencoded")
      .set("cache-control", "no-cache");

    input.forEach((value, key) => {
      if (Array.isArray(value)) {
        (value as Array<any>).forEach((val) => {
          body.append(key, val);
        });
      } else {
        body.set(key, value);
      }
    });

    return this.httpClient.post(
      `${this.buildActionUrl(action)}`,
      body.toString(),
      {
        headers: headers,
        responseType: "blob",
      }
    );
  }

  /**
   * Send a Url form encoded type POST request.
   * @param  action the api action to call.
   * @param  converter function to convertor api response.
   * @param  input post input.
   * @returns observable of type TOutput.
   */
  postUrlFormEncoded<TInput, TOutput>(
    action: string,
    converter: (json: {}) => TOutput,
    input: TInput
  ) {
    const body = new URLSearchParams();
    for (const key in input) {
      if (input.hasOwnProperty(key)) {
        body.set(key, input[key].toString());
      }
    }
    const headers = new HttpHeaders().set(
      "Content-Type",
      "application/x-www-form-urlencoded"
    );

    return this.post<string, TOutput>(
      action,
      converter,
      body.toString(),
      headers
    );
  }

  /**
   * Posts a file to server.
   * @param  fileToBeUploaded file to be uploaded.
   * @param  responseType from api ('arraybuffer' | 'blob' | 'json' | 'text').
   * @returns observable of type HttpEvent<any>.
   */
  postFile(
    action: string,
    fileToBeUploaded: File,
    responseType: "arraybuffer" | "blob" | "json" | "text"
  ): Observable<HttpEvent<any>> {
    const fd = new FormData();
    fd.append("file", fileToBeUploaded);
    const req = new HttpRequest("POST", this.buildActionUrl(action), fd, {
      reportProgress: true,
      responseType: responseType,
    });
    return this.httpClient.request(req);
  }

  /**
   * Sends a PUT request.
   * @param  action the api action to call.
   * @param  converter function to convertor api response.
   * @param input post input.
   * @returns  observable of type TOutput.
   */
  put<TInput, TOutput>(
    action: string,
    converter: (json: {}) => TOutput,
    input: TInput
  ): Observable<TOutput> {
    return this.httpClient
      .put(this.buildActionUrl(action), input, {
        headers: this.createHeaders(),
      })
      .pipe(map((item: any) => converter(item) as TOutput));
  }

  /**
   * Sends a DELETE request.
   * @param  action the api action to call.
   * @param input post input.
   */
  delete<TInput>(action: string, input: TInput): Observable<object> {
    return this.httpClient.delete(`${this.buildActionUrl(action)}/${input}`, {
      headers: this.createHeaders(),
    });
  }

  /**
   * Send a POST request with payload in body.
   * @param  action the api action to call.
   * @param  converter function to convertor api response.
   * @param  input post input.
   * @returns observable of type TOutput.
   */
  postWithBody<TInput, TOutput>(
    action: string,
    converter: (json: {}) => TOutput,
    input: TInput
  ): Observable<TOutput> {
    return this.httpClient
      .post(this.buildActionUrl(action), input, {
        headers: this.createHeaders(),
      })
      .pipe(map((item) => converter(item) as TOutput));
  }

  /**
   * Send a POST request with payload in body.
   * @param  action the api action to call.
   * @param  converter function to convertor api response.
   * @param  input post input.
   * @returns observable of type TOutput.
   */
  postWithBodyRaw<TInput, TOutput>(
    action: string,
    converter: (json: {}) => TOutput,
    input: TInput
  ): Observable<TOutput> {
    return this.httpClient
      .post(this.buildActionUrl(action), input)
      .pipe(map((item) => converter(item) as TOutput));
  }

  /**
   * Send a POST request.
   * @param  action the api action to call.
   * @param  converter function to convertor api response.
   * @param  input post input.
   * @returns observable of type TOutput.
   */
  private post<TInput, TOutput>(
    action: string,
    converter: (json: {}) => TOutput,
    input: TInput,
    headers: HttpHeaders
  ): Observable<TOutput> {
    return this.httpClient
      .post(this.buildActionUrl(action), input, { headers: headers })
      .pipe(map((item) => converter(item) as TOutput));
  }

  private createHeaders(): HttpHeaders {
    const headers = new HttpHeaders();
    headers.append("Content-Type", "application/json");
    return headers;
  }

  getUcpHubToken(): Observable<any> {
    const ucpHubTokenEndPoint = 'v1.0/hubauthentication';
    const apiUrl = environment.ADMIN_API_BASE_URL + ucpHubTokenEndPoint;
    const currentUser = this.sharedService.getCurrentUser();
    let headers = new HttpHeaders();
    if(currentUser && currentUser.token){
      headers = headers.set('Authorization', `Bearer ${currentUser.token}`);
    }
    //handler is used here to avoid the infinite loop from jwt interceptor.
    const httpClient = new HttpClient(this.handler);
    return httpClient.get(apiUrl, { headers }).pipe(tap(res => {
      localStorage.setItem("ucpApiAccessToken", res.apiAccessToken);
    }));
  }

  //generic get method for 'ucp sps'.
  getSPS(endPoint: string, queryMap?: Map<string, string>): Observable<any> {
    const apiUrl =`${environment.UCP_SPS_BASE_URL}${endPoint}${this.queryOptions.toQueryString(queryMap)}`
    return this.httpClient.get(apiUrl, { headers: this.createHeaders() });
  }
}
