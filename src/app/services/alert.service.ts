﻿import { Injectable } from '@angular/core';
import { MatSnackBar, MatSnackBarRef } from '@angular/material';
import { NavigationStart, Router } from '@angular/router';

@Injectable({ providedIn: 'root' })
export class AlertService {
    private keepAfterNavigationChange = false;
    private errorDurationInSeconds = 8;
    snackBarRef: MatSnackBarRef<any>;

    constructor(private router: Router,
        public snackBar: MatSnackBar) {
        // clear alert message on route change
        router.events.subscribe(event => {
            if (event instanceof NavigationStart) {
                if (this.keepAfterNavigationChange) {
                    // only keep for a single location change
                    this.keepAfterNavigationChange = false;
                } else if (this.snackBarRef) {
                    // clear alert
                    this.snackBarRef.dismiss();
                }
            }
        });
    }

    success(message: string, keepAfterNavigationChange = false) {
        this.keepAfterNavigationChange = keepAfterNavigationChange;
        this.snackBarRef = this.snackBar.open(message, 'Close', { panelClass: ['success'] });
    }

    error(message: string, keepAfterNavigationChange = false) {
        this.keepAfterNavigationChange = keepAfterNavigationChange;
        this.snackBarRef = this.snackBar.open(message, 'Close', { panelClass: ['error'],  duration: this.errorDurationInSeconds * 1000, });
    }
}
