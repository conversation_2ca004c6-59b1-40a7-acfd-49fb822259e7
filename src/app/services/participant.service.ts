
import { Client, ClientAssessmentN<PERSON>, ProctoringResult } from '@/shared/models';
import { Observable, of } from 'rxjs';
import { UpdateHiredStatus } from '@/shared/models/projects/participants';
import { Injectable } from '@angular/core';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root',
})
export class ParticipantService {
  
  constructor(private apiService: ApiService) {}

  getParticipantDetails(participantId) {
    const url = `project/participantDetails/${participantId}`;
    return this.apiService.get(url, json => json, null);
  }

  getClientParticipantMetadata(clientId) {
    const url = `participants/searchMetadata?KFASClientId=${clientId}`;
    return this.apiService.get(url, json => json, null);
  }

  searchClientParticipants(
    clientId,
    searchKey,
    filter,
    pageIndex?: number,
    pageSize?: number,
    sortColumn: string = "",
    sortBy: string = ""
  ) {
    const filterKeys = Object.keys(filter).filter(
      (key) => filter[key] && filter[key].length
    );
    const filterBy = filterKeys.join("|");
    const filterValues = filterKeys
      .map((key) => filter[key].join(";"))
      .join("|");
    const encodedSearchKey = encodeURIComponent(searchKey);
    const url = `participants/search?kfasclientid=${clientId}&SearchString=${encodedSearchKey}&FilterBy=${filterBy}&FilterValues=${filterValues}&pageIndex=${pageIndex}&pageSize=${pageSize}&sortColumn=${sortColumn}&sortBy=${sortBy}`;
    return this.apiService.get(url, (json) => json, null);
  }

  getAssessments(participantId, projectId) {
    const url = `assessments?candidateId=${participantId}&projectId=${projectId}`;
    return this.apiService.get(url, json => json, null);
  }

  resetAssessments(candidateId, projectId, assessmentIds) {
    const url = `assessments/reset`;
    const body = {
      candidateId,
      projectId,
      assessmentIds,
    };
    return this.apiService.postWithBodyRaw<any, any>(url, json => json, JSON.stringify(body));
  }

  reenableAssessments(candidateId, projectId, assessmentIds) {
    const url = `assessments/reenable`;
    const body = {
      candidateId,
      projectId,
      assessmentIds,
    };
    return this.apiService.postWithBodyRaw<any, any>(url, json => json, JSON.stringify(body));
  }

  resetSjtAssessments(candidateId, projectId, assessmentIds) {
    const url = `assessments/resetSjt`;
    const body = {
      candidateId,
      projectId,
      assessmentIds,
    };
    return this.apiService.postWithBodyRaw<any, any>(url, json => json, JSON.stringify(body));
  }

  reenableSjtAssessments(candidateId, projectId, assessmentIds) {
    const url = `assessments/reenableSjt`;
    const body = {
      candidateId,
      projectId,
      assessmentIds,
    };
    return this.apiService.postWithBodyRaw<any, any>(url, json => json, JSON.stringify(body));
  }

  updateLanguage(model) {
    const url = `assessments/updateLanguage?languageId=${model.languageId}&assessmentId=${model.assessmentId}`;
    return this.apiService.postWithBody<any, any>(url, json => json, model);
  }

  updateTime(model) {
    const url = `assessments/updatePercentageTime?kfasAssessmentId=${model.kfasAssessmentId}&assessmentId=${model.assessmentId}&timePercentage=${model.timePercentage}&candidateId=${model.candidateId}`;
    return this.apiService.postWithBody<any, any>(url, json => json, model);
  }

  updateHiredStatus(model: UpdateHiredStatus, clientId: number) {
    const url =  `participantsHiredBatch?kfasclientid=${clientId}`;
    return this.apiService.postWithBody<any, any>(url, json => json, model);
  }

  getParticipantProctoringResults(participantId: number, projectId: number) {
    const url =  `proctoring/scores/${participantId}?projectId=${projectId}`;
    return this.apiService.get(url, (json) => json, null);
  }

  getParticipantReviewLink(participantId: number, assessmentId: number) {
    const url =  `proctoring/${participantId}/reviewLink/${assessmentId}`;
    return this.apiService.get(url, (json) => json, null);
  }

  resyncProctoringScores(participantId: number, projectId: number) {
    const url = `proctoring/scores/${participantId}/sync?projectId=${projectId}`;
    return this.apiService.get(url, json => json, null);
  }

}
