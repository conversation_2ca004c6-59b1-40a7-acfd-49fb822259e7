import { AssessmentValidityPeriod, ClientAssessmentValidityPeriodsModel } from "@/shared/models/assessment-validity-period/assessment-validity-period";
import { Injectable } from "@angular/core";
import { plainToClass } from "class-transformer";
import { Observable } from "rxjs";
import { ApiService } from ".";

@Injectable({
    providedIn: 'root',
})

/**
 * Service for assessment validity periods
 */
export class AssessmentValidityPeriodService {

  constructor(public apiService: ApiService) {}

  /**
   * Loads assessment validity periods for a client.
   * @param clientId Id of the Client.
   */
  getAssessmentsValidityPeriods(clientId: number): Observable<ClientAssessmentValidityPeriodsModel> {
    return this.apiService.get<ClientAssessmentValidityPeriodsModel>(
      `${'clientAssessmentValidityPeriod'}/${clientId}`,
      (result: Object):ClientAssessmentValidityPeriodsModel => {
        return ((plainToClass(
          Object,
          result as Object
        ) as Object) as ClientAssessmentValidityPeriodsModel);
      },
      null);
  }

  /**
   * Adds an assessment validity period.
   * @param period assessment validity period to be added.
   */
  addAssessmentValidityPeriod(period: AssessmentValidityPeriod): Observable<number> {
    return this.apiService.put<AssessmentValidityPeriod, number>(
      'saveClientAssessmentValidityPeriod',
      (result) => result as number,
      period
    );
  }

  /**
   * Updates an assessment validity period.
   * @param period assessment validity period to be updated.
   */
  updateAssessmentValidityPeriod(period: AssessmentValidityPeriod): Observable<number> {
    return this.apiService.postWithBody<AssessmentValidityPeriod, number>(
      'saveClientAssessmentValidityPeriod',
      (result) => result as number,
      period
    );
  }

  /**
   * Saves as assessment validity period.
   * @param period assessment validity period to be saved. 
   */
  saveAssessmentValidityPeriod(period: AssessmentValidityPeriod): Observable<number> {
    return period.clientAssessmentValidityPeriodId > 0 
        ?  this.updateAssessmentValidityPeriod(period)
        :  this.addAssessmentValidityPeriod(period);
  }

  /**
   * Deletes an assessment validity period.
   * @param period assessment validity period to be deleted.
   */
  deleteAssessmentValidityPeriod(period: AssessmentValidityPeriod): Observable<Object> {
    return this.apiService.delete<number>('deleteClientAssessmentValidityPeriod', period.clientAssessmentValidityPeriodId);
  }

  /**
   * Overrides assessment validity periods.
   * @param clientId Id of the Client.
   */
  overrideInheritedValidityPeriods(clientId: number): Observable<Object> {
    return this.apiService.postWithBody<number, any>(
      'overrideInheritedValidityPeriods',
      json => json,
      clientId
    );
  }
}
