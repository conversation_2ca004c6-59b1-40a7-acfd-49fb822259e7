import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { ApiService } from "./api.service";
import { CollaborationListResponse } from "@/shared/models/sps-client/collaboration-list";

@Injectable({
    providedIn: "root",
})
export class SPSCollaborationService {
    
    constructor(private apiService: ApiService) { }
    getCollaborationListData(clientId: string, pageIndex?:number, pageSize?:number): Observable<CollaborationListResponse> {
        return this.apiService.getSPS(`getcollaborationbyclientid?clientId=${clientId}&pageIndex=${pageIndex}&pageSize=${pageSize}`);
    }

}