import { ReportAsset } from '@/shared/models/configurable-reports/ReportAsset';
import { HttpEvent } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root'
})
export class UploadAssetsService {

  /**
   * Creates an instance of UploadAssetsService.
   * @param  apiService to handle http calls.
   */
  constructor(private apiService: ApiService) { }

  /**
   * Method to get assets uploaded for selected client.
   * @param clientId Id of the client
   * @returns  Observable<ReportAsset[]>
   */
  getClientAssets(clientId: number): Observable<ReportAsset[]> {
    return this.apiService.get<ReportAsset[]>(
      `ReportAssets/${clientId}`,
      json => <any>json,
      null
    );
  }

  /**
   * Method to upload new asset for passed client.
   * @param clientId Id of the client
   * @returns  Observable<ReportAsset[]>
   */
  uploadNewAsset(clientId: number, uploadFile: File): Observable<HttpEvent<any>> {
    return this.apiService.postFile(
      `ReportAssets/${clientId}`,
      uploadFile,
      'json'
    );
  }
}
