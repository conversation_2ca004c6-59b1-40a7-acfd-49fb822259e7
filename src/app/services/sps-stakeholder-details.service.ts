import { Injectable } from "@angular/core";
import { ApiService } from './api.service';
import { Observable } from "rxjs";

@Injectable({
  providedIn: "root",
})
export class StakeholderDetailsService {
  constructor(private apiService: ApiService) {}

  getStakeholderDetails(clientId:any, emailId:any, pageIndex:any, pageSize: any) : Observable<any>{
    return this.apiService.getSPS(`getstakeholderdetailsbyclientidandemailid?clientId=${clientId}&emailId=${emailId}&pageIndex=${pageIndex}&pageSize=${pageSize}`)
  }



}
