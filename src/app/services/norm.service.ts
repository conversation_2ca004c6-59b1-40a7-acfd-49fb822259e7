  import {
    ClientAssessmentNorms,
    ClientCustomNormDescriptions,
    CustomNormDescription,
    ClientAssessmentNormsModel,
    SavedCustomNorm
  } from '@/shared/models';
  import { Injectable } from '@angular/core';
  import { plainToClass } from 'class-transformer';
  import { Observable } from 'rxjs';
  import { ApiService } from './api.service';

  @Injectable({
    providedIn: 'root',
  })

  /**
   * Service for norms
   */
  export class NormService {

    constructor(public apiService: ApiService) {}

    /**
    *Loads custom norm descriptions for a client.
    */
    getClientCustomNormDescriptions(clientId: number): Observable<CustomNormDescription[]> {
      return this.apiService
        .get<CustomNormDescription[]>(
          `${'clientCustomNormDescriptions'}/${clientId}`,
          (result: object): CustomNormDescription[] => {
            return ((plainToClass(
              ClientCustomNormDescriptions,
              result as Object
            ) as object) as ClientCustomNormDescriptions).customNorms;
          },
          null
        );
    }

    /**
    *Loads the list of custom norms, grouped by Assessments, for client
    */
    getClientAssessmentNorms(clientId: number): Observable<ClientAssessmentNormsModel> {
      return this.apiService
        .get<ClientAssessmentNormsModel>(
          `${'clientAssessmentNorms'}/${clientId}`,
          (result: object):ClientAssessmentNormsModel => {
            return ((plainToClass(
              Object,
              result as Object
            ) as object) as ClientAssessmentNormsModel);
          },
          null
        );
    }

    /**
     * Loads custom norm with details
     */
    getCustomNormWithDetails(clientId: number, normNo: number): Observable<CustomNormDescription> {
      return this.apiService
        .get<CustomNormDescription>(
          `${'customNormWithDetails'}?clientId=${clientId}&normNo=${normNo}`,
          (result: object): CustomNormDescription => {
            return (plainToClass(
              CustomNormDescription,
              result as Object
            ) as object) as CustomNormDescription;
          },
          null
        );
    }

    /**
     * Saves custom norm with details
     */
    saveCustomNormWithDetails(normDetails: CustomNormDescription): Observable<SavedCustomNorm> {
      return normDetails.normNo > 0
        ? this.apiService.postWithBody<CustomNormDescription, SavedCustomNorm>(
          `${'saveCustomNormWithDetails'}`,
          json => json as SavedCustomNorm,
          normDetails)
        : this.apiService.put<CustomNormDescription, SavedCustomNorm>(
          `${'saveCustomNormWithDetails'}`,
          json => json as SavedCustomNorm,
          normDetails);
    }
  }
