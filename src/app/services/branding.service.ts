import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';

import { PortalBrandingSettings } from '@/shared/models';
import { BrandingIdName } from '@/shared/models/BrandingIdName';
import {
  BrandingData,
  BrandingSettings,
} from '@/shared/models/configurable-reports/brandingSettings';

@Injectable({
  providedIn: 'root',
})
export class BrandingService {
  constructor(private apiService: ApiService) {}

  getSettings(clientId: number): Observable<any> {
    return this.apiService.get<any>(
      `Client/Report/Branding/${clientId}`,
      (json) => json,
      null
    );
  }

  updateSettings(data: BrandingData): Observable<number> {
    return this.apiService.put(`Client/Report/Branding`, (json) => +json, data);
  }

  downloadPreview(
    reportId: number,
    clientId: number,
    settings: BrandingSettings
  ): Observable<Blob> {
    const data = { reportId, clientId, settings };
    return this.apiService.postWithBodyWithFileResponse(
      `Client/Report/Branding/Preview`,
      data
    );
  }

  getPortalSettings(clientId: number): Observable<any> {
    return this.apiService.get<any>(
      `Client/Portal/Branding/${clientId}`,
      (json) => json,
      null
    );
  }

  updatePortalSettings(branding: PortalBrandingSettings): Observable<BrandingIdName> {
    return this.apiService.postWithBodyRaw(
      `Client/Portal/Branding/${branding.kfasClientId}`,
      json => json as BrandingIdName,
      branding);
  }

  getSigninPreviewPortalUrl(branding: BrandingIdName): string {
    const baseUrl = this.apiService.getPortalSigninPreviewUrl();
    return this.enforceHttp(`${baseUrl}/${branding.name}/true`);
  }

  getDashboardPreviewPortalUrl(branding: BrandingIdName): string {
    const baseUrl = this.apiService.getPortalDashboardPreviewUrl();
    return this.enforceHttp(`${baseUrl}/${branding.name}`);
  }

  private enforceHttp(url: string): string {
    if(url.startsWith("http://") || url.startsWith("https://")) return url;

    return `https://${url}`;
  }
}
