import {
  DisableReportRequestData,
  EnableReportRequestData,
  ReportData,
} from '@/components/dialogs/report-details-dialog/report-details.data';
import { AlertService } from '@/services/alert.service';
import {
  ClientReportActions,
  ReportConfiguration,
  ReportDisplayOrder,
  ReportDisplayOrderUpdateRequest,
} from '@/shared/models';
import { Injectable } from '@angular/core';
import { plainToClass } from 'class-transformer';
import { BehaviorSubject, Observable } from 'rxjs';
import { ApiService } from './api.service';

/**
 * Singleton Service to load client reports from api.
 */
@Injectable({
  providedIn: 'root',
})
export class ClientReportsService {
  /* client for which report data is being retrieved */
  clientId: number;

  /**
   * Observable for all the client reports loaded for a client. Service user can subscribe  it to view client reports.
   */
  reportConfigurations: Observable<ReportConfiguration[]>;

  private reportConfigurationSubject: BehaviorSubject<ReportConfiguration[]>;
  private action: string;

  /* To indicate if the result have been loaded from api.*/
  private loadedSubject: BehaviorSubject<boolean>;
  loaded: Observable<boolean>;

  /* To indicate if report filter control is to be shown. */
  private showFiltersSubject: BehaviorSubject<boolean>;
  showFilters: Observable<boolean>;

  /* To indicate if reports display control is to be made visible */
  private showReportsSubject: BehaviorSubject<boolean>;
  showReports: Observable<boolean>;

  /* To indicate if reports display control is to be made visible */
  private showDisplayOrderSubject: BehaviorSubject<boolean>;
  showDisplayOrder: Observable<boolean>;

  /* Id of the report that has been selected to view/update  configuration. */
  selectedReportId: number;

  /**
   *Creates an instance of ClientReportsService.
   */
  constructor(
    public apiService: ApiService,
    private alertService: AlertService
  ) {
    this.action = 'clientReportConfiguration';
    this.reportConfigurationSubject = <BehaviorSubject<ReportConfiguration[]>>(
      new BehaviorSubject([])
    );
    this.loadedSubject = <BehaviorSubject<boolean>>new BehaviorSubject(false);
    this.showFiltersSubject = <BehaviorSubject<boolean>>(
      new BehaviorSubject(false)
    );
    this.showReportsSubject = <BehaviorSubject<boolean>>(
      new BehaviorSubject(false)
    );
    this.showDisplayOrderSubject = <BehaviorSubject<boolean>>(
      new BehaviorSubject(false)
    );

    this.reportConfigurations = this.reportConfigurationSubject.asObservable();
    this.loaded = this.loadedSubject.asObservable();
    this.showFilters = this.showFiltersSubject.asObservable();
    this.showReports = this.showReportsSubject.asObservable();
    this.showDisplayOrder = this.showDisplayOrderSubject.asObservable();
  }

  // TODO: Add a separate method to load updated singlw report for a client.

  /**
   *Loads all the report specific data for a client.
   */
  loadClientReports(clientId: number, resetState: boolean = true) {
    if (resetState) {
      this.reset();
    }
    this.clientId = clientId;
    this.apiService
      .get<ReportConfiguration[]>(
        `${this.action}/${clientId}`,
        this.convertor,
        null
      )
      .subscribe(
        (result) => {
          this.reportConfigurationSubject.next(result);
          this.loadedSubject.next(true);
        },
        (error) => {
          this.alertService.error(error.message);
          this.loadedSubject.next(false);
        }
      );
  }

  /**
   * Saves the updated report display order to database.
   */
  saveDisplayOrder(
    updatedDisplayOrders: ReportDisplayOrder[]
  ): Observable<string> {
    const reportDisplayOrderUpdateRequest = new ReportDisplayOrderUpdateRequest(
      updatedDisplayOrders
    );
    return this.apiService.postWithBody<
      ReportDisplayOrderUpdateRequest,
      string
    >(
      'clientReportsDisplayOrder',
      (json) => json as string,
      reportDisplayOrderUpdateRequest
    );
  }

  getAllUnavailableReports() {
    return this.apiService.get<ReportData[]>(
      `client/report/getReportsUnAvailableToClient/${this.clientId}`,
      (json) => <ReportData[]>json,
      null
    );
  }

  enableReportForClient(enableData: EnableReportRequestData) {
    return this.apiService.postWithBody<EnableReportRequestData, string>(
      `client/report/Enable`,
      (json) => <string>json,
      enableData
    );
  }

  disableReportForClient(disableData: DisableReportRequestData) {
    return this.apiService.postUrlFormEncoded<DisableReportRequestData, string>(
      `client/report/disable/${this.clientId}?reportId=${disableData.reportId}`,
      (json) => <string>json,
      null
    );
  }

  /* Update the client report component display state based on action passed */
  state(action: ClientReportActions, selectedReportId?: number) {
    switch (action) {
      case ClientReportActions.showFilters: {
        this.reset();
        this.selectedReportId = selectedReportId;
        this.showFiltersSubject.next(true);
        break;
      }
      case ClientReportActions.showReports: {
        this.reset();
        this.showReportsSubject.next(true);
        break;
      }
      case ClientReportActions.showDisplayOrder: {
        this.reset();
        this.showDisplayOrderSubject.next(true);
        break;
      }
      case ClientReportActions.none:
      default:
        break;
    }
  }

  /**
   * Reset child control status to default ( hidden).
   */
  private reset() {
    this.selectedReportId = undefined;
    this.showFiltersSubject.next(false);
    this.showReportsSubject.next(false);
    this.showDisplayOrderSubject.next(false);
  }

  private convertor = (result: object): ReportConfiguration[] => {
    // Plain to class, converts to array as well. Converting the result to object first  so that it can be cast to type
    return (plainToClass(
      ReportConfiguration,
      result as Object
    ) as object) as ReportConfiguration[];
  }
}
