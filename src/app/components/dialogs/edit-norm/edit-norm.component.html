<h2 mat-dialog-title>Amend Project Location/Norm</h2>
<kf-loading></kf-loading>

<ng-container mat-dialog-content>
  <ng-container *ngIf="activeStep == 1">
    <div class="content">
      <div class="row">
        <div class="col">
          <div class="label">Project Location</div>
        </div>
        <div class="col" *ngIf="isNormEditable">
          <div class="label">Norm</div>
        </div>
      </div>
      <div class="row">
        <div class="col">
          <kf-select
            placeholder="Project Location"
            [options]="locationOptions"
            [selection]="selectedOptions.LOCATION"
            (selectionChange)="onSelectedOptionChange('LOCATION', $event)"
            [multiple]="false"
          ></kf-select>
        </div>
        <div class="col" *ngIf="isNormEditable">
          <kf-select
            placeholder="Project Norm"
            [options]="normOptions"
            [selection]="selectedOptions.NORM"
            (selectionChange)="onSelectedOptionChange('NORM', $event)"
            [multiple]="false"
          ></kf-select>
        </div>
      </div>
      <div class="row">
        <div class="col">
          <p>
            Note that changing these fields will result in all completed
            assessments within the project being rescored. Depending on the
            project size, this may take a considerable time. While this is
            happening, the participants' status will show as 'Scoring'.
          </p>
          <br />
          <p>
            If this project includes multiple success profiles, assessments will
            be rescored against all profiles.
          </p>
        </div>
      </div>
    </div>
  </ng-container>

  <ng-container *ngIf="activeStep == 2">
    <div class="content">
      <div class="row">
        <div class="col">
          <p>
            Selecting this norm has had the following impact on which traits
            and/or drivers are assessed for.
          </p>
          <p>
            <span class="both">Bold</span> appears in original Success Profile
          </p>
          <p>
            <span class="new-only">Highlight</span> is new to Success Profile
          </p>
          <p>
            <span class="old-only">Highlight with strikethrough</span> is
            removed from the original Success Profile
          </p>
        </div>
      </div>

      <mat-tab-group>
        <mat-tab *ngFor="let impactPreviewItem of allAssessmentImpactPreview">
          <ng-template mat-tab-label>
            {{ impactPreviewItem.title }}
          </ng-template>

          <div class="row">
            <div class="col" *ngIf="impactPreviewItem.traitsImpactPreview">
              <h3>TRAITS</h3>
              <ul>
                <ng-container *ngFor="let item of impactPreviewItem.traitsImpactPreview">
                  <li>
                    <span class="{{ item.state }}">{{ item.title }}</span>
                  </li>
                </ng-container>
              </ul>
            </div>

            <div class="col" *ngIf="impactPreviewItem.driversImpactPreview">
              <h3>DRIVERS</h3>
              <ul>
                <ng-container *ngFor="let item of impactPreviewItem.driversImpactPreview">
                  <li>
                    <span class="{{ item.state }}">{{ item.title }}</span>
                  </li>
                </ng-container>
              </ul>
            </div>
          </div>
        </mat-tab>
      </mat-tab-group>
    </div>
  </ng-container>

  <mat-dialog-actions>
    <button mat-button mat-dialog-close>Cancel</button>

    <ng-container *ngIf="activeStep == 1 && showImpactPreview; else saveButton">
      <button mat-button (click)="next()" [disabled]="disableNextButton || !isNormSavable">Continue</button>
    </ng-container>

    <ng-template #saveButton>
      <button mat-button (click)="save()" [disabled]="disableNextButton || !isNormSavable">Save</button>
    </ng-template>
  </mat-dialog-actions>
</ng-container>
