@import "../../../../styles/colors.scss";
@import "../../../../styles/typography.scss";

h3 {
  margin: 0;
}

.content{
  span.both {
    font-weight: bold;
  }
  span.old-only{
    text-decoration: line-through;
    background: lightyellow;
  }
  span.new-only{
    background: lightyellow;
  }
}

.row {
  display: flex;
  margin: 8px;
  .col {
    flex: 50%;
  }
}

.mat-dialog-actions {
  justify-content: flex-end;

  button {
    cursor: pointer;
    display: inline-block;
    padding: 6px 12px;
    margin-right: 12px;

    white-space: nowrap;
    vertical-align: middle;

    font: unquote($proxima-font);
    font-size: 0.9em;
    font-weight: bold;
    letter-spacing: 0.5px;
    text-transform: uppercase;

    background: white;
    color: $primary--blue;
    border: 1px solid $primary--blue;

    &[disabled]{
      cursor: auto;
      background-color: $disabled-color;
      border-color: rgba(0, 0, 0, 0.1);
      color: $primary--grey;
    }

    &:hover:enabled {
      background: $primary--blue;
      color: white;
    }

    .mat-icon {
      margin-right: 12px;
      vertical-align: middle;
      color: inherit;
    }
  }
}
