import { NormModel, LocationModel, ProjectDetails, ProjectAssessments, ProjectLocation, SuccessProfile, GetNormResponse } from "@/shared/models";
import { ProjectService, SpinnerService } from "@/services";
import { Component, Inject, OnInit } from "@angular/core";
import { MatDialogRef, MAT_DIALOG_DATA } from "@angular/material";

@Component({
  selector: "app-edit-norm",
  templateUrl: "./edit-norm.component.html",
  styleUrls: ["./edit-norm.component.scss"],
})
export class EditNormComponent implements OnInit {
  contextProject: ProjectDetails;
  isNormEditable: boolean;
  isNormSavable: boolean = true;
  lists: {
    norms: NormModel[],
    locations: LocationModel[],
  };

  normOptions: any;
  locationOptions: any;

  activeStep: number;

  selectedOptions = {
    NORM: 0,
    LOCATION: 0,
  };

  allAssessmentImpactPreview: any;
  projStatus: string;
  normVersion: string;

  constructor(
    public dialogRef: MatDialogRef<EditNormComponent>,
    private projectService: ProjectService,
    private spinnerService: SpinnerService,
    @Inject(MAT_DIALOG_DATA) private data: any
  ) {}

  ngOnInit() {
    this.contextProject = this.data.contextProject;
    this.isNormEditable = this.data.isNormEditable;
    this.projStatus = this.data.projStatus;
    
    this.projectService
    .getNorms(this.contextProject.projectId)
    .subscribe((result) => {

      this.initSelectOptions(result);

      this.initSelectedOptions();

      this.activeStep = 1;
      this.allAssessmentImpactPreview = [];
    });
  }

  initSelectOptions(source:GetNormResponse){
      this.lists = {
        locations : source.data.locations,
        norms : source.data.norms
      };

      this.locationOptions = this.lists.locations.map((x: LocationModel) => ({
        id: x.countryId,
        value: x.countryName,
        disabled: false,
      }));

      this.normOptions = this.getFilteredNorm(this.contextProject.country);
  }

  getFilteredNorm(currentCountry: string) {
    var allNorms = this.lists.norms
      .filter((x) => x.normCountry === 'GLOBAL' || x.normLabel === currentCountry)
      .map((x: NormModel) => ({
        id: x.normId,
        value: `${x.normLabel} - ${x.normVersion}`,
        disabled: false,
        normVersion: x.normVersion
      }));
    const normVersion3_0 = allNorms.filter((norm) => norm.normVersion === '3.0');
    this.normVersion = normVersion3_0.length > 0 ? "3.0" : "2.0";
    const normVersion2_0 = allNorms.filter((norm) => norm.normVersion !== '3.0');
    let filteredNorms = this.contextProject.norm.normVersion === '3.0' ? normVersion3_0 : normVersion2_0;
    filteredNorms = filteredNorms.sort((a, b) => a.id - b.id);
    if (normVersion3_0.length > 0 && this.contextProject.norm.normVersion !== '3.0' && this.contextProject.projectType !== 'ENTRY') {
      this.isNormSavable = false; // KFAS-10943 #3
    }

    return filteredNorms;
  }

  initSelectedOptions(){

    if (this.isNormEditable) {
      const matchingNorm = this.normOptions.find((x) => x.id == this.contextProject.norm.normId);
      if (matchingNorm !== undefined) {
        this.selectedOptions.NORM = matchingNorm.id;
      }
    }

    const currentLocationId = this.contextProject.location != null ? this.contextProject.location.countryId : null;
    const findCountryPredicate = currentLocationId != null ? (x) => x.id == currentLocationId : (x) => x.value == 'United States of America';
    const currentLocation = this.locationOptions.find(findCountryPredicate);
    if(currentLocation !== null) {
      this.selectedOptions.LOCATION = currentLocation.id;
    }
  }

  onSelectedOptionChange(optionType, values) {

    this.selectedOptions[optionType] = values;

    if(optionType == 'LOCATION' && this.isNormEditable) {
      var currentCountry = this.lists.locations.find((x) => x.countryId == values);
      this.normOptions = this.getFilteredNorm(currentCountry.countryName);
      this.selectedOptions.NORM = null;
    }
  }

  next() {
    var newNorm = this.lists.norms.find(x => x.normId == this.selectedOptions.NORM && x.normVersion === this.normVersion);

    let counter = this.contextProject.successProfiles.length;
    this.spinnerService.activate();

    this.contextProject.successProfiles.forEach((successProfile) => {

      this.projectService
        .calculateNewAssessmentsForNewNorm(this.contextProject.projectId, successProfile.id, newNorm)
        .subscribe((result) => {

          this.buildAllAssessmentsImpactPreview(successProfile, JSON.parse(successProfile.assessments), result[0]);

          --counter;
          if(counter == 0) {
            this.spinnerService.deactivate();
            this.activeStep = 2;
          }
        });
    });
  }

  save(){
    const newNorm = this.isNormEditable ? this.lists.norms.find(x => x.normId == this.selectedOptions.NORM  && x.normVersion === this.normVersion) : null;
    const selectedLocation = this.lists.locations.find(x => x.countryId == this.selectedOptions.LOCATION);

    let newLocation = new ProjectLocation();
    newLocation.countryId = selectedLocation.countryId
    newLocation.isNormCountryId = selectedLocation.isNormCountry;

    this.spinnerService.activate();

    this.projectService
      .updateRegionalData(this.contextProject.projectId, newNorm, newLocation)
      .subscribe((data) => {
        this.spinnerService.deactivate();
        location.reload();
      });
  }

  get showImpactPreview() {

    if(this.selectedOptions.NORM == this.contextProject.norm.normId) {
      return false;
    }

    if(this.hasSuccessProfile() && this.hasDriversOrTraits()) {
       return true;
    }

      return false;
  }

  get disableNextButton() {
    return this.selectedOptions.NORM === null || this.selectedOptions.LOCATION === null || this.projStatus === 'INACTIVE';
  }

  buildAllAssessmentsImpactPreview(successProfile:SuccessProfile, currentAssessments: ProjectAssessments, newAssessments: ProjectAssessments) {

    var obj = {
      title: successProfile.name,
      driversImpactPreview: null,
      traitsImpactPreview: null,
    };

    if(this.hasMeasuredAssessment(this.contextProject.assessments.drivers) && this.canBuildPreview(currentAssessments.drivers, newAssessments.drivers)) {
      obj.driversImpactPreview = this.buildAssessmentImpactPreview(currentAssessments.drivers.report, newAssessments.drivers.report, successProfile.driversFullNames);
    }

    if(this.hasMeasuredAssessment(this.contextProject.assessments.traits) && this.canBuildPreview(currentAssessments.traits, newAssessments.traits)) {
      obj.traitsImpactPreview = this.buildAssessmentImpactPreview(currentAssessments.traits.report, newAssessments.traits.report, successProfile.traitsFullNames);
    }

    this.allAssessmentImpactPreview.push(obj);
  }

  buildAssessmentImpactPreview(currentValues, newValues, fullNamesDictionary) {

    var result = newValues.map(x => ({
      state: currentValues.includes(x) ? 'both' : 'new-only',
      title: this.getFullName(fullNamesDictionary, x),
    }));

    currentValues
      .filter(x => !newValues.includes(x))
      .forEach(x => result.push({
         state:'old-only',
         title: this.getFullName(fullNamesDictionary, x),
      }));

    return result;
  }

  getFullName(fullNamesDictionary: any, code:string) {
    if(fullNamesDictionary === undefined) return code;

    return fullNamesDictionary[code] || fullNamesDictionary[code.toUpperCase()] || fullNamesDictionary[code.toLowerCase()] || code;
  }

  hasSuccessProfile() {
    return this.contextProject.successProfiles != null
      && this.contextProject.successProfiles.length > 0
      && this.contextProject.successProfiles.some(x => x != null);
  }

  hasDriversOrTraits() {
    return this.hasMeasuredAssessment(this.contextProject.assessments.drivers)
      || this.hasMeasuredAssessment(this.contextProject.assessments.traits);
  }

  hasMeasuredAssessment(assessment:any) {
    return assessment != null && assessment.measure;
  }

  canBuildPreview(currentAssessment, newAssessment) {
    return currentAssessment != null && currentAssessment.report != null
      && newAssessment != null && newAssessment.report != null
  }
}
