import { AppMaterialModule } from '@/app.material.module';
import { SharedModule } from '@/shared/shared.module';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import {
  MatCheckboxModule,
  MatDialogModule,
  MatSlideToggleModule,
  MAT_DIALOG_DEFAULT_OPTIONS,
  MatTabsModule,
} from '@angular/material';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouterModule } from '@angular/router';
import {
  CandidateJobDetailsComponent,
  CandidateLogDetailsComponent,
  EmailTemplateComponent,
  EnableReportDialogComponent,
  FiltersDialogComponent,
  OverallScoreWidgetComponent,
  ParticipantDetailsInfoComponent,
  ReportsDisplayComponent,
  ReportsDisplayOrderComponent,
  ReportDetailsDialogComponent,
  ReportFiltersComponent,
  ReportLanguagesDialogComponent,
  SpDetailsComponent,
  EditNormComponent,
  CandidateReportLogsComponent,
  SelectCustomNormComponent,
  ValidityPeriodDialogComponent,
  CPTHistoryDialogComponent,
  CPTAmendmentReasonDialogComponent,
  CPTReportManagementConfirmationComponent,
  ReportDownloadLimitComponent,
  ParticipantProctoringInfoComponent,
  DeleteProjectDialogComponent,
  SystemLogDetailsComponent
} from '.';
import { ControlsModule } from '../controls/controls.module';

@NgModule({
  imports: [
AppMaterialModule,
    BrowserAnimationsModule,
    BrowserModule,
    CommonModule,
    DragDropModule,
    FormsModule,
    MatCheckboxModule,
    MatDialogModule,
    MatTabsModule,
    MatSlideToggleModule,
    ReactiveFormsModule,
    ControlsModule,
    RouterModule,
    SharedModule
  ],
  declarations: [
    CandidateJobDetailsComponent,
    CandidateLogDetailsComponent,
    EmailTemplateComponent,
    EnableReportDialogComponent,
    FiltersDialogComponent,
    OverallScoreWidgetComponent,
    ParticipantDetailsInfoComponent,
    ReportDetailsDialogComponent,
    ReportFiltersComponent,
    ReportsDisplayOrderComponent,
    ReportLanguagesDialogComponent,
    ReportsDisplayComponent,
    SpDetailsComponent,
    EditNormComponent,
    CandidateReportLogsComponent,
    SelectCustomNormComponent,
    ValidityPeriodDialogComponent,
    CPTHistoryDialogComponent,
    CPTAmendmentReasonDialogComponent,
    CPTReportManagementConfirmationComponent,
    ReportDownloadLimitComponent,
    ParticipantProctoringInfoComponent,
    DeleteProjectDialogComponent,
    SystemLogDetailsComponent
  ],
  exports: [
    CandidateJobDetailsComponent,
    CandidateLogDetailsComponent,
    EmailTemplateComponent,
    EnableReportDialogComponent,
    FiltersDialogComponent,
    OverallScoreWidgetComponent,
    ParticipantDetailsInfoComponent,
    ReportDetailsDialogComponent,
    ReportFiltersComponent,
    ReportsDisplayOrderComponent,
    ReportLanguagesDialogComponent,
    ReportsDisplayComponent,
    SpDetailsComponent,
    EditNormComponent,
    CandidateReportLogsComponent,
    SelectCustomNormComponent,
    ValidityPeriodDialogComponent,
    CPTHistoryDialogComponent,
    CPTAmendmentReasonDialogComponent,
    CPTReportManagementConfirmationComponent,
    ReportDownloadLimitComponent,
    ParticipantProctoringInfoComponent,
    DeleteProjectDialogComponent,
    SystemLogDetailsComponent
  ],
  entryComponents: [
    CandidateJobDetailsComponent,
    CandidateLogDetailsComponent,
    EmailTemplateComponent,
    EnableReportDialogComponent,
    FiltersDialogComponent,
    ParticipantDetailsInfoComponent,
    ReportDetailsDialogComponent,
    ReportLanguagesDialogComponent,
    ReportsDisplayOrderComponent,
    SpDetailsComponent,
    EditNormComponent,
    CandidateReportLogsComponent,
    SelectCustomNormComponent,
    ValidityPeriodDialogComponent,
    CPTHistoryDialogComponent,
    CPTAmendmentReasonDialogComponent,
    CPTReportManagementConfirmationComponent,
    ReportDownloadLimitComponent,
    ParticipantProctoringInfoComponent,
    DeleteProjectDialogComponent,
    SystemLogDetailsComponent
  ],
  providers: [
    { provide: MAT_DIALOG_DEFAULT_OPTIONS, useValue: { hasBackdrop: true } },
  ],
})
export class DialogsModule {
  constructor() {}
}
