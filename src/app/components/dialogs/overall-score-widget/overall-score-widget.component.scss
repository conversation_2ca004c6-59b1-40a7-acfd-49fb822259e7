@import '../../../../styles/colors.scss';
@import '../../../../styles/typography.scss';

:host {
  font: unquote($proxima-font);
  font-weight: 600;

  display: flex;
  align-items: center;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: flex-end;

  .score-bar-container {
    width: 200px;
    display: inline-flex;
    align-items: center;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-end;

    .score-bar {
      flex: 1 1 auto;
      height: 6px;
      margin-right: 3px;
      background: $primary--grey-medium;

      &.active {
        background: $primary--blue;
      }
    }
  }

  h5 {
    margin: 0 12px;
  }
}
