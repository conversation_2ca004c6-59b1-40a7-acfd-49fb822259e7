import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'overall-score-widget',
  templateUrl: './overall-score-widget.component.html',
  styleUrls: ['./overall-score-widget.component.scss'],
})
export class OverallScoreWidgetComponent implements OnInit {
  @Input() score = 0;
  @Input() maximum = 10;
  @Input() label: string;

  constructor() {}

  ngOnInit() {}

  get scoreBars() {
    const arr = Array.from({ length: this.maximum }, (k, v) => ({ active: v + 1 <= this.score }));
    return arr;
  }
}
