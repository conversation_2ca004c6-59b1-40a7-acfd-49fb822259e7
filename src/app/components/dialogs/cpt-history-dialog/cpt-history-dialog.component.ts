import { AlertService, CustomProjectTypeService, SpinnerService } from '@/services';
import { Client, CustomProjectTypeHistory, HISTORY_COLUMNS, Months } from '@/shared/models';
import { Component, OnInit, OnDestroy, Inject } from '@angular/core';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA, MatTableDataSource } from '@angular/material';
import { ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
@Component({
  selector: 'app-cpt-history-dialog',
  templateUrl: './cpt-history-dialog.component.html',
  styleUrls: ['./cpt-history-dialog.component.scss']
})
export class CPTHistoryDialogComponent implements OnInit, OnDestroy {
  currentSubscriptions: Subscription[] = [];

  dataSource: MatTableDataSource<any>;
  historyData: CustomProjectTypeHistory[];
  isLoading = false;
  client: Client;
  projectTypeId: any;
  columns: any[] = HISTORY_COLUMNS;
  constructor(
    private customProjectTypeService: CustomProjectTypeService,
    public dialogRef: MatDialogRef<CPTHistoryDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private alertService: AlertService,
    private spinnerService: SpinnerService,
    private route: ActivatedRoute,
  ) {}

  ngOnInit() {
    this.projectTypeId = this.data.projectTypeId;
   
    this.currentSubscriptions.push(
      this.route.data.subscribe(
      (result) => {
        this.client = result.client;
      },
      (error) => {
        this.alertService.error(error.message);
      })
    );

    this.refreshTable();

  }

  refreshTable() {
    this.isLoading = true;
    this.spinnerService.activate();
    this.historyData = [];

    this.currentSubscriptions.push(
      this.customProjectTypeService.getCustomProjectTypeHistory(this.projectTypeId).subscribe(
      result => {
        this.historyData = result;
        this.historyData.forEach(history => {
          const dateTimeOfChange = new Date(history.dateTimeOfChange);
          const formattedDate = `${dateTimeOfChange.getDate()} ${this.customProjectTypeService.getMonthName(dateTimeOfChange.getMonth())} ${dateTimeOfChange.getFullYear()}`;
          history.amendedBy = `${history.createdBy.firstNameKey} ${history.createdBy.lastNameKey}`;
          history.dateTimeOfChange = formattedDate;
        });
        this.isLoading = false;
        this.spinnerService.deactivate();
      },
      error => {
        this.isLoading = false;
        this.spinnerService.deactivate();
        this.alertService.error(error.message);
      })
    );
  }

  ngOnDestroy() {
    if (this.currentSubscriptions.length > 0) {
      this.currentSubscriptions.forEach(subscription => subscription.unsubscribe());
    }
  }
}