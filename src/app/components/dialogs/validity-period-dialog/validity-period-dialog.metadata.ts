import { ConfigurationNumericOption } from "@/shared/models";

export const VALIDITY_PERIODS_ASSESSMENT_TYPES: ConfigurationNumericOption[] = [
    { value: 2, viewValue: 'Elements Numerical' },
    { value: 3, viewValue: 'Dimensions Competency' },
    { value: 5, viewValue: 'Elements Verbal' },
    { value: 10, viewValue: 'Elements Logical' },
    { value: 14, viewValue: 'Drives' },
    { value: 15, viewValue: 'Aspects Numerical' },
    { value: 16, viewValue: 'Aspects Verbal' },
    { value: 17, viewValue: 'Aspects Checking' },
    { value: 20, viewValue: 'SJT' },
    { value: 23, viewValue: 'Kf4d Behaviours' },
    { value: 24, viewValue: 'Kf4d Drivers' },
    { value: 25, viewValue: 'Kf4d Traits' },
    { value: 26, viewValue: 'Experiences' },
    { value: 27, viewValue: 'Preferences' },
    { value: 28, viewValue: 'Abstract Reasoning' },
    { value: 30, viewValue: 'ELCA' },
    { value: 31, viewValue: 'Virtual Recruiter' },
    { value: 32, viewValue: 'Inclusive Leader Sjt' }
];

export const VALIDITY_PERIODS_PERIOD_TYPES: ConfigurationNumericOption[] = [
    { value: 1, viewValue: 'Day' },
    { value: 2, viewValue: 'Week' },
    { value: 3, viewValue: 'Month' },
    { value: 4, viewValue: 'Year' }
];
