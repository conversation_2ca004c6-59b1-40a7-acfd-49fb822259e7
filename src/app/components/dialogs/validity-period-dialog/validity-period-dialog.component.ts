import { Component, Inject, OnInit } from '@angular/core';
import {
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { MAT_DIALOG_DATA } from '@angular/material';

import { AssessmentValidityPeriod, AssessmentValidityPeriodData } from '@/shared/models/assessment-validity-period/assessment-validity-period';
import { ConfigurationNumericOption } from '@/shared/models';
import { VALIDITY_PERIODS_ASSESSMENT_TYPES, VALIDITY_PERIODS_PERIOD_TYPES } from './validity-period-dialog.metadata';

@Component({
  selector: 'validity-period-dialog',
  styleUrls: ['./validity-period-dialog.component.scss'],
  templateUrl: './validity-period-dialog.component.html',
})
export class ValidityPeriodDialogComponent implements OnInit {
  periodForm: FormGroup;
  type = 'Add';
  assessmentTypes: ConfigurationNumericOption[];
  assessmentValidityPeriod: AssessmentValidityPeriod;
  allAssessmentTypes = VALIDITY_PERIODS_ASSESSMENT_TYPES;
  periodTypes = VALIDITY_PERIODS_PERIOD_TYPES;
  durationMinValue: number = 1;
  durationMaxValue: number = 1000;

  constructor(@Inject(MAT_DIALOG_DATA) public data: AssessmentValidityPeriodData) {}

  ngOnInit() {
    const durationValidator = [Validators.required, Validators.min(this.durationMinValue), Validators.max(this.durationMaxValue)];
    this.type = this.data.type || this.type;
    this.assessmentTypes = this.allAssessmentTypes.filter(t => this.data.assessmentTypes.indexOf(t.value) >= 0);
    this.assessmentValidityPeriod = this.data.selectedPeriod;
    this.periodForm = new FormGroup({
      assessmentType: new FormControl('', [Validators.required]),
      periodType: new FormControl('', [Validators.required]),
      periodDuration: new FormControl('', durationValidator)
    });
    this.periodForm.get('periodDuration').setValue(this.assessmentValidityPeriod.periodDuration);
  }

  get formControls() {
    return this.periodForm.controls;
  }

  get updates() {
    if (this.periodForm.invalid) {
      return;
    }

    this.assessmentValidityPeriod.periodDuration = parseInt(this.periodForm.get('periodDuration').value);
    return this.assessmentValidityPeriod;
  }

  checkIntValue(event: any) {
    event.target.value = parseInt(event.target.value);
  }

  checkDurationInput(event: KeyboardEvent) {
    if (!(event.charCode > 47 && event.charCode < 58)) {
      event.preventDefault();
    }
  }
}
