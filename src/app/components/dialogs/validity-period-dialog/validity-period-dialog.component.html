<h2 mat-dialog-title>{{type}} validity period</h2>
<div class="form-title">
  <mat-icon>help_outline</mat-icon>
  All fields are required. 'Period duration' must be in the range from {{durationMinValue}} to {{durationMaxValue}}.
</div>
<mat-dialog-content>
  <form #createForm="ngForm"
        [formGroup]="periodForm">
    <div>
      <label>Assessment type</label>
      <mat-form-field>
        <mat-select class="form-group"
                    placeholder="Select assessment type"
                    [(ngModel)]="assessmentValidityPeriod.assessmentType"
                    formControlName="assessmentType">
          <mat-option class="form-control"
                      *ngFor="let type of assessmentTypes"
                      [value]="type.value">
            {{ type.viewValue }}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="formControls.assessmentType.errors?.required">required</mat-error>
      </mat-form-field>
    </div>
    <div>
      <label>Period type</label>
      <mat-form-field>
        <mat-select class="form-group"
                    placeholder="Select period type"
                    [(ngModel)]="assessmentValidityPeriod.periodType"
                    formControlName="periodType">
          <mat-option class="form-control"
                      *ngFor="let type of periodTypes"
                      [value]="type.value">
            {{ type.viewValue }}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="formControls.periodType.errors?.required">required</mat-error>
      </mat-form-field>
    </div>
    <div>
      <label>Period duration</label>
      <input type="numeric" formControlName="periodDuration" class="numericField" (blur) = "checkIntValue($event)" (keypress)="checkDurationInput($event)"/>
    </div>
  </form>
</mat-dialog-content>
<mat-dialog-actions>
  <button mat-button
          mat-dialog-close
          class="btn-secondary">Close</button>
  <button mat-button
          [mat-dialog-close]="updates"
          cdkFocusInitial
          class="btn-primary"
          [disabled]="this.periodForm.invalid">Save</button>
</mat-dialog-actions>
