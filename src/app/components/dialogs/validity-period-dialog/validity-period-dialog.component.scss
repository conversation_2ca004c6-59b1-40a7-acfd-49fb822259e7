@import "../../../../styles/typography.scss";

mat-dialog-content {
  flex-direction: column;

  form > div {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    label {
      display: flex;
      align-self: center;
      margin-right: 50px;
    }
  }
}

mat-dialog-actions {
  justify-content: flex-end;

  button {
    &[disabled]{
      cursor: auto;
      background-color: $disabled-color;
      border-color: rgba(0, 0, 0, 0.1);
      color: $primary--grey;
    }
  }
}

mat-option {
  font: unquote($proxima-font);
}

.form-title {
  vertical-align: top;
  color: $primary--grey;
  font-size: 13px;
  margin-bottom: 12px;
  opacity: .85;

  mat-icon {
    font-size: 20px;
    line-height: 20px;
    height: 20px;
  }
}
