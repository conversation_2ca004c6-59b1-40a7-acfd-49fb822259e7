@import "../../../../styles/colors.scss";
@import "../../../../styles/typography.scss";

h3 {
  margin: 0 !important;
  display: flex;

  span.type {
    font-size: .9rem;
    vertical-align: middle;
    margin-left: auto;
  }
}

.mat-dialog-actions {
  justify-content: flex-end;

  button {
    // TODO create shared component for properly styled button
    cursor: pointer;
    display: inline-block;
    padding: 6px 12px;
    margin-right: 12px;

    white-space: nowrap;
    vertical-align: middle;

    font: unquote($proxima-font);
    font-size: 0.9em;
    font-weight: bold;
    letter-spacing: 0.5px;
    text-transform: uppercase;

    background: white;
    color: $primary--blue;
    border: 1px solid $primary--blue;

    &:hover {
      background: $primary--blue;
      color: white;
    }

    .mat-icon {
      margin-right: 12px;
      vertical-align: middle;
      color: inherit;
    }
  }
}

.menu-button {
  width: 150px;
  color: $primary--blue-medium;
  .mat-select-arrow {
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid $primary--blue-medium;
    margin: 0 6px;
    display: inline-block;
    vertical-align: middle;
  }
}

.template-options {
  line-height: 2;
  padding-bottom: 12px;
  border-bottom: solid thin lightgrey;
  margin-bottom: 12px;

  h4 {
    margin-bottom: 6px;
  }
}

.subject {
  line-height: 2;
  margin-bottom: 12px;
}

.body {
  max-height: 330px;
  overflow: auto;
}

pre {
  font-family: monospace;
  font-weight: 400;
  line-height: 1.4em;
  font-size: 12px;
  background: #eee;
  padding: 12px 24px 12px 12px;
  opacity: 0.9;
  min-width: 0;
  max-width: 980px;
  max-height: 300px;
  overflow: auto;
}


.table-actions-panel {
  display: flex;
  height: 48px;
  padding: 12px 0;
  flex-direction: row;
  justify-content: stretch;
  align-items: center;
  user-select: none;

  a {
    display: inline-block;
    font-weight: bold;
    padding: 3px 6px;
    white-space: nowrap;
    vertical-align: middle;
    color: $primary--blue;
    font-size: 0.9em;
  }

  .left-side {
    a {
      font-size: 12px;
      font-weight: bold;
      font-style: normal;
      font-stretch: normal;
      letter-spacing: 1px;
      text-transform: uppercase;
      position: relative;
      padding: 6px 0;
      vertical-align: middle;
      margin-right: 48px;

      &.active {
        &::after {
          content: "";
          width: 22px;
          height: 2px;
          position: absolute;
          background: $primary--blue;
          bottom: 3px;
          left: 0;
        }
      }
    }
  }
}
