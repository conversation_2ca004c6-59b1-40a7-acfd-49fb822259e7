<h3 mat-dialog-title>
  {{ schedule.settingName }}
  <span class="type grey">
    {{ schedule.templateType }} :: {{ schedule.settingType }}
  </span>
</h3>

<div class="table-actions-panel">
  <div class="left-side">
    <a
      [class.active]="currentTab === EmailSchedulePreviewTab.TemplateView"
      (click)="currentTab = EmailSchedulePreviewTab.TemplateView"
    >
      Template View
    </a>
    <a
      [class.active]="currentTab === EmailSchedulePreviewTab.ScheduleView"
      (click)="currentTab = EmailSchedulePreviewTab.ScheduleView"
    >
      Schedule View
    </a>
  </div>
</div>

<div
  *ngIf="currentTab === EmailSchedulePreviewTab.TemplateView"
  class="template-view"
>
  <div class="template-options">
    <h4>{{ templateInfo.name }}</h4>
    <div>Used in {{ templateInfo.defaultForProjectsCount }} project(s).</div>

    <mat-menu #languageMenu="matMenu" class="menu">
      <button
        mat-menu-item
        *ngFor="let template of templateInfo.templates"
        (click)="onTemplateChange(template)"
      >
        {{ template.locale | language }}
      </button>
    </mat-menu>

    <span>
      Available languages:
      <a class="menu-button" [matMenuTriggerFor]="languageMenu">
        {{ selectedTemplate.locale | language }}
        <span class="mat-select-arrow"></span>
      </a>
    </span>
  </div>

  <div class="subject">
    Subject: <b>{{ selectedTemplate.subject }}</b>
  </div>

  <div class="body" [innerHTML]="selectedTemplate.body"></div>
</div>

<pre *ngIf="currentTab === EmailSchedulePreviewTab.ScheduleView">
  {{ scheduleJSON }}
</pre>

<mat-dialog-actions>
  <button mat-button mat-dialog-close>Close</button>
</mat-dialog-actions>
