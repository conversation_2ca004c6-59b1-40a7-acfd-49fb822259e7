import { ProjectEmailSchedule } from '@/shared/models/ProjectEmailSchedule';
import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';

export enum EmailSchedulePreviewTab {
  TemplateView,
  ScheduleView
}

@Component({
  selector: 'app-email-template',
  templateUrl: './email-template.component.html',
  styleUrls: ['./email-template.component.scss'],
})
export class EmailTemplateComponent implements OnInit {
  templateInfo: any;
  selectedTemplate: any;
  schedule: ProjectEmailSchedule;
  currentTab: EmailSchedulePreviewTab = EmailSchedulePreviewTab.TemplateView;

  EmailSchedulePreviewTab = EmailSchedulePreviewTab;

  constructor(
    public dialogRef: MatDialogRef<EmailTemplateComponent>,
    @Inject(MAT_DIALOG_DATA) private data: any
  ) {}

  ngOnInit() {
    this.templateInfo = this.data.template;
    this.schedule = this.data.schedule;
    this.selectedTemplate = this.templateInfo.templates.find(
      (t) => t.locale === this.templateInfo.defaultLocale
    ) || this.templateInfo.templates[0];
  }

  onTemplateChange(template) {
    this.selectedTemplate = template;
  }

  get scheduleJSON() {
    return JSON.stringify(this.schedule, null, 3);
  }
}
