import { KFTableColumn } from '@/components/controls';
import { ProjectService } from '@/services';
import { CandidateJobLogRecord, CandidateJobRecord } from '@/shared/models';
import { CandidateLogRecord } from '@/shared/models/CandidateLogRecord';
import {
  Component,
  Inject,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { CandidateLogDetailsComponent } from '../candidate-log-details/candidate-log-details.component';

@Component({
  selector: 'app-candidate-job-details',
  templateUrl: './candidate-job-details.component.html',
  styleUrls: ['./candidate-job-details.component.scss'],
})
export class CandidateJobDetailsComponent implements OnInit {
  @ViewChild('LEVEL_TPL') LEVEL_TPL: TemplateRef<any>;
  columns: KFTableColumn<CandidateJobLogRecord>[];
  rowClasses: any;

  constructor(
    public dialogRef: MatDialogRef<CandidateJobDetailsComponent>,
    public projectService: ProjectService,
    @Inject(MAT_DIALOG_DATA) public data: CandidateJobRecord,
    public dialog: MatDialog
  ) {}

  ngOnInit() {
    this.columns = this.logColumns;
    this.rowClasses = this.logsRowClasses;
  }

  get logsRowClasses() {
    return {
      default: true,
      error: (row: CandidateJobLogRecord) => row.level.name === 'Error',
      fatal: (row: CandidateJobLogRecord) => row.level.name === 'Fatal',
      warn: (row: CandidateJobLogRecord) => row.level.name === 'Warn',
    };
  }

  get logColumns(): KFTableColumn<CandidateJobLogRecord>[] {
    return [
      {
        type: 'text',
        name: 'logId',
        label: 'Log ID',
        sortable: true,
        click: (item: CandidateJobLogRecord) => {
          this.projectService
            .getCandidateLogDetails(
              this.data.request.requestorUserId,
              item.logId,
              item.created
            )
            .subscribe((logDetails) => {
              this.dialog.open(CandidateLogDetailsComponent, {
                width: '1250px',
                data: logDetails,
              });
            });
        },
        width: '100px',
      },
      {
        type: 'textCapitalized',
        name: 'level',
        label: 'level',
        template: this.LEVEL_TPL,
        width: '100px',
      },
      {
        type: 'date',
        name: 'created',
        label: 'created',
        dateFormat: 'dd MMM yyyy, HH:mm:ss',
        sortable: true,
        width: '200px',
      },
      {
        type: 'text',
        name: 'logger',
        label: 'logger',
      },
      {
        type: 'text',
        name: 'message',
        label: 'message',
      },
    ];
  }

  convertToJson(content) {
    return JSON.parse(content);
  }
}
