<h3 mat-dialog-title>JOB {{ data.jobId }}</h3>

<div class="body">
  <table class="job-info">
    <tr>
      <td>Sent</td>
      <td>{{ data.request.sent | date: "dd MMMM yyyy, HH:mm:ss" }}</td>
      <td>Status</td>
      <td>{{ data.status }}</td>
      <td>Retry attempts</td>
      <td>{{ data.details.retryAttempts }}</td>
    </tr>
  </table>

  <pre class="request">{{data.details.request}}</pre>
  <div class="table-container">
    <kf-table
      *ngIf="data && data.logs"
      size="small"
      [data]="data.logs || []"
      [columns]="columns"
      [rowClasses]="rowClasses"
    ></kf-table>
  </div>
</div>

<mat-dialog-actions>
  <button class="btn btn-secondary" mat-dialog-close="true" mat-button>
    <mat-icon>cached</mat-icon> Retry Job
  </button>
  <button class="btn btn-primary" mat-button mat-dialog-close>Close</button>
</mat-dialog-actions>

<ng-template #LEVEL_TPL let-cell>
  <span class="level {{ cell.element.level.name | lowercase }}">
    {{ cell.element.level.name }}
  </span>
</ng-template>
