@import "../../../../styles/colors.scss";
@import "../../../../styles/typography.scss";

h3 {
  margin: 0;
}

.mat-dialog-actions {
  justify-content: flex-end;
}

.menu-button {
  width: 150px;
  color: $primary--blue-medium;
  .mat-select-arrow {
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid $primary--blue-medium;
    margin: 0 6px;
    display: inline-block;
    vertical-align: middle;
  }
}

table.headers {
  tr {
    td {
      line-height: 1.8em;

      &.key {
        font-weight: bold;
        padding-right: 24px;
      }

      &.value {
        &.red {
          color: $secondary--red;
        }
        &.green {
          color: $secondary--green-dark;
        }
        &.yellow {
          color: $secondary--orange;
        }
      }
    }
  }
}

pre.request {
  font-family: monospace;
  font-weight: 400;
  line-height: 1.4em;
  font-size: 13px;
  background: rgba(#eee, 0.5);
  padding: 12px 24px 12px 12px;
  max-height: 300px;
  margin-bottom: 24px;
  overflow: auto;
}

.table-container {
  box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.1);
}

:host {
  .level {
    margin: -2px -4px;
    padding: 2px 4px;
    border-radius: 3px;

    &.trace {
      color: gray;
    }

    &.debug {
      color: blue;
    }

    &.info {
      color: black;
    }

    &.warn {
      background-color: #dddd09;
    }

    &.error {
      color: red;
    }

    &.fatal {
      background-color: red;
      color: white;
    }

    &.processing {
      color: gray;
    }

    &.completed {
      color: green;
    }

    &.failed,
    &.failandretry {
      color: red;
    }

    &.delayandretry {
      color: darkblue;
    }
  }

  .job-info {
    margin-bottom: 24px;

    td:nth-child(even) {
      font-weight: bold;
      padding-right: 48px;
      padding-left: 8px;
    }
  }
}
