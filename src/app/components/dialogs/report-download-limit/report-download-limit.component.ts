import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA } from '@angular/material';

@Component({
  selector: 'app-report-download-limit',
  templateUrl: './report-download-limit.component.html',
  styleUrls: ['./report-download-limit.component.scss']
})
export class ReportDownloadLimitComponent {

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: ReportDownLoadLimitData
  ) { }

}

export class ReportDownLoadLimitData {
  reportCount: number;
  reportDownloadLimit: number
}