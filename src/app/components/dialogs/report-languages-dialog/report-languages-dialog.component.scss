@import "../../../../styles/colors.scss";
@import "../../../../styles/theme.scss";

:host {
  .details-row {
    color: red;
  }

  h3 {
    color: $primary--blue;
    margin: 0;
    margin-bottom: 24px;
  }

  .table-container {
    margin: 32px 64px;
    box-shadow: 0 0 1px rgba(0, 0, 0, 0.3);
  }

  .btn {
    margin-top: 24px;
  }

  .flex {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
    width: 100%;
  }
}

.menu-button {
  min-width: 220px;
  background: white;
  margin-right: 12px;
  padding: 16px;
  border: thin solid rgba(145, 145, 145, 0.3);
  letter-spacing: 0.4px;
  color: $dark-primary-text;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .mat-select-arrow {
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid $primary--blue-medium;
    margin: 0 6px;
    display: inline-block;
    vertical-align: middle;
  }
}

::ng-deep .mat-menu-content {
  max-height: 300px;
  overflow: auto;

  /* width */
  &::-webkit-scrollbar {
    width: 8px;
  }

  /* Track */
  &::-webkit-scrollbar-track {
    background: $primary--grey-light;
  }

  /* Handle */
  &::-webkit-scrollbar-thumb {
    background: $primary--grey-medium;
  }

  /* Handle on hover */
  &::-webkit-scrollbar-thumb:hover {
    background: $primary--grey-dark;
  }
}
