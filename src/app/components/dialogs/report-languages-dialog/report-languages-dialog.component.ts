import { LanguagesService, ProjectService } from '@/services';
import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';

@Component({
  selector: 'app-report-status',
  styleUrls: ['./report-languages-dialog.component.scss'],
  templateUrl: './report-languages-dialog.component.html',
})
export class ReportLanguagesDialogComponent implements OnInit {
  languages: any;
  options: { id: string | number; value: string }[];
  response: { id: string | number; value: string }[] = [];
  allLanguages: any;

  constructor(
    public projectService: ProjectService,
    public dialogRef: MatDialogRef<any>,
    public languageService: LanguagesService,
    @Inject(MAT_DIALOG_DATA) public data: ReportLanguagesData
  ) {}

  ngOnInit() {
    this.languages = this.data.languages;
    this.allLanguages = this.languageService.getLanguages();
    this.options = this.data.languages.map((x: any) => ({
      id: x.languageCode,
      value: this.languageService.getLanguageNameByLocale(x.languageCode),
      disabled: this.data.disabledIf && this.data.disabledIf(x),
    }));
  }

  onFilterChange(event) {
    this.response = event.map((x: any) =>
      this.languageService.getLanguageByLocale(x)
    );
  }
}

export class ReportLanguagesData {
  action: string;
  languages: string[];
  disabledIf: (x) => boolean;
}
