<h2 mat-dialog-title>Delete Project</h2>

<div class="mat-dialog-content">
  <p>
    Are you sure you want to delete <strong>{{ data.projectName }}</strong>?
  </p>

   <div class="warning-message">
    <mat-icon color="warn" class="warning-icon">warning</mat-icon>
    <span>This action cannot be undone.</span>
  </div>

 <mat-form-field appearance="fill" style="width: 100%;">
    <input
      matInput
      [(ngModel)]="confirmationText"
      [disabled]="isLoading"
      placeholder='Type "Delete" to confirm'
      autocomplete="off"
    />
  </mat-form-field>
</div>

<div mat-dialog-actions class="actions-panel">
  <button
    mat-raised-button
    color="warn"
    (click)="confirmDelete()"
    [disabled]="confirmationText !== 'Delete' || isLoading"
  >
    <span *ngIf="!isLoading">Delete</span>
  </button>
  <button mat-button class="btn btn-secondary" (click)="dialogRef.close()" [disabled]="isLoading">Cancel</button>
</div>
