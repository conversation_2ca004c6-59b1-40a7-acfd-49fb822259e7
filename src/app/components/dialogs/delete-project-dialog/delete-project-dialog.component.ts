import { Component, Inject } from "@angular/core";
import { MatDialogRef, MAT_DIALOG_DATA } from "@angular/material/dialog";
import { ProjectService } from "@/services/project.service";
import { SpinnerService } from "@/services";

@Component({
  selector: "app-delete-project-dialog",
  templateUrl: "./delete-project-dialog.component.html",
  styleUrls: ["./delete-project-dialog.component.scss"],
})
export class DeleteProjectDialogComponent {
  confirmationText = "";
  isLoading = false;
  errorMessage = "";

  constructor(
    public dialogRef: MatDialogRef<DeleteProjectDialogComponent>,
    @Inject(MAT_DIALOG_DATA)
    public data: { projectId: number; projectName: string },
    private projectService: ProjectService,
    private spinnerService: SpinnerService
  ) {}

  confirmDelete() {
    if (this.confirmationText !== "Delete") return;

    this.isLoading = true;
    this.spinnerService.activate();

    this.projectService.deleteProject(this.data.projectId).subscribe({
      next: () => {
        this.isLoading = false;
        this.spinnerService.deactivate();
        this.dialogRef.close("deleted");
      },
      error: (err) => {
        this.spinnerService.deactivate();
        this.isLoading = false;
        this.errorMessage =
          (err && err.message) || "Failed to delete project. Please try again.";
        this.dialogRef.close({ error: this.errorMessage });
      },
    });
  }
}
