import { AlertService, ClientReportsService } from '@/services';
import {
  ClientReportActions,
  ReportConfiguration,
  ReportDisplayOrder,
} from '@/shared/models';
import { moveItemInArray, CdkDragDrop } from '@angular/cdk/drag-drop';
import { Component, Inject, OnDestroy, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { Subscription } from 'rxjs';

export class ReportsDisplayOrderData {
  order: ReportConfiguration[];
}
/**
 * Component to configure report display order.
 */
@Component({
  selector: 'app-reports-display-order',
  styleUrls: ['./reports-display-order.component.scss'],
  templateUrl: './reports-display-order.component.html',
})
export class ReportsDisplayOrderComponent implements OnInit, OnDestroy {
  currentSubscription: Subscription;
  reportsDisplayOrder: ReportDisplayOrder[];
  originalReportsDisplayOrder: ReportDisplayOrder[];
  constructor(
    public dialogRef: MatDialogRef<ReportsDisplayOrderComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ReportsDisplayOrderData,
    private clientReportService: ClientReportsService,
    private alertService: AlertService
  ) {}

  ngOnInit() {
    this.originalReportsDisplayOrder = this.data.order
      .map((reportConfig) => new ReportDisplayOrder(reportConfig))
      .sort((a, b) => a.originalDisplayOrder - b.originalDisplayOrder);
    this.reportsDisplayOrder = this.originalReportsDisplayOrder.slice();
  }

  drop(event: CdkDragDrop<string[]>) {
    moveItemInArray(
      this.reportsDisplayOrder,
      event.previousIndex,
      event.currentIndex
    );
  }

  /**
   * Return to report display page.
   */
  back() {
    this.clientReportService.state(ClientReportActions.showReports);
  }

  /**
   * Reset the display to original order.
   */
  reset() {
    this.reportsDisplayOrder = this.originalReportsDisplayOrder.slice();
  }

  /**
   * Saves the updated display order.
   */
  save() {
    this.dialogRef.close(this.reportsDisplayOrder);
  }

  /**
   * Check if the display order was updated.
   */
  canSave() {
    return (
      JSON.stringify(this.originalReportsDisplayOrder) !==
      JSON.stringify(this.reportsDisplayOrder)
    );
  }

  ngOnDestroy() {
    // unsubscribe to ensure no memory leaks
    if (this.currentSubscription) {
      this.currentSubscription.unsubscribe();
    }
  }
}
