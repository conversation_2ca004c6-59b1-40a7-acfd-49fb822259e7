@import '../../../../styles/colors.scss';

.flex-row {
  display: flex;
  align-items: center;
  flex-direction: row;
}

.info {
  padding: 12px 48px;
  .info-text {
    margin-bottom: 25%;
    font-size: 9pt;
    text-align: center;
  }
  .btn {
    margin-bottom: 3px;
  }
  img {
    height: 65px;
    margin: 24px auto;
    text-align: center;
    display: block;
    opacity: .08;
    user-select: none;
    -webkit-user-drag: none;
  }
}
// TODO: replace with proper scss styling.
.drag-list {
  flex: 0 0 360px;
  max-width: 100%;
  display: block;
  background: white;
}

.drag-box {
  .order-number {
    color: $primary--blue;
    margin-right: 12px;
    min-width: 16px;
  }
  padding: 8px 12px;
  margin: 6px 0;
  border-left: solid 3px $primary--blue;
  color: rgba(0, 0, 0, 0.87);
  background: white;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  box-sizing: border-box;
  font-weight: 600;
  cursor: move;

  &:hover {
    box-shadow: 0 0 6px 0 rgba($primary--blue-light, 0.7);
  }
}

.cdk-drag-preview {
  .order-number {
    color: $primary--blue-light;
  }
  border-left: solid 3px $primary--blue-light;
  // filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.27));
  box-shadow: 0 0 10px 0 rgba($primary--blue-light, 0.7);
  color: rgba(0, 0, 0, 0.67);
}

.cdk-drag-placeholder {
  opacity: 0;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.drag-list.cdk-drop-list-dragging .drag-box:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}
