<div class="flex-row">
  <div class="info">
    <div class="info-text">
      <img src="assets/images/drag-drop.svg" alt="" />
      Update report display order by <b>dragging and dropping</b> to desired location
    </div>
    <button class="btn btn-primary" [disabled]="!canSave()" (click)="save()">
      Save
    </button>
    <button class="btn btn-secondary" [disabled]="!canSave()" (click)="reset()">
      Reset
    </button>
  </div>

  <div cdkDropList class="drag-list" (cdkDropListDropped)="drop($event)">
    <div class="drag-box" *ngFor="let report of reportsDisplayOrder; index as i" cdkDrag cdkDragLockAxis="y">
      <span class="order-number">{{ i + 1 }}</span> {{ report.reportName }}
    </div>
  </div>
</div>
