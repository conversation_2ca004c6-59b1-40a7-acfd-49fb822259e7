<ng-template #text let-cell>
  {{ cell.element[cell.columnName] }}
</ng-template>

<ng-template #array let-cell>
  <mat-chip-list>
    <mat-chip *ngFor="let item of cell.element[cell.columnName]">{{
      getValue(item, cell.path)
    }}</mat-chip>
  </mat-chip-list>
</ng-template>

<ng-template #boolean let-cell>
  <mat-icon *ngIf="cell.element[cell.columnName]">check</mat-icon>
</ng-template>

<ng-template #rowOptions let-cell>
  <mat-menu #actionsMenu="matMenu">
    <button mat-menu-item (click)="edit(cell.element)">Edit</button>
    <button mat-menu-item (click)="openConfirmDialog(cell.element)">
      Delete
    </button>
  </mat-menu>

  <button
    [allowedRoles]="['admin', 'reportManagement']"
    mat-icon-button
    [matMenuTriggerFor]="actionsMenu"
    matTooltip="Open actions menu"
    *ngIf="!filtersInheritedFromParent"
  >
    <mat-icon>more_vert</mat-icon>
  </button>
</ng-template>

<div class="page-content glass">
  <div class="table-options">
    <div class="breadcrumbs">
      <b>
        <a (click)="back()">
          Reports
          <mat-icon>chevron_right</mat-icon>
        </a>
        <span> {{ selectedReportName }}</span>
      </b>
    </div>
    <a
      [allowedRoles]="['admin', 'reportManagement']"
      *ngIf="!filtersInheritedFromParent"
      (click)="add()"
    >
      <mat-icon>add</mat-icon>
      <b> Add new filter </b>
    </a>
    <a
      [allowedRoles]="['admin', 'reportManagement']"
      *ngIf="filtersInheritedFromParent"
      (click)="override()"
    >
      <mat-icon>settings_backup_restore</mat-icon>
      <b> Override parent filters </b>
    </a>
  </div>

  <div class="table-container">
    <mat-table [dataSource]="reportFilters" *ngIf="!loading">
      <ng-container
        *ngFor="let column of filtersColumns"
        [cdkColumnDef]="column.name"
      >
        <mat-header-cell *cdkHeaderCellDef [ngClass]="column.type">{{
          column.label
        }}</mat-header-cell>
        <mat-cell *cdkCellDef="let element" [ngClass]="column.type">
          <ng-container
            *ngTemplateOutlet="
              getTemplate(column.type);
              context: {
                $implicit: {
                  element: element,
                  columnName: column.name,
                  path: column.path
                }
              }
            "
          ></ng-container>
        </mat-cell>
      </ng-container>

      <mat-header-row *cdkHeaderRowDef="displayedColumns"></mat-header-row>
      <mat-row *cdkRowDef="let row; columns: displayedColumns"></mat-row>
    </mat-table>

    <mat-paginator
      [pageSize]="10"
      [pageSizeOptions]="[5, 10, 20]"
      [showFirstLastButtons]="true"
      *ngIf="!loading"
    ></mat-paginator>
  </div>
</div>
