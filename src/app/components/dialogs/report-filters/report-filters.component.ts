import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { MatDialog, MatDialogRef, MatTableDataSource } from '@angular/material';
import { throwError, Subscription } from 'rxjs';

import { ConfirmationDialogComponent } from '@/components/controls/confirmation-dialog/confirmation-dialog.component';
import {
  AlertService,
  ClientReportsService,
  ReportFilterService,
  SpinnerService
} from '@/services';
import {
  ClientReportActions,
  ConfigurationOption,
  FilterOverrideRequest,
  ReportFilter,
  ReportFilterOptions
} from '@/shared/models';
import { ReportFilterDataWrapper } from '@/shared/models/reports/reportFilterData';
import { FiltersDialogComponent } from '../filters-dialog/filters-dialog.component';
import { FILTERS_COLUMNS } from './report-filters.columns';

/**
 * Component to display/update report filters.
 */
@Component({
  selector: 'app-report-filters',
  styleUrls: ['./report-filters.component.scss'],
  templateUrl: './report-filters.component.html'
})
export class ReportFiltersComponent implements OnInit {
  currentSubscription: Subscription;
  reportFilters: MatTableDataSource<ReportFilter> = new MatTableDataSource<ReportFilter>();
  filtersColumns = FILTERS_COLUMNS;
  displayedColumns = FILTERS_COLUMNS.map((c) => c.name);
  selectedReportName: string;
  filtersInheritedFromParent: boolean;
  reportFilterOptions: ReportFilterOptions = new ReportFilterOptions();
  reportFilterTypes: ConfigurationOption[] = this.reportFilterOptions.allFilters.map(r => r.filterType);

  // Report filters configuration options.
  filterValues: ConfigurationOption[];
  selectedFilter: ReportFilter = new ReportFilter();
  valueSelectDropdown = false;
  dialogRef: MatDialogRef<any, any>;
  loading = false;

  @ViewChild('text') text: TemplateRef<any>;
  @ViewChild('array') array: TemplateRef<any>;
  @ViewChild('rowOptions') rowOptions: TemplateRef<any>;
  @ViewChild('boolean') boolean: TemplateRef<any>;

  constructor(
    private clientReportService: ClientReportsService,
    private reportFilterService: ReportFilterService,
    private spinnerService: SpinnerService,
    private alertService: AlertService,
    public dialog: MatDialog
  ) {
  }

  ngOnInit() {
    this.spinnerService.activate();
    this.currentSubscription = this.clientReportService.reportConfigurations
      .subscribe(
        result => {
          if (!this.clientReportService.selectedReportId) {
            throwError('Report filter cannot be shown as  selected - report id is not set');
          }
          const report = result.find(x => x.id === this.clientReportService.selectedReportId);
          this.reportFilters.data = report.reportFilters;
          this.selectedReportName = report.reportKey;
          this.filtersInheritedFromParent = report.filtersInhertitedFromParent;
          this.spinnerService.deactivate();
        },
        error => {
          this.alertService.error(error.message);
          this.spinnerService.deactivate();
        }
      );
  }

  openDialog(data: ReportFilterDataWrapper) {
    this.dialogRef = this.dialog.open(FiltersDialogComponent, data);
    this.dialogRef.afterClosed()
      .subscribe(updates => {
        if (!updates) {
          this.resetSelection();
        } else {
          this.selectedFilter = { ...this.selectedFilter, ...updates };
          this.save();
        }
      });
  }

  /**
   * Return to report display page.
   */
  back() {
    this.clientReportService.state(ClientReportActions.showReports);
  }

  /**
   * Shows edit existing report filters.
   * @param  filter the filter being edited.
   */
  edit(filter: ReportFilter) {
    this.selectedFilter = filter;
    this.openDialog({
      data: {
        selectedFilter: this.selectedFilter,
        reportName: this.selectedReportName,
        options: this.reportFilterOptions,
        type: 'Edit',
        valueSelectDropdown: this.valueSelectDropdown,
        clientId: this.clientReportService.clientId
      }
    });
  }

  /**
   * Shows dialog for a new report filter  control.
   */
  add() {
    this.openDialog({
      data: {
        type: 'Add',
        selectedFilter: new ReportFilter(),
        selectedReportName: this.selectedReportName,
        options: this.reportFilterOptions,
        clientId: this.clientReportService.clientId
      }
    });

  }

  /**
   * If a given clients report filters are inherited from parent client, all the inherited filters are re created for the client.
   * This will allow the client to edit them or add new ones.
   */
  override() {
    const overrideRequest = new FilterOverrideRequest(
      this.clientReportService.clientId,
      this.clientReportService.selectedReportId
    );
    this.reportFilterService.OverideInheritedFilters(overrideRequest).subscribe(
      () => {
        this.alertService.success('filter override successful');
        this.clientReportService.loadClientReports(
          this.clientReportService.clientId,
          false
        );
      },
      error => {
        this.alertService.error(error.message);
      }
    );
  }

  /**
   * Open modal window for confirmation
   */

  openConfirmDialog(filter: ReportFilter) {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: '250px',
      data: { action: 'delete' }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.delete(filter);
      }
    });
  }

  /**
   * Delete a filter.
   */

  delete(filter: ReportFilter) {
    this.reportFilterService.deleteFilter(filter.filterId)
      .subscribe(
        () => {
          this.alertService.success('filter deleted');
          this.clientReportService.loadClientReports(
            this.clientReportService.clientId,
            false
          );
        },
        error => {
          this.alertService.error(error.message);
        }
      );
  }

  /**
   * Removes the filter from local collection.
   * @param filterId the filter to be deleted.
   */
  deleteLocal(filterId: number) {
    const data = this.reportFilters.data;
    data.forEach((item, index) => {
      if (item.filterId === filterId) {
        data.splice(index, 1);
      }
    });
    this.reportFilters.data = data;
  }

 /**
 * Saves a report filter. The backend expects filter values to be separated by pipes ('|'), 
 * so commas in the filterValues string are replaced with pipes before sending.
 */
  save() {
    const {clientId, selectedReportId: reportId} = this.clientReportService;
    const reportFilter = {...this.selectedFilter, clientId, reportId};
    reportFilter.filterValues = reportFilter.filterValues.replace(/,/g, '|');
    const {filterId} = this.selectedFilter;
    if (filterId) {
      this.updateFilter({...reportFilter, filterId: this.selectedFilter.filterId});
    } else {
      this.addFilter(reportFilter);
    }
  }

  /**
   * Updates a report filter.
   */
  updateFilter(reportFilter) {
    this.reportFilterService.UpdateFilter(reportFilter)
      .subscribe(
        () => {
          this.alertService.success('Filter updated');
          this.clientReportService.loadClientReports(
            this.clientReportService.clientId,
            false
          );
          this.resetSelection();
        },
        error => {
          this.alertService.error(error.message);
        }
      );
  }

  /**
   * Creates a report filter.
   */
  addFilter(reportFilter) {
    this.reportFilterService.AddFilter(reportFilter)
      .subscribe(
        result => {
          this.alertService.success('Report filter added');
          this.clientReportService.loadClientReports(
            this.clientReportService.clientId,
            false
          );
          this.resetSelection();
        },
        error => {
          this.alertService.error(error.message);
        }
      );
  }

  // Reset selected values for report filter control.
  resetSelection() {
    this.selectedFilter = new ReportFilter();
  }

  // returns column template
  getTemplate(id: string) {
    return this[id] || this.text;
  }

  // returns value from array of objects by path if it has been passed and returns array item if hasn't
  getValue(item, path: string) {
    if (!path) {
      return item.toString();
    }
    const props = path.split('.');
    return props.reduce((prev, curr) => prev[curr], item);
  }

}
