@import "../../../../styles/typography.scss";

$columns-min-widths: (
  filterValuesText: 520px,
);
$columns-max-widths: (
  filterId: 50px,
  actions: 90px,
);

@each $name, $width in $columns-min-widths {
  .mat-column-#{$name} {
    min-width: $width;
  }
}

@each $name, $width in $columns-max-widths {
  .mat-column-#{$name} {
    max-width: $width;
  }
}

.table-options {
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0;
}
