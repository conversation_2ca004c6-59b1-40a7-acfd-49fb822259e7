<div class="header">
  <h3>PROCTORING RESULTS</h3>
  <button class="btn btn-primary glass resync-button" (click)="resyncScores()" [disabled]="!isResyncAllowed()"  title="Resync Scores">Resync Scores</button>
</div>
<div class="assessment-table">
  <kf-table [data]="proctoringResults" [columns]="columns"></kf-table>
</div>
<div class="iframe-container" *ngIf="reviewLink">
  <button class="close-btn" (click)="closeIframe()">&times;</button>
  <iframe [src]="reviewLink" frameborder="0"></iframe>
</div>

<ng-template #reviewLinkTemplate let-cell>
  <div class="proctoring-result">
    <ng-container *ngIf="cell.element.isProctoringResultsAvailable; else noLink">
      <a href="#" (click)="proctoringReviewandDownload(cell.element); $event.preventDefault()">
        Review Session
      </a>
    </ng-container>
    <ng-template #noLink>
      <span class="no-link">Review Session</span>
    </ng-template>
  </div>
</ng-template>

<ng-template #PARTICIPANT_PROCTORING_TPL let-cell>
  <div class="proctoring-score" [ngSwitch]="cell.element.score">
    <div *ngSwitchCase="'Undefined'" class="pill pill-default">
      <span>-</span>
    </div>
    <div *ngSwitchCase="'Low'" class="pill pill-low">
      <span>Low</span>
    </div>
    <div *ngSwitchCase="'Medium'" class="pill pill-medium">
      <span>Medium</span>
    </div>
    <div *ngSwitchCase="'High'" class="pill pill-high">
      <span>High</span>
    </div>
    <div *ngSwitchDefault class="pill pill-default">
      <span>-</span>
    </div>
  </div>
</ng-template>