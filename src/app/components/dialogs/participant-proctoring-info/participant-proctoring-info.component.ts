import { KFTableColumn } from '@/components/controls/kf-table/kf-table.component';
import { ReportDisplayOrder } from '@/shared/models';
import { Component, Inject, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { Subscription } from 'rxjs';

import {
  ParticipantInfo,
  ProctoringResult,
  Assessment
} from '@/shared/models/projects/participants';

import { ParticipantService } from '@/services/participant.service';
import {
  AlertService,
  SpinnerService,
} from '@/services';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';


@Component({
  selector: "app-participant-proctoring-info",
  styleUrls: ["./participant-proctoring-info.component.scss"],
  templateUrl: "./participant-proctoring-info.component.html",
})
export class ParticipantProctoringInfoComponent implements OnInit {
  @ViewChild("reviewLinkTemplate") reviewLinkTemplate: TemplateRef<any>;
  @ViewChild("PARTICIPANT_PROCTORING_TPL") PARTICIPANT_PROCTORING_TPL: TemplateRef<any>;
  columns: KFTableColumn<any>[] = [
    {
      label: "Assessment",
      name: "name",
      type: "text",
      sortable: true,
    },
    {
      label: "Proctoring Score",
      name: "score",
      type: "text",
      sortable: true,
    },
    {
      label: "Review Links",
      name: "reviewLink",
      type: "text",
      sortable: false,
      template: null,
    },
  ];
  proctoringResults: Assessment[] = [];
  reviewLink: SafeResourceUrl | null = null;
  isResyncInProgress = false;
  constructor(
    public dialogRef: MatDialogRef<ParticipantProctoringInfoComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private participantService: ParticipantService,
    private alertService: AlertService,
    private spinnerService: SpinnerService,
    private sanitizer: DomSanitizer
  ) {}

  ngOnInit() {
    this.proctoringResults = this.mapAssessments(this.data.proctoringResult.assessments);

    this.columns.forEach((column) => {
      if (column.name === "reviewLink") {
        column.template = this.reviewLinkTemplate;
      }
      if (column.name === "score") {
        column.template = this.PARTICIPANT_PROCTORING_TPL;
      }
    });
  }

  resyncScores() {
    this.isResyncInProgress = true; 
    this.spinnerService.activate();
    this.participantService
      .resyncProctoringScores(this.data.participant.participantId, this.data.projectId)
      .subscribe({
        next: () => {
          // Immediately fetch the updated scores
          this.participantService
            .getParticipantProctoringResults(this.data.participant.participantId, this.data.projectId)
            .subscribe({
              next: (updatedResults: ProctoringResult) => {
                this.spinnerService.deactivate();
                this.isResyncInProgress = false;
  
                this.proctoringResults = Array.isArray(updatedResults.assessments)
                ? this.mapAssessments(updatedResults.assessments)
                : [];
  
                this.alertService.success("Proctoring Scores refreshed just now!");
              },
              error: (err) => {
                this.spinnerService.deactivate();
                this.isResyncInProgress = false; 
                console.error("Error fetching updated scores:", err);
                this.alertService.error("Failed to fetch updated scores. Please try again.");
              }
            });
        },
        error: (err) => {
          this.spinnerService.deactivate();
          this.isResyncInProgress = false; 
          console.error("Error during resync:", err);
          this.alertService.error("Unable to refresh Proctoring Scores. Please try again.");
        }
      });
  }

private mapAssessments(assessments: Assessment[]): Assessment[] {
  return assessments.map((assessment: Assessment) => {
    let formattedName = assessment.name;

    // Special mappings
    const AssessmentNameMap: Record<string, string> = {
      'Kf4d Behaviours': 'Competencies',
      'Kf4dBehaviours': 'Competencies',
      'EntryLevelCompetency': 'Competencies',
      'TechnicalSkillsInventory': 'Technical Self-Inventory',
      'InclusiveLeaderSjt': 'Inclusive Leader Situational Insight Tool',
    };

    // Use mapped name if available
    if (AssessmentNameMap[formattedName]) {
      formattedName = AssessmentNameMap[formattedName];
    } else {
      // Remove 'Kf4d' prefix if present
      formattedName = formattedName.replace(/^Kf4d\s*/i, '');
      // Add space before capital letters (camelCase to spaced words)
      formattedName = formattedName.replace(/([a-z])([A-Z])/g, '$1 $2');
    }

    return {
      ...assessment,
      reviewLink: "Review Session",
      name: formattedName,
    };
  });
}


  proctoringReviewandDownload(pResult: Assessment) {
    this.spinnerService.activate();
    this.participantService
      .getParticipantReviewLink(
        this.data.participant.participantId,
        pResult.assessmentId
      )
      .subscribe({
        next: (response: string) => {
          this.spinnerService.deactivate();
          // Validate if the response is a valid URL
          try {
            const url = new URL(response);
            this.reviewLink = this.sanitizer.bypassSecurityTrustResourceUrl(url.href);
          } catch (error) {
            console.warn("Invalid URL received:", response);
            this.alertService.error("Invalid URL received. Please try again.");
          }
        },
        error: (err) => {
          this.spinnerService.deactivate();
          console.error("Error fetching review link:", err);
          this.alertService.error("Error fetching review link!");
        },
      });

    
  }

  isResyncAllowed(): boolean {
    const status = this.data && this.data.participant ? this.data.participant.status : null;
    return (
      (status === 'IN_PROGRESS' || status === 'COMPLETED') &&
      !this.isResyncInProgress
    );
  }

  closeIframe() {
    this.reviewLink = null;
  }
}
