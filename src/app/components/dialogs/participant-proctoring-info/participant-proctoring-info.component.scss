@import '../../../../styles/colors.scss';

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 16px;
}

h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: bold;
  color: $primary--blue;
}

table.details {
  margin-bottom: 24px;

  td  {
    padding-right: 24px;
    padding-bottom: 12px;
  }
}

.assessment-table {
  flex-grow: 1;
}

::ng-deep kf-table {
  mat-header-row {
    border-top: solid thin rgba(0, 0, 0, 0.12);
  }
}

.iframe-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgb(255, 255, 255);
  z-index: 9999;
}

iframe {
  width: 100%;
  height: 100%;
}

.close-btn {
  position: absolute;
  border: none;
  top: 5px;
  right: 5px;
  font-size: 28px;
  color: #333;
  cursor: pointer;
  background-color: rgb(255, 255, 255);
}

.proctoring-result {
  font-weight: 800;

  .no-link {
    cursor: not-allowed;
    color: $primary--grey;
  }
}

.proctoring-score {
  .pill {
    display: inline-block;
    padding: 2px 10px;
    border-radius: 6px;
    color: #000;
    font-weight: 500;
    text-align: center;
    min-width: 40px;
    box-sizing: border-box;
  }

  .pill-default {
    background-color: none;
    color: #7a4f1d;
  }

  .pill-low {
    background-color: #FF9675;
    color: #7a4f1d;
  }

  .pill-medium {
    background-color: #FEE0B6;
    color: #7a4f1d;
  }

  .pill-high {
    background-color: #78D363;
    color: #1b4d1b;
  }
}
.header .resync-button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
  color: #919191;
}
