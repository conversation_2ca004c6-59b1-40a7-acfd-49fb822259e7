@import "../../../../styles/colors.scss";
@import "../../../../styles/typography.scss";

h3 {
  margin: 0;
}

.mat-dialog-actions {
  justify-content: flex-end;

  // button {
  //   // TODO create shared component for properly styled button
  //   cursor: pointer;
  //   display: inline-block;
  //   padding: 6px 12px;
  //   margin-right: 12px;

  //   white-space: nowrap;
  //   vertical-align: middle;

  //   font: unquote($proxima-font);
  //   font-size: 0.9em;
  //   font-weight: bold;
  //   letter-spacing: 0.5px;
  //   text-transform: uppercase;

  //   background: white;
  //   color: $primary--blue;
  //   border: 1px solid $primary--blue;

  //   &:hover {
  //     background: $primary--blue;
  //     color: white;
  //   }

  //   .mat-icon {
  //     margin-right: 12px;
  //     vertical-align: middle;
  //     color: inherit;
  //   }
  // }
}

.menu-button {
  width: 150px;
  color: $primary--blue-medium;
  .mat-select-arrow {
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid $primary--blue-medium;
    margin: 0 6px;
    display: inline-block;
    vertical-align: middle;
  }
}

table.headers {
  tr {
    td {
      line-height: 1.8em;

      &.key {
        font-weight: bold;
        padding-right: 24px;
      }
    }

    &.content {
      td {
        padding-top: 24px;

        pre {
          font-family: monospace;
          font-weight: 400;
          line-height: 1.4em;
          font-size: 12px;
          background: #eee;
          padding: 12px 24px 12px 12px;
          opacity: 0.9;
          min-width: 0;
          max-width: 980px;
          max-height: 300px;
          overflow: auto;

          /* width */
          &::-webkit-scrollbar {
            width: 8px;
            height: 8px;
          }

          /* Track */
          &::-webkit-scrollbar-track {
            background: $primary--grey-light;
          }

          /* Handle */
          &::-webkit-scrollbar-thumb {
            background: $primary--grey-medium;
          }

          /* Handle on hover */
          &::-webkit-scrollbar-thumb:hover {
            background: $primary--grey-dark;
          }
        }
      }
    }
  }
}
