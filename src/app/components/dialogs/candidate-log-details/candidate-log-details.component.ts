import { CandidateLogRecord } from '@/shared/models';
import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';

@Component({
  selector: 'app-candidate-log-details',
  templateUrl: './candidate-log-details.component.html',
  styleUrls: ['./candidate-log-details.component.scss'],
})
export class CandidateLogDetailsComponent {
  constructor(
    public dialogRef: MatDialogRef<CandidateLogDetailsComponent>,
    @Inject(MAT_DIALOG_DATA) public data: CandidateLogRecord
  ) {}

  convertToJson(content) {
    return JSON.parse(content);
  }
}
