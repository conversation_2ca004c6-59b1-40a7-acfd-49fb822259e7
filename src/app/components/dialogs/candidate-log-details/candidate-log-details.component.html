<h3 mat-dialog-title>Log Details: ID {{ data.logId }}</h3>

<div class="body">
  <table class="headers">
    <tr>
      <td class="key">Logger</td>
      <td class="value">{{ data.logger }}</td>
    </tr>

    <tr>
      <td class="key">Created Date</td>
      <td class="value">{{ data.created }}</td>
    </tr>

    <tr>
      <td class="key">Level</td>
      <td class="value">{{ data.level | json }}</td>
    </tr>

    <tr>
      <td class="key">ClientApplication</td>
      <td class="value">{{ data.application }}</td>
    </tr>

    <tr>
      <td class="key">ClientMachine</td>
      <td class="value">{{ data.machine }}</td>
    </tr>

    <tr class="content" *ngIf="data.exceptions">
      <td class="key">Exceptions</td>
      <td class="value">
        <pre><ng-container *ngFor="let exception of data.exceptions"><b>{{ exception.exceptionType }}</b> - <b>{{ exception.message }}</b><br>{{ exception.stackTrace }}<br><br></ng-container></pre>
      </td>
    </tr>

    <tr class="content" *ngIf="data.jsonData">
      <td class="key">Context</td>
      <td class="value">
        <pre>{{ convertToJson(data.jsonData) | json }}</pre>
      </td>
    </tr>
  </table>
</div>

<mat-dialog-actions>
  <button class="btn btn-primary" mat-button mat-dialog-close>Close</button>
</mat-dialog-actions>
