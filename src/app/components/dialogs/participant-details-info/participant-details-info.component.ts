import { KFTableColumn } from '@/components/controls/kf-table/kf-table.component';
import { ReportDisplayOrder } from '@/shared/models';
import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { Subscription } from 'rxjs';

/**
 * Component to configure report display order.
 */
@Component({
  selector: 'app-participant-details-info',
  styleUrls: ['./participant-details-info.component.scss'],
  templateUrl: './participant-details-info.component.html',
})
export class ParticipantDetailsInfoComponent implements OnInit {
  currentSubscription: Subscription;
  reportsDisplayOrder: ReportDisplayOrder[];
  originalReportsDisplayOrder: ReportDisplayOrder[];

  columns: KFTableColumn<ReportDisplayOrder>[] = [
    {
      label: 'Assessment',
      name: 'type',
      type: 'text',
      sortable: true,
    },
    {
      label: 'Status',
      name: 'status',
      type: 'text',
      sortable: true,
    },
    {
      label: 'Completed Date',
      name: 'completed',
      type: 'date',
      sortable: true,
    },
  ];

  constructor(
    public dialogRef: MatDialogRef<ParticipantDetailsInfoComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
  ) {}

  ngOnInit() {}

  goToProjectDetails(id) {
    this.dialogRef.close(id);
  }
}
