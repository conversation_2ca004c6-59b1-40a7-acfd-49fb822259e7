<h3 class="header">
  <a (click)="goToProjectDetails(data.id)">
    {{ data.projectId }} - {{ data.projectName }}
  </a>
</h3>

<table class="details">
  <tr *ngIf="data.childProjectIds.length">
    <td>
      <h4>Child projects:</h4>
    </td>
    <td>
      <h5 *ngFor="let id of data.childProjectIds">
        <a (click)="goToProjectDetails(id)"
          ><b>{{ id }}</b></a
        >
      </h5>
    </td>
  </tr>

  <tr *ngIf="data.currentLevel">
    <td>
      <h4>Current level:</h4>
    </td>
    <td>
      <h5>{{ data.currentLevel | levels }}</h5>
    </td>
  </tr>

  <tr *ngIf="data.targetLevel">
    <td>
      <h4>Target level:</h4>
    </td>
    <td>
      <h5>{{ data.targetLevel | levels }}</h5>
    </td>
  </tr>
</table>

<div class="assessment-table">
  <kf-table [data]="data.assessments" [columns]="columns"></kf-table>
</div>
