<h2 mat-dialog-title>{{type}} filter</h2>
<mat-dialog-content>
  <form #createForm="ngForm"
        [formGroup]="filterForm">
    <div>
      <label>Filter type</label>
      <mat-form-field>
        <mat-select class="form-group"
                    placeholder="Select type"
                    (selectionChange)="filterChange('filterType', $event.value)"
                    [(ngModel)]="filterDetails.selectedFilter.filterType"
                    formControlName="type">
          <mat-option class="form-control"
                      *ngFor="let type of filterDetails.options?.filterTypes"
                      [value]="type.viewValue">
            {{ type.viewValue }}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="formControls.type.errors?.required">required</mat-error>
      </mat-form-field>
    </div>
    <div>
      <label>Filter policy</label>
      <mat-form-field>
        <mat-select class="form-group"
                    placeholder="Select policy"
                    (selectionChange)="filterChange('filterPolicy', $event.value)"
                    [(ngModel)]="filterDetails.selectedFilter.filterPolicy"
                    formControlName="policy">
          <mat-option class="form-control"
                      *ngFor="let policy of filterDetails.options?.filterPolicy"
                      [value]="policy.viewValue">
            {{ policy.viewValue }}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="formControls.policy.errors?.required">required</mat-error>
      </mat-form-field>
    </div>
    <div>
      <label>Filter values</label>
      <mat-form-field>
        <mat-select class="form-group"
                    *ngIf="isDropdown"
                    placeholder="Select values"
                    (selectionChange)="filterChange('filterValues', $event.value.join('|'))"
                    [(ngModel)]="selectedFilterValues"
                    formControlName="value"
                    multiple>
          <mat-option class="form-control"
                      *ngFor="let item of filterValues"
                      [value]="item.value">
            {{item.viewValue}}
          </mat-option>
        </mat-select>
        <input id="filterValues"
               [(type)]="valueInputType"
               *ngIf="!isDropdown"
               matInput
               [(ngModel)]="selectedFilterValues"
               formControlName="value"
               placeholder="Input filter values" />
        <mat-hint class="strong-hint" *ngIf="filterForm.get('type').value === 'SuccessProfile'">
                If there are multiple success profiles to add, please enter them separated by commas. Special characters are not allowed.
        </mat-hint>
        <mat-error>
          <span *ngIf="formControls.value.errors?.required">required</span>
          <span *ngIf="!formControls.value.errors?.required && formControls.value.touched && formControls.value.errors?.invalidSPId">
            Please enter numeric values separated by commas. Special characters, spaces, or pipe symbols are not allowed.
          </span>
        </mat-error>
      </mat-form-field>
    </div>
  </form>

</mat-dialog-content>
<mat-dialog-actions>
  <button mat-button
          mat-dialog-close
          class="btn-secondary">Close</button>
  <button mat-button
          [mat-dialog-close]="updates"
          cdkFocusInitial
          class="btn-primary"
          [disabled]="filterForm.invalid">Save</button>
</mat-dialog-actions>
