import { Component, Inject, OnInit } from '@angular/core';
import {
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { MAT_DIALOG_DATA } from '@angular/material';
import { ConfigurationOption } from '@/shared/models';
import { ReportFilterData } from '@/shared/models/reports/reportFilterData';
import { CustomProjectTypeService } from '@/services/custom-project-type.service';
import { ClientReportsService } from '@/services/client-reports.service';

@Component({
  selector: 'filters-dialog',
  styleUrls: ['./filters-dialog.component.scss'],
  templateUrl: './filters-dialog.component.html',
})
export class FiltersDialogComponent implements OnInit {
  filterForm: FormGroup;
  type = 'Add';
  filterUpdates = {};
  filterDetails: any;
  showDropdown = true;
  selectedFilterValues: string[];
  filterValues: ConfigurationOption[] = [];
  customProjectTypes: ConfigurationOption[] = [];
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: ReportFilterData,
    private customProjectTypeService: CustomProjectTypeService,
    private clientReportService: ClientReportsService
  ) {
    const selectedFilter = { ...data.selectedFilter };
    this.filterDetails = { ...data, selectedFilter };
    if (selectedFilter.filterValues) {
      this.selectedFilterValues = selectedFilter.filterValues.split('|');
    }
    if (this.data.selectedFilter.filterType) {
      this.filterValues = this.data.options.allFilters.find(
        (f) => f.filterType.viewValue === this.data.selectedFilter.filterType
      ).filterValues.sort((a: any, b: any) => a.viewValue.localeCompare(b.viewValue));
    }
  }

  ngOnInit() {
    this.filterForm = new FormGroup({
      policy: new FormControl('', [Validators.required]),
      type: new FormControl('', [Validators.required]),
      value: new FormControl('', [Validators.required]),
    });
    this.type = this.data.type || this.type;

    // Set initial filter type if available
    const initialFilterType = this.data.selectedFilter.filterType;
    if (initialFilterType) {
      this.filterForm.get("type").setValue(initialFilterType);
      this.filterChange("filterType", initialFilterType);
    }

    this.customProjectTypeService
      .getCustomProjectTypes(this.data.clientId)
      .subscribe((customProjectTypes) => {
        this.customProjectTypes = customProjectTypes.map((projectType) => ({
          value: projectType.customProjectTypeId.toString(),
          viewValue: projectType.name,
        }));
        // Combine existing filter values with the new filter custom project type values
        if (this.data.selectedFilter.filterType === 'ProjectType') {
          this.filterValues = [
            ...this.filterValues,
            ...this.customProjectTypes,
          ];
        }
      });
  }

  get formControls() {
    return this.filterForm.controls;
  }

  get updates() {
    if (this.filterForm.invalid) {
      return;
    }
    if (!this.isDropdown) {
      this.filterUpdates['filterValues'] = this.selectedFilterValues;
    }
    
    return this.filterUpdates;
  }

  get isDropdown() {
    return this.filterValues.length > 0;
  }

  // Handle filter change
  filterChange(prop: string, value: string) {
    this.filterUpdates[prop] = value;
  
    if (prop === 'filterValues') {
      this.filterUpdates['filterValuesText'] = this.filterValues
        .filter((filterValue) => value.split('|').indexOf(filterValue.value) !== -1)
        .reduce((a, currentValue) => [...a, currentValue.viewValue], []);
    }
 
     // Combine existing filter values with the new filter custom project type values
    if (prop === 'filterType') {
      if (value === 'ProjectType') {
        this.filterValues = [
          ...this.data.options.allFilters.find(
            (f) => f.filterType.viewValue === value
          ).filterValues,
          ...this.customProjectTypes,
        ];
      } else {
        this.filterValues = this.data.options.allFilters.find((f) => f.filterType.viewValue === value).filterValues;
      }
      if (value === 'SuccessProfile') {
        this.filterForm.get('value').setValidators([Validators.required, this.validateSPIds.bind(this)]);
      } else {
        this.filterForm.get('value').setValidators(Validators.required);
      }
    }
  }

  public get valueInputType() {
      return 'text';
  }
  validateSPIds(control: FormControl){
    const value: string = control.value;
    if (this.filterForm.get('type').value === 'SuccessProfile' && !/^\d+(,\d+)*$/.test(value)) {
      return { 'invalidSPId': true };
    }
    return null;
  }
}
