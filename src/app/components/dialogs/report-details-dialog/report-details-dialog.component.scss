@import '../../../../styles/colors.scss';

.mat-dialog-content {
  .field-group {
    padding: 12px 0;

    .field-name {
      font-weight: bold;
      letter-spacing: 1px;
      color: $primary--grey-dark;
      white-space: nowrap;

      user-select: none;
      display: inline-block;
      vertical-align: middle;
      min-width: 200px;

      a {
        float: right;
        color: inherit;

        .mat-icon {
          vertical-align: middle;
          line-height: inherit;
          color: inherit;

          font-size: 1.3em;
          margin-right: 0;
        }
      }
    }

    .field-value {
      display: inline-block;
      vertical-align: middle;
      max-width: calc(100% - 248px);

      .mat-chip {
        padding: 2px 8px;
        min-height: 0px;
        font-size: 0.9em;
        height: auto;
      }

      .columns-2 {
        columns: 2;

        .mat-checkbox {
          display: block;
          margin-right: 18px;
          margin-bottom: 4px;
        }
      }

      input[type=text] {
        min-width: 400px;
      }
    }
  }
}

.mat-dialog-actions {
  button {
    &:not(.btn-primary) {
      border: solid 1px $primary--blue;
      background: transparent;
      color: $primary--blue;
      text-transform: uppercase;
      margin-right: 12px;
    }

    .mat-icon {
      vertical-align: middle;
      color: inherit;
      margin-right: 6px;
      line-height: 19px;
      height: 19px;
    }

    &.green {
      border-color: $secondary--green-dark;
      background: transparent;
      color: $secondary--green-dark;
    }

    &.red {
      border-color: $secondary--red;
      background: transparent;
      color: $secondary--red;
    }
  }
}
