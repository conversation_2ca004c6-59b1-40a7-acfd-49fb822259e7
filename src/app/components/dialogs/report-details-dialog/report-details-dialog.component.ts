import { ClientReportsService } from '@/services';
import { LanguageDetail, ReportConfiguration, ReportDisplayOrder, ReportLanguages } from '@/shared/models';
import { Component, Inject, OnDestroy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { Subscription } from 'rxjs';
import { EnableReportRequestData, ReportDetailsData, REPORT_DETAIL_FIELDS } from './report-details.data';
import * as _ from 'lodash';

export class ReportDetailsDialogData {
  report: ReportConfiguration;
  isEnabledForClient: boolean;
}

export enum ReportDetailsDialogResult {
  editFilters,
  enableForClient,
  disableForClient,
}

/**
 * Component to configure report display order.
 */
@Component({
  selector: 'app-report-details-dialog',
  styleUrls: ['./report-details-dialog.component.scss'],
  templateUrl: './report-details-dialog.component.html',
})
export class ReportDetailsDialogComponent implements OnInit, OnDestroy {
  report: ReportConfiguration;
  currentSubscription: Subscription;
  ReportDetailsDialog: ReportDisplayOrder[];
  originalReportDetailsDialog: ReportDisplayOrder[];
  allFields: ReportDetailsData[];
  allLanguages: ReportLanguages;

  @ViewChild('text') text: TemplateRef<any>;
  @ViewChild('array') array: TemplateRef<any>;
  draft: { nameReference?: string; languages?: any[] } = {};
  isReportInDraft = false;

  constructor(
    public dialogRef: MatDialogRef<ReportDetailsDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ReportDetailsDialogData,
    private reportService: ClientReportsService,
  ) {}

  ngOnInit() {
    this.allFields = REPORT_DETAIL_FIELDS;

    this.reportService.apiService
      .get<ReportLanguages>(
        `clientReportConfiguration/GetAllReportsLanguages`,
        (json) => <ReportLanguages>json
      )
      .subscribe(
        (result) => {
          this.allLanguages = result as ReportLanguages;
        }
      );
  }

  get fields() {
    return this.isReportInDraft ? this.allFields.filter(f => f.isEditable) : this.allFields;
  }

  get props() {
    return Object.keys(this.report);
  }

  getTemplate(type: string) {
    return this[type] || this.text;
  }

  getValue(item, path: string) {
    if (!path) {
      return item.toString();
    }

    const props = path.split('.');
    return props.reduce((prev, curr) => prev[curr], item);
  }

  getLanguages() {
    const reportLanguageIds = this.allLanguages.reportLanguages[this.data.report.id] || [];
    const reportLanguages = reportLanguageIds.map((langId) =>
      this.allLanguages.languages.find((l) => l.languageId === langId)
    );

    reportLanguages.forEach((item) => {
      item['selected'] = this.data.report.languages.find(
        (l) => l.languageId === item.languageId
      );
    });

    return _.sortBy(reportLanguages, 'language.languageName');
  }

  getLanguageTitle(item: LanguageDetail) {
    return `${item.language.languageName} (${item.language.productsLocale})`;
  }

  cancelChanges() {
    this.draft = {};
    this.isReportInDraft = false;
  }

  saveChanges() {
    const requestBody = {
      blendedReportId: this.data.report.id,
      kfasClientId: this.data.report.kfasClientId,
      nameReference: this.draft.nameReference,
      languageIds: this.draft.languages.map(l => l.languageId),
    } as EnableReportRequestData;

    this.reportService.enableReportForClient(requestBody).subscribe(result => {
      this.data.report = Object.assign({}, this.data.report, this.draft);
      this.cancelChanges();
    });
  }

  toggleSelection(fieldName, itemIdPath, item) {
    const itemId = this.getValue(item, itemIdPath);
    const index = this.draft[fieldName].map(d => this.getValue(d, itemIdPath)).indexOf(itemId);

    if (index < 0) {
      this.draft[fieldName].push(item);
    } else {
      this.draft[fieldName].splice(index, 1);
    }
  }

  editFields() {
    this.allFields
      .filter(f => f.isEditable)
      .forEach(field => {
        const oldValue = this.getValue(this.data.report, field.path);
        this.draft[field.path] = JSON.parse(JSON.stringify(oldValue));
      });

    this.isReportInDraft = true;
  }

  confirmEditField(field: ReportDetailsData) {
    const newValue = this.draft[field.path];
    this.data.report[field.path] = JSON.parse(JSON.stringify(newValue));
  }

  ngOnDestroy() {
    // unsubscribe to ensure no memory leaks
    if (this.currentSubscription) {
      this.currentSubscription.unsubscribe();
    }
  }
}
