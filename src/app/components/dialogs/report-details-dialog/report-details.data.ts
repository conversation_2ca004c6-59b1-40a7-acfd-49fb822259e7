export class ReportDetailsData {
  type: string;
  name: string;
  path: string;
  itemLabelPath?: string;
  itemIdPath?: string;
  isEditable?: boolean;
}
export class ReportData {
  'reportId': number;
  'manifestId': string;
  'reportKey': string;
  'isParticipantReport': boolean;
  'languages': {
    name: string;
    id: number;
  }[];
  'participantCategory': any[];
  'blendedScoreTypes': any[];
  'reportLabels': string;
  'nameReference': string;
}

export class EnableReportRequestData {
  kfasClientId: string;
  blendedReportId: number;
  languageIds: number[];
  nameReference: string;
}
export class DisableReportRequestData {
  reportId: number;
}

export const REPORT_DETAIL_FIELDS: ReportDetailsData[] = [
  {
    type: 'text',
    name: 'Manifest ID',
    path: 'cmsManifestId',
  },
  {
    type: 'text',
    name: 'Report Key',
    path: 'reportKey',
  },
  {
    type: 'text',
    name: 'Name Reference',
    path: 'nameReference',
    isEditable: true,
  },
  {
    type: 'text',
    name: 'Inherit filters',
    path: 'filtersInhertitedFromParent',
  },
  {
    type: 'text',
    name: 'Is Participant',
    path: 'isParticipant',
  },
  {
    type: 'text',
    name: 'Labels',
    path: 'labels',
  },
  {
    type: 'array',
    name: 'Languages',
    path: 'languages',
    itemLabelPath: 'language.languageName',
    itemIdPath: 'languageId',
    isEditable: true,
  },
  {
    type: 'array',
    name: 'Projects',
    path: 'reportProjects',
  },
  {
    type: 'array',
    name: 'Assessments',
    path: 'reportAssessments',
  },
];
