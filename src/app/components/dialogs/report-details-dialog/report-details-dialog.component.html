<!-- <PERSON><PERSON><PERSON>OG HEADER -->
<h2 mat-dialog-title>Report Details</h2>

<!-- DIALOG CONTENT -->
<div class="table-container" mat-dialog-content>
  <div class="field-group" *ngFor="let field of fields">
    <!-- left column with field name and edit button (optional) -->
    <div class="field-name">
      {{ field.name }}

      <!-- For editable fields -->
      <ng-container *ngIf="data.isEnabledForClient">
        <a *ngIf="!isReportInDraft && field.isEditable" class="flat" matTooltip="Edit {{ field.name }}" (click)="editFields()">
          <mat-icon>edit</mat-icon>
        </a>
      </ng-container>
    </div>

    <!-- right column with data (using of ng-templates to correct display different types) -->
    <div class="field-value">
      <ng-container
        *ngTemplateOutlet="getTemplate(field.type); context: { $implicit: field, this: this }"
      ></ng-container>
    </div>
  </div>
</div>

<!-- DIALOG ACTIONS PANEL -->
<div mat-dialog-actions>
  <ng-container *ngIf="isReportInDraft">
    <button class="btn btn-primary" (click)="saveChanges()">
      SAVE CHANGES
    </button>
    <button class="btn btn-secondary" (click)="cancelChanges()">
      CANCEL
    </button>
  </ng-container>

  <ng-container *ngIf="!isReportInDraft">
    <button *ngIf="!data.isEnabledForClient" class="btn green" mat-dialog-close="enable">
      <mat-icon>add</mat-icon>
      Enable for this client
    </button>

    <button *ngIf="data.isEnabledForClient" class="btn" mat-dialog-close="editFilters">
      <mat-icon>edit</mat-icon>
      Edit filters
    </button>

    <button *ngIf="data.isEnabledForClient" class="btn red" mat-dialog-close="disable">
      <mat-icon>close</mat-icon>
      Disable for this client
    </button>
  </ng-container>
</div>

<!-- template for display text properties -->
<ng-template #text let-field let-this="this">
  <div *ngIf="!this.isReportInDraft">
    {{ this.getValue(this.data.report, field.path) }}
  </div>

  <input *ngIf="this.isReportInDraft" type="text" [(ngModel)]="this.draft[field.path]" />
</ng-template>

<!-- template for display array properties as chips -->
<ng-template #array let-field let-this="this">
  <mat-chip-list *ngIf="!this.isReportInDraft">
    <mat-chip *ngFor="let item of this.getValue(this.data.report, field.path)">
      {{ this.getValue(item, field.itemLabelPath) }}
    </mat-chip>
  </mat-chip-list>

  <div class="columns-2" *ngIf="this.isReportInDraft">
    <mat-checkbox
      color="primary"
      [(checked)]="item.selected"
      (change)="this.toggleSelection(field.path, field.itemIdPath, item)"
      *ngFor="let item of this.getLanguages()"
    >
      {{ this.getLanguageTitle(item) }}
    </mat-checkbox>
  </div>
</ng-template>
