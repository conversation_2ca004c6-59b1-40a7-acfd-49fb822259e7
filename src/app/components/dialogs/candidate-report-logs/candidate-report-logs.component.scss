@import "../../../../styles/colors.scss";
@import "../../../../styles/typography.scss";

h3 {
  margin: 0;
}

.mat-dialog-actions {
  justify-content: flex-end;
}

.menu-button {
  width: 150px;
  color: $primary--blue-medium;
  .mat-select-arrow {
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid $primary--blue-medium;
    margin: 0 6px;
    display: inline-block;
    vertical-align: middle;
  }
}

tbody {
  tr {
    td {
      line-height: 1.8em;
    }
    th {
      padding: 0.5em 0.5em 0.5em 0;
      font-weight: bold;
    }
  }
}

