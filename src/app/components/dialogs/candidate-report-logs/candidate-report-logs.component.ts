import { Component, Inject, TemplateRef, ViewChild } from "@angular/core";
import { MatDialogRef, MAT_DIALOG_DATA } from "@angular/material";
import { CandidateReportLog } from "@/shared/models/projects/participants";
import { KFTableColumn } from "@/components/controls";
import { ApiService } from "@/services/api.service"
import { catchError } from "rxjs/operators";
import { of } from "rxjs";
import { AlertService } from '@/services';

@Component({
  selector: "app-candidate-report-logs",
  templateUrl: "./candidate-report-logs.component.html",
  styleUrls: ["./candidate-report-logs.component.scss"],
})
export class CandidateReportLogsComponent {
  get columns(): KFTableColumn<any>[] {
    return [
      {
        type: "date",
        name: "requestCreated",
        label: "Created",
      },
      {
        type: "text",
        name: "request",
        template: this.REQUEST_TPL,
        label: "Request",
        width: this.data.length ? "120px" : ""
      },
      {
        type: "text",
        name: "response",
        template: this.RESPONSE_TPL,
        label: "Response",
        width: this.data.length ? "120px" : "100px"
      },
    ];
  }

  @ViewChild("REQUEST_TPL") REQUEST_TPL: TemplateRef<any>;
  @ViewChild("RESPONSE_TPL") RESPONSE_TPL: TemplateRef<any>;

  constructor(
    public dialogRef: MatDialogRef<CandidateReportLogsComponent>,
    @Inject(MAT_DIALOG_DATA) public data: CandidateReportLog[],
    public apiService: ApiService,
    private alertService: AlertService,
  ) {}

  downloadFile(fileUrl: string, type: string): void {
    const apiUrl = this.apiService.buildActionUrl(fileUrl);
    const fileName = fileUrl.split('/').pop() || `${type}.json`;
  
    this.apiService.getWithFileResponse(apiUrl)
      .subscribe(
        (data) => {
          if (data) {
            const blob = new Blob([data], { type: 'application/json' });
            const downloadURL = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = downloadURL;
            link.download = fileName;
            link.click();
          } else {
            this.alertService.error('Failed to download file. Response data is empty.');
          }
        },
        (error) => {
          this.alertService.error('Failed to download file. Please try again.');
        }
      );
  }
}
