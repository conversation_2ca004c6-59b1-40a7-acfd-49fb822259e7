@import "../../../../styles/colors.scss";
@import "../../../../styles/theme.scss";

:host {
  .content {
    @extend .glass;
    margin: 0 64px 48px 64px;
  }

  .table-container {
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
  }

  .table-actions-panel {
    display: flex;
    padding: 12px 24px;
    flex-direction: row;
    justify-content: stretch;
    align-items: center;
    user-select: none;

    input[type="search"] {
      width: 250px;
    }

    a {
      display: inline-block;
      font-weight: bold;
      padding: 3px 6px;
      white-space: nowrap;
      vertical-align: middle;
      color: $primary--blue;
      font-size: 0.9em;

      .mat-icon {
        margin-right: 12px;
        vertical-align: middle;
        color: inherit;
      }
    }

    .right-side {
      margin-left: auto;

      a {
        margin-left: 6px;
        font-size: 1em;
      }
    }

    .left-side {
      span {
        display: inline-block;
        margin-left: auto;
        font-size: 12px;
        font-weight: bold;
        font-style: normal;
        font-stretch: normal;
        letter-spacing: 1px;
        text-transform: uppercase;
        color: rgba(0, 0, 0, 0.33);
        margin-right: 48px;
        padding: 6px 0;
        vertical-align: middle;
      }
      a {
        font-size: 12px;
        font-weight: bold;
        font-style: normal;
        font-stretch: normal;
        letter-spacing: 1px;
        text-transform: uppercase;
        position: relative;
        padding: 6px 0;
        vertical-align: middle;
        margin-right: 48px;

        &.active {
          &::after {
            content: "";
            width: 22px;
            height: 2px;
            position: absolute;
            background: $primary--blue;
            bottom: 3px;
            left: 0;
          }
        }
      }
    }
  }

  .element-row {
    position: relative;
    &:not(.expanded) {
      cursor: pointer;
      &:hover {
        background: $primary--grey-light;
      }
    }
    &.expanded {
      border-bottom-color: transparent;
    }
  }

  mat-paginator {
    border-top: thin solid rgba(0, 0, 0, 0.12);
  }
}
