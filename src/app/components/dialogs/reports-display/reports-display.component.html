<div class="page-content">
  <div class="glass">
    <div class="table-actions-panel">
      <div class="left-side">
        <span>reports to Display: </span>
        <a
          [class.active]="selectedTab === 'Client'"
          (click)="refreshTable('Client')"
          >ENabled for client</a
        >
        <a [class.active]="selectedTab === 'All'" (click)="refreshTable('All')"
          >Available</a
        >
        <input class="glass" type="search" [(ngModel)]="filter" placeholder="Search report" />
      </div>
      <div class="right-side">
        <a *ngIf="selectedTab === 'Client'" (click)="showDisplayOrder()">
          <mat-icon>sort</mat-icon>
          <span>Change Display Order</span>
        </a>
      </div>
    </div>

    <div class="table-container">
      <kf-table
        class="client-reports"
        [data]="tableData"
        [filter]="filter"
        [columns]="columns"
        [actions]="actions"
      ></kf-table>
    </div>
  </div>
</div>
