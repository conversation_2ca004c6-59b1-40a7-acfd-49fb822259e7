import { ConfirmationDialogComponent } from '@/components/controls/confirmation-dialog/confirmation-dialog.component';
import { KFTableAction } from '@/components/controls/kf-table/kf-table.component';
import { AlertService, ClientReportsService, SpinnerService } from '@/services';
import { ClientReportActions, ReportConfiguration } from '@/shared/models';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material';
import { EnableReportDialogComponent } from '../enable-report-dialog/enable-report-dialog.component';
import {
  ReportDetailsDialogComponent,
  ReportDetailsDialogData,
} from '../report-details-dialog/report-details-dialog.component';
import { EnableReportRequestData, ReportData } from '../report-details-dialog/report-details.data';
import {
  ReportsDisplayOrderComponent,
  ReportsDisplayOrderData,
} from '../reports-display-order/reports-display-order.component';
import { REPORTS_COLUMNS } from './reports-display.columns';

// Component to display client reports with configuration.
@Component({
  selector: 'app-reports-display',
  styleUrls: ['./reports-display.component.scss'],
  templateUrl: './reports-display.component.html',
})
export class ReportsDisplayComponent implements OnInit, OnDestroy {
  filter = '';
  columns = REPORTS_COLUMNS;
  actions: KFTableAction<ReportConfiguration>[] = [
    {
      click: (t: ReportConfiguration) => {
        this.showReportDetails(t);
      },
      displayCondition: (t: ReportConfiguration) => true,
      icon: 'list_alt',
      label: 'View Details',
      css: 'default',
    },
    {
      click: (t: ReportConfiguration) => {
        this.showFilters(t.id);
      },
      displayCondition: (t: ReportConfiguration) => this.selectedTab === 'Client',
      icon: 'edit',
      label: 'Edit Filters',
      css: 'default',
    },
    {
      click: (t: ReportConfiguration) => {
        this.enableForClient(t);
      },
      displayCondition: (t: ReportConfiguration) => !this.isEnabledForClient(t),
      icon: 'add',
      label: 'Enable for client',
      css: 'green',
    },
    {
      click: (t: ReportConfiguration) => {
        this.disableForClient(t);
      },
      displayCondition: (t: ReportConfiguration) => this.isEnabledForClient(t),
      icon: 'close',
      label: 'Disable for client',
      css: 'red',
    },
  ];

  displayOrder: ReportConfiguration[];
  tableData: ReportConfiguration[];
  selectedTab: 'Client' | 'All' = 'Client';
  loading = false;

  constructor(
    public dialog: MatDialog,
    private clientReportService: ClientReportsService,
    private spinnerService: SpinnerService,
    private alertService: AlertService,
  ) {}

  ngOnInit() {
    this.refreshTable('Client');
  }

  isEnabledForClient(report) {
    return this.selectedTab !== 'All';
  }

  // TBD: change state workflow with dialog/routing approach
  public showFilters(item: number) {
    this.clientReportService.state(ClientReportActions.showFilters, item);
  }

  public showDisplayOrder() {
    const dialogRef = this.dialog.open(ReportsDisplayOrderComponent, {
      width: '650px',
      data: { order: this.displayOrder } as ReportsDisplayOrderData,
    });

    dialogRef.afterClosed().subscribe(result => {
      if (!result) {
        return;
      }

      this.spinnerService.activate();
      this.clientReportService.saveDisplayOrder(result).subscribe(
        () => {
          this.spinnerService.deactivate();
          this.alertService.success('Display order updated');
          this.clientReportService.loadClientReports(this.clientReportService.clientId, false);
        },
        error => {
          this.spinnerService.deactivate();
          this.alertService.error(error.message);
        },
      );
    });
  }

  public showReportDetails(report) {
    const dialogRef = this.dialog.open(ReportDetailsDialogComponent, {
      width: '65%',
      data: { report: report, isEnabledForClient: this.isEnabledForClient(report) } as ReportDetailsDialogData,
    });

    dialogRef.afterClosed().subscribe((result: string) => {
      switch (result) {
        case 'editFilters': {
          this.showFilters(report.id);
          break;
        }
        case 'enable': {
          this.enableForClient(report);
          break;
        }
        case 'disable': {
          this.disableForClient(report);
          break;
        }
        default: {
          this.clientReportService.loadClientReports(this.clientReportService.clientId, false);
          this.refreshTable();
          return;
        }
      }
    });
  }

  public enableForClient(report) {
    const dialogRef = this.dialog.open(EnableReportDialogComponent, {
      width: '45%',
      data: { report: report } as ReportDetailsDialogData,
    });

    dialogRef.afterClosed().subscribe((enableData: EnableReportRequestData) => {
      if (enableData) {
        this.spinnerService.activate();
        this.clientReportService.enableReportForClient(enableData).subscribe(
          result => {
            this.clientReportService.loadClientReports(this.clientReportService.clientId, false);
            this.refreshTable();
            this.alertService.success('Report has been enabled');
            this.spinnerService.deactivate();
          },
          error => {
            this.alertService.error(error.message);
            this.spinnerService.deactivate();
          },
        );
      }
    });
  }

  public disableForClient(report: ReportConfiguration) {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: '450px',
      data: { action: `disable "${report.reportKey}" report for this client` },
    });

    dialogRef.afterClosed().subscribe(confirm => {
      if (confirm) {
        this.spinnerService.activate();
        this.clientReportService.disableReportForClient({ reportId: report.id }).subscribe(
          result => {
            this.clientReportService.loadClientReports(this.clientReportService.clientId, false);
            this.refreshTable();
            this.alertService.success('Report has been disabled');
            this.spinnerService.deactivate();
          },
          error => {
            this.alertService.error(error.message);
            this.spinnerService.deactivate();
          },
        );
      }
    });
  }

  refreshTable(selectedTab?: 'Client' | 'All') {
    if (selectedTab) {
      this.selectedTab = selectedTab;
    }

    switch (this.selectedTab) {
      case 'Client': {
        this.spinnerService.activate();
        this.clientReportService.reportConfigurations.subscribe(
          result => {
            if (this.selectedTab !== 'Client') {
              return;
            }
            this.displayOrder = result.sort((a, b) => a.displayOrder - b.displayOrder);
            this.tableData = this.displayOrder.slice();
            this.spinnerService.deactivate();
          },
          error => {
            this.spinnerService.deactivate();
            this.alertService.error(error.message);
          },
        );
        break;
      }
      case 'All': {
        this.spinnerService.activate();
        this.clientReportService.getAllUnavailableReports().subscribe(
          result => {
            this.tableData = result.map(value => this.convertReportData.call(this, value));
            this.spinnerService.deactivate();
          },
          error => {
            this.alertService.error(error.message);
            this.spinnerService.deactivate();
          },
        );
        break;
      }
    }
  }

  convertReportData(value: ReportData): ReportConfiguration {
    return {
      displayOrder: 0,
      id: value.reportId,
      nameReference: value.nameReference,
      reportKey: value.reportKey,
      isParticipant: value.isParticipantReport,
      cmsManifestId: value.manifestId,
      labels: value.reportLabels,
      kfasClientId: this.clientReportService.clientId.toString(),
      name: value.reportKey,
      languages: value.languages.map(lang => ({
        languageId: lang.id,
        language: {
          languageID: lang.id,
          languageName: lang.name,
        },
      })),      
    } as ReportConfiguration;
  }

  ngOnDestroy() {}
}
