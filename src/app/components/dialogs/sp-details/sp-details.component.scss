@import "../../../../styles/colors.scss";
@import "../../../../styles/typography.scss";

$container-padding: 20px;
$label-width: 250px;
$value-padding: 7px;

.mat-dialog-title {
  padding: $value-padding;
}

.group {
  display: flex;
}

.label {
  width: $label-width;
  padding: $value-padding;
}

.value {
  font-weight: bold;
  padding: $value-padding;
  max-width: 60%;
  max-height: 200px;
  margin-bottom: 12px;
  flex: 1 1 auto;
  overflow: auto;
}

.tech-competency {
  width: 360px;
}

.skill-item {
  font-weight: normal;
  &::before {
    content: '- ';
    user-select: none;
  }
}

.mat-dialog-actions {
  justify-content: flex-end;

  button {
    // TODO create shared component for properly styled button
    cursor: pointer;
    display: inline-block;
    padding: 6px 12px;
    margin-right: 12px;

    white-space: nowrap;
    vertical-align: middle;

    font: unquote($proxima-font);
    font-size: 0.9em;
    font-weight: bold;
    letter-spacing: 0.5px;
    text-transform: uppercase;

    background: white;
    color: $primary--blue;
    border: 1px solid $primary--blue;

    &:hover {
      background: $primary--blue;
      color: white;
    }

    .mat-icon {
      margin-right: 12px;
      vertical-align: middle;
      color: inherit;
    }
  }
}
