<h2 mat-dialog-title>Success profile details</h2>

<div>
  <div class="group">
    <div class="label">Project id:</div>
    <div class="value">{{ data.projectId }}</div>
  </div>
  <div class="group" *ngIf="getCompetenciesReports()">
    <div class="label">{{ assessments.behavioural.assessmentId | assessmentById }}</div>
    <div class="value">{{ getCompetenciesReports() }}</div>
  </div>
  <div class="group" *ngIf="getTraitsReports()">
    <div class="label">{{ assessments.traits.assessmentId | assessmentById }}</div>
    <div class="value">{{ getTraitsReports() }}</div>
  </div>
  <div class="group" *ngIf="getDriversReports()">
    <div class="label">{{ assessments.drivers.assessmentId | assessmentById }}</div>
    <div class="value">{{ getDriversReports() }}</div>
  </div>
  <div class="group" *ngIf="getTechnicalCompetencies()">
    <div class="label">{{ assessments.technicalSkillsInventory.assessmentId | assessmentById }}:</div>
    <div class="value">
      <ng-container *ngFor="let item of getTechnicalCompetencies(); index as i">
        <div class="group">
          <div class="tech-competency">
            {{ item.name }}
            <span *ngIf="item.level"> (Level {{ item.level }}) </span>
          </div>
          <a
            *ngIf="item.technicalSkills.length"
            (click)="toggleExpand(item)"
          >
            {{ item.technicalSkills.length }} skills
          </a>
        </div>
        <div *ngIf="isTechSkillOpened(item)" class="skills">
          <div class="skill-item" *ngFor="let skill of item.technicalSkills">
            {{ skill.name }}
          </div>
        </div>
      </ng-container>
    </div>
  </div>
  <div class="group">
    <div class="label">Assessment JSON:</div>
    <div class="value">{{ data.assessments }}</div>
  </div>

  <mat-dialog-actions>
    <button mat-button mat-dialog-close>Close</button>
  </mat-dialog-actions>
</div>
