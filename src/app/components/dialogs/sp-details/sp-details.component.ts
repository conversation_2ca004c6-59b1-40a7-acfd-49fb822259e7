import { AssessmentService } from '@/services';
import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';

@Component({
  selector: 'app-sp-details',
  templateUrl: './sp-details.component.html',
  styleUrls: ['./sp-details.component.scss'],
})
export class SpDetailsComponent implements OnInit {
  openedTechSkills = [];

  constructor(
    public dialogRef: MatDialogRef<SpDetailsComponent>,
    public assessmentService: AssessmentService,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit() {}

  getCompetenciesReports() {
    const competencies = this.assessments.behavioural;
    return competencies
      ? (competencies.report || [])
          .map((code) =>
            this.assessmentService.getNameOfCompetenciesByCode(code)
          )
          .sort()
          .join(', ')
      : null;
  }

  getDriversReports() {
    const drivers = this.assessments.drivers;
    return drivers
      ? (drivers.report || [])
          .map((code) => this.assessmentService.getNameOfDriversByCode(code))
          .sort()
          .join(', ')
      : null;
  }

  getTraitsReports() {
    const traits = this.assessments.traits;
    return traits
      ? (traits.report || [])
          .map((code) => this.assessmentService.getNameOfTraitsByCode(code))
          .sort()
          .join(', ')
      : null;
  }

  toggleExpand(item) {
    const index = this.openedTechSkills.indexOf(item.id);

    if (index < 0) {
      this.openedTechSkills.push(item.id);
    } else {
      this.openedTechSkills.splice(index, 1);
    }
  }

  getTechnicalCompetencies() {
    const skillsInventory = this.assessments.technicalSkillsInventory;
    return skillsInventory ? skillsInventory.technicalCompetencies || [] : null;
  }

  get assessments() {
    return JSON.parse(this.data.assessments);
  }

  isTechSkillOpened(item) {
    return this.openedTechSkills.indexOf(item.id) >= 0;
  }
}
