<h2 mat-dialog-title>Amend Assessment Custom Norm</h2>
<kf-loading></kf-loading>

<ng-container mat-dialog-content>
  <ng-container>
    <kf-select
      placeholder="Select norm"
      [options]="localNorms"
      [multiple]="false"
      [selection]="selectedOptions.id"
      (selectionChange)="onSelectedNormChange($event)"
    ></kf-select>
    <div class="content">
      <div class="row">
        <div class="col">
          <p>
            Note that changing these fields will result in all completed
            assessments within the project being rescored. Depending on the
            project size, this may take a considerable time. While this is
            happening, the participants' status will show as 'Scoring'.
          </p>
          <br />
          <p>
            If this project includes multiple success profiles, assessments will
            be rescored against all profiles.
          </p>
        </div>
      </div>
    </div>
  </ng-container>

  <mat-dialog-actions>
    <button mat-button mat-dialog-close>Cancel</button>
    <button mat-button id="saveButton" (click)="save()" [disabled]="disabledSaveButton">Save</button>
  </mat-dialog-actions>
</ng-container>
