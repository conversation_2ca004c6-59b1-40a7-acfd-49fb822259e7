import { ProjectService, SpinnerService } from "@/services";
import { ProjectDetails, NormOption, ProjectAssessmentCustomNorm } from "@/shared/models";
import { AssessmentType } from "@/shared/pipes";
import { Component, Inject, OnInit } from "@angular/core";
import { MatDialogRef, MAT_DIALOG_DATA } from "@angular/material";

@Component({
  selector: "app-select-custom-norm",
  templateUrl: "./select-custom-norm.component.html",
  styleUrls: ["./select-custom-norm.component.scss"],
})
export class SelectCustomNormComponent implements OnInit {

  contextProject: ProjectDetails;
  customNorms: NormOption[];
  assessmentType: AssessmentType;

  initialNormNoValue: number;
  localNorms: any;
  disabledSaveButton = true;

  selectedOptions = {
    id: -1
  };

  defaultNormId = -1;

  constructor(
    public dialogRef: MatDialogRef<SelectCustomNormComponent>,
    private projectService: ProjectService,
    private spinnerService: SpinnerService,
    @Inject(MAT_DIALOG_DATA) private data: any
  ) {}

  ngOnInit() {
    this.contextProject = this.data.contextProject;
    this.customNorms = this.data.customNorms;
    this.assessmentType = this.data.assessmentType;

    this.initialNormNoValue = this.data.initialNormNoValue != null ? this.data.initialNormNoValue : -1;
    this.selectedOptions.id = this.initialNormNoValue;

    this.localNorms =
      this.customNorms.map((x: NormOption) => ({
        id: x.normNo ? x.normNo : this.defaultNormId,
        value: x.text,
        disabled: false
      }));
  }

  onSelectedNormChange(value) {
    this.selectedOptions.id = value;
    this.disabledSaveButton = false;
  }

  save() {
    if (this.selectedOptions.id == this.initialNormNoValue) {
      return;
    }

    var normId = this.selectedOptions.id;

    let assessmentCustomNorm: ProjectAssessmentCustomNorm = {
      assessmentType: this.assessmentType,
      normNo: normId == this.defaultNormId ? null : normId,
    };

    this.spinnerService.activate();

    this.projectService
      .updateAssessmentNorm(this.contextProject.projectId, assessmentCustomNorm)
      .subscribe((data) => {
        this.spinnerService.deactivate();
        location.reload();
      });
  }
}
