import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material';


@Component({
  selector: 'app-cpt-report-management-confirmation',
  templateUrl: './cpt-report-management-confirmation.component.html',
  styleUrls: ['./cpt-report-management-confirmation.component.scss']
})
export class CPTReportManagementConfirmationComponent implements OnInit {

  constructor(
    public dialogRef: MatDialogRef<CPTReportManagementConfirmationComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { clientId: string }
  ) {}
  ngOnInit(): void {
    
  }

  onNoClick() {
    this.dialogRef.close('no');
  }

  onYesClick() {
    this.dialogRef.close('yes');
  }

}
