@import "../../../../styles/colors.scss";
@import "../../../../styles/typography.scss";

h3 {
  margin: 0;
}

.mat-dialog-actions {
  justify-content: flex-end;
}

.menu-button {
  width: 150px;
  color: $primary--blue-medium;

  .mat-select-arrow {
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid $primary--blue-medium;
    margin: 0 6px;
    display: inline-block;
    vertical-align: middle;
  }
}


table.headers {
  tr {
    td {
      line-height: 1.8em;

      &.key {
        font-weight: bold;
        padding-right: 24px;
      }
    }

    &.content {
      td {
        padding-top: 24px;

        .content-scroll {
          max-width: 700px;
          overflow: auto;
          background: #f5f5f5;
          border: 1px solid #ccc;
          border-radius: 4px;
          padding: 8px;

          width: 100%;
          box-sizing: border-box;

          pre {
            margin: 0;
            font-size: 12px;
            overflow: auto;
            white-space: pre;
            max-width: 100%;
          }

          // Scrollbar styles
          &::-webkit-scrollbar {
            height: 6px;
            width: 6px;
          }

          &::-webkit-scrollbar-thumb {
            background: #aaa;
            border-radius: 4px;
          }

          &.full-height {
            max-height: 360px;
          }

          &.half-height {
            max-height: 180px;
          }
        }

      }
    }
  }
}