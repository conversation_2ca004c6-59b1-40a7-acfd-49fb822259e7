import { SystemLogDetailsRecord } from "@/shared/models";
import { Component, Inject } from "@angular/core";
import { MatDialogRef, MAT_DIALOG_DATA } from "@angular/material";

@Component({
  selector: "app-system-log-details",
  templateUrl: "./system-log-details.component.html",
  styleUrls: ["./system-log-details.component.scss"],
})
export class SystemLogDetailsComponent {
  constructor(
    public dialogRef: MatDialogRef<SystemLogDetailsComponent>,
    @Inject(MAT_DIALOG_DATA) public data: SystemLogDetailsRecord
  ) {}

  convertToJson(content) {
    return JSON.parse(content);
  }

  getTypeLabel(type: number): string {
    const map = {
      0: "Job",
      1: "Website",
      2: "Integration",
    };
    return type in map ? map[type] : `Unknown (${type})`;
  }
}
