import { ReportConfiguration, ReportDisplayOrder } from '@/shared/models';
import { Component, Inject, OnDestroy, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { Subscription } from 'rxjs';
import { EnableReportRequestData } from '../report-details-dialog/report-details.data';

export class EnableReportDialogData {
  report: ReportConfiguration;
  isEnabledForClient: boolean;
}

export enum EnableReportDialogResult {
  confirm,
  cancel,
}

/**
 * Component to configure report display order.
 */
@Component({
  selector: 'app-enable-report-dialog',
  styleUrls: ['./enable-report-dialog.component.scss'],
  templateUrl: './enable-report-dialog.component.html',
})
export class EnableReportDialogComponent implements OnInit, OnDestroy {
  currentSubscription: Subscription;
  EnableReportDialog: ReportDisplayOrder[];
  originalEnableReportDialog: ReportDisplayOrder[];
  report: ReportConfiguration;
  reqData: EnableReportRequestData;
  separator = ',';

  constructor(
    public dialogRef: MatDialogRef<EnableReportDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: EnableReportDialogData,
  ) {}

  ngOnInit() {
    this.reqData = {
      kfasClientId: this.data.report.kfasClientId,
      blendedReportId: this.data.report.id,
      languageIds: [],
      nameReference: this.data.report.nameReference,
    };
  }

  get props() {
    return Object.keys(this.report);
  }

  toggleLanguage(langId) {
    const index = this.reqData.languageIds.indexOf(langId);

    if (index < 0) {
      this.reqData.languageIds.push(langId);
    } else {
      this.reqData.languageIds.splice(index, 1);
    }
  }

  ngOnDestroy() {
    // unsubscribe to ensure no memory leaks
    if (this.currentSubscription) {
      this.currentSubscription.unsubscribe();
    }
  }
}
