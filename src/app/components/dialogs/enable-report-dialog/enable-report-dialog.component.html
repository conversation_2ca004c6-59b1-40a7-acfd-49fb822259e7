<h2 mat-dialog-title>Enable Report</h2>

<div class="mat-dialog-content">
  <table class="table">
    <tbody>
      <tr>
        <td class="bold">Report Key</td>
        <td class="bold">{{ data.report.reportKey }}</td>
      </tr>
      <tr>
        <td class="bold">Name Reference</td>
        <td>
          <input type="text" [(ngModel)]="reqData.nameReference"/>
        </td>
      </tr>
      <tr>
        <td class="bold">Languages</td>
        <td>
          <div class="three-columns">
            <div *ngFor="let item of data.report.languages">
              <mat-checkbox color="primary" (change)="toggleLanguage(item.language.languageID)">
                {{item.language.languageName}}
              </mat-checkbox>
            </div>
          </div>
        </td>
      </tr>
      <tr>
        <td class="bold"></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</div>

<div mat-dialog-actions>
  <button class="btn btn-primary" [mat-dialog-close]="reqData" [disabled]="!reqData.languageIds">CONFIRM</button>
  <button class="btn btn-secondary" [mat-dialog-close]>CANCEL</button>
</div>
