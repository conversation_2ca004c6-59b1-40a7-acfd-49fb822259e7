@import '../../../../styles/colors.scss';

table {
  width: calc(100% - 48px);
  td {
    padding: 24px 48px 0 0;

    &:first-of-type {
      font-weight: bold;
      letter-spacing: 1px;
      color: $primary--grey-dark;
      white-space: nowrap;
      width: 0%;
    }

    .three-columns {
      columns: 3;
    }

    button {
      display: inline-block;
      vertical-align: middle;
      margin-right: 3px;
    }

    input[type='text'] {
      width: 100%;
      max-width: 300px;
    }

    .mat-primary.mat-checkbox-checked {
      color: $primary--blue;
    }
  }
}
