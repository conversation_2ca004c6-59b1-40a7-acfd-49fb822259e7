<h2 mat-dialog-title>Custom Project Type has been changed</h2>
<mat-dialog-content>
  <form #createForm="ngForm" [formGroup]="reasonForm">
    <div>
      <label>Please enter your reason for making this change</label>
      <mat-form-field>
        <textarea
          id="reasonForChange"
          matInput
          formControlName="reasonForChange"
          (keyup)="onReasonChange()"
          maxlength="255">
        </textarea>
        <mat-error *ngIf="reasonForm.get('reasonForChange').invalid && reasonForm.get('reasonForChange').touched">
          *Required (minimum 5 characters and maximum 255 characters allowed)
        </mat-error>
      </mat-form-field>
    </div>
  </form>
</mat-dialog-content>
<mat-dialog-actions>
  <button mat-button
          mat-dialog-close
          class="btn-secondary">Cancel</button>
  <button mat-button
          [mat-dialog-close]="reasonForChange"
          cdkFocusInitial
          [disabled]="!isSaveEnabled"
          class="btn-primary">Save</button>
</mat-dialog-actions>
