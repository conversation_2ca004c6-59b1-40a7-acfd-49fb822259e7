import { Component, Inject, OnInit } from '@angular/core';
import {
  FormControl,
  FormGroup,
  Validators,
  AbstractControl,
  ValidationErrors,
} from '@angular/forms';
import { MAT_DIALOG_DATA } from '@angular/material';

@Component({
  selector: 'cpt-amend-reason-dialog',
  styleUrls: ['./cpt-amend-reason-dialog.component.scss'],
  templateUrl: './cpt-amend-reason-dialog.component.html',
})
export class CPTAmendmentReasonDialogComponent implements OnInit {
  reasonForm: FormGroup;
  isSaveEnabled = false;
  constructor() {}

  ngOnInit() {
    this.reasonForm = new FormGroup({
      reasonForChange: new FormControl('', [Validators.required, this.reasonLengthValidator])
    });
  }

  get reasonForChange() {
    return this.reasonForm.get('reasonForChange').value;
  }

  // Handle reason change
  onReasonChange() {
    const reasonForChange = this.reasonForm.get('reasonForChange').value;
    if (reasonForChange.trim().length >= 5) {
      this.isSaveEnabled = true;
    } else {
      this.isSaveEnabled = false;
    }
  }

  private reasonLengthValidator(control: AbstractControl): ValidationErrors {
    if (!control.value) {
        // if control is empty return no error
        return null;
    }

    // test the value of the control for the character length excluding the start & trailing spaces
    const valid = control.value.trim().length >= 5 && control.value.trim().length <= 255;

    return valid ? null : { required: true };
  }
}
