@import "../../../../styles/typography.scss";
@import "../../../../styles/colors.scss";

mat-dialog-content {
  flex-direction: row;

  form > div {
    mat-form-field {
      display: block;
    }

    textarea {
      border: 1px solid $primary--blue;
      width: 95%;
      min-height: 200px;
      max-height: 250px;
      padding: 6px 12px;
    }
  }
}

mat-dialog-actions {
  justify-content: flex-end;
}

mat-option {
  font: unquote($proxima-font);
}

.mat-button[disabled] {
  cursor: not-allowed;
}
.btn-secondary{
  background-color: transparent;
  color: $primary--blue; 
  border: 1px solid $primary--blue; 
}