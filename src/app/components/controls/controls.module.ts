import { AppMaterialModule } from '@/app.material.module';
import { SharedModule } from '@/shared/shared.module';
import { DatePipe } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxModule, MatSlideToggleModule } from '@angular/material';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouterModule } from '@angular/router';
import { ColorPickerModule } from 'ngx-color-picker';
import {
  AssessmentValidityPeriodComponent,
  BrandingSettingsFormComponent,
  BrandingSettingsFormFieldComponent,
  ColorChooserComponent,
  ConfirmationDialogComponent,
  FileUploadComponent,
  FrontCoverPreviewComponent,
  ClientDefaultSettingsComponent,
  ClientGeneralInfoComponent,
  KfFloatNavComponent,
  KfFooterComponent,
  KfSelectComponent,
  KfTableComponent,
  KfTilesComponent,
  KFLoadingComponent,
  PageHeaderComponent,
  PortalBrandingSettingsFormComponent,
  PortalDashboardPreviewComponent,
  PortalSigninPreviewComponent,
  RecaptchaComponent,
  ToolbarComponent,
} from '.';
import { SpsPageHeaderComponent } from './sps-page-header/sps-page-header.component';

@NgModule({
  imports: [
    AppMaterialModule,
    BrowserModule,
    BrowserAnimationsModule,
    ColorPickerModule,
    MatCheckboxModule,
    FormsModule,
    ReactiveFormsModule,
    MatSlideToggleModule,
    RouterModule,
    SharedModule,
  ],
  declarations: [
    AssessmentValidityPeriodComponent,
    BrandingSettingsFormComponent,
    BrandingSettingsFormFieldComponent,
    ColorChooserComponent,
    ConfirmationDialogComponent,
    FileUploadComponent,
    FrontCoverPreviewComponent,
    ClientDefaultSettingsComponent,
    ClientGeneralInfoComponent,
    KfFloatNavComponent,
    KfFooterComponent,
    KFLoadingComponent,
    KfSelectComponent,
    KfTableComponent,
    KfTilesComponent,
    PageHeaderComponent,
    PortalBrandingSettingsFormComponent,
    PortalDashboardPreviewComponent,
    PortalSigninPreviewComponent,
    RecaptchaComponent,
    ToolbarComponent,
    SpsPageHeaderComponent,
  ],
  exports: [
    AssessmentValidityPeriodComponent,
    BrandingSettingsFormComponent,
    BrandingSettingsFormFieldComponent,
    ColorChooserComponent,
    ConfirmationDialogComponent,
    FileUploadComponent,
    FrontCoverPreviewComponent,
    ClientDefaultSettingsComponent,
    ClientGeneralInfoComponent,
    KfFloatNavComponent,
    KfFooterComponent,
    KFLoadingComponent,
    KfSelectComponent,
    KfTableComponent,
    KfTilesComponent,
    PageHeaderComponent,
    PortalBrandingSettingsFormComponent,
    PortalDashboardPreviewComponent,
    PortalSigninPreviewComponent,
    RecaptchaComponent,
    ToolbarComponent,
    SpsPageHeaderComponent
  ],
  entryComponents: [ConfirmationDialogComponent],
  providers: [DatePipe],
})
export class ControlsModule {
  constructor() {}
}
