<div class="settings-form">
  <div class="settings-group">
    <div class="row-group">
      <div class="group-header">General</div>
      <branding-settings-form-field
        type="toggle"
        label="Co-branded"
        [(value)]="settings.coBranded"
        (valueChange)="emit()"
      ></branding-settings-form-field>
    </div>
  </div>

  <div class="settings-group">
    <div class="row-group">
      <div class="group-header">Colours</div>
      <branding-settings-form-field
        type="color"
        [nullable]="true"
        default="#000000"
        label="Report Title"
        [(value)]="settings.titleTextColour"
        (valueChange)="debouncedEmit()"
      ></branding-settings-form-field>
      <branding-settings-form-field
        type="color"
        [nullable]="true"
        default="#000000"
        label="Participant Name"
        [(value)]="settings.subTitleTextColour"
        (valueChange)="debouncedEmit()"
      ></branding-settings-form-field>
      <branding-settings-form-field
        type="color"
        [nullable]="true"
        default="#000000"
        label="Solution Name"
        [(value)]="settings.solutionTextColour"
        (valueChange)="debouncedEmit()"
      ></branding-settings-form-field>
      <branding-settings-form-field
        type="color"
        [nullable]="true"
        default="#000000"
        label="Report Info"
        [(value)]="settings.metadataTextColour"
        (valueChange)="debouncedEmit()"
      ></branding-settings-form-field>
      <branding-settings-form-field
        type="color"
        [nullable]="true"
        default="#000000"
        label="Info Details"
        [(value)]="settings.metadataDetailsTextColour"
        (valueChange)="debouncedEmit()"
      ></branding-settings-form-field>
      <branding-settings-form-field
        type="color"
        [nullable]="true"
        default="#000000"
        label="Text Inputs"
        [(value)]="settings.textInputTextColour"
        (valueChange)="debouncedEmit()"
      ></branding-settings-form-field>
      <branding-settings-form-field
        type="color"
        [nullable]="true"
        default="#000000"
        label="Footer Bar"
        [(value)]="settings.footerBarColour"
        (valueChange)="debouncedEmit()"
      ></branding-settings-form-field>
    </div>
  </div>
  <div class="settings-group">
    <div class="row-group">
      <div class="group-header">Cover Image</div>
      <branding-settings-form-field
        type="select"
        label="Asset Name"
        [(value)]="settings.coverImagePath"
        (valueChange)="emit()"
        [items]="assetsListItems"
      ></branding-settings-form-field>
      <branding-settings-form-field
        type="select"
        label="Position"
        [(value)]="settings.coverImageFormat"
        (valueChange)="emit()"
        [items]="bgPositions"
      ></branding-settings-form-field>
    </div>
  </div>

  <div class="settings-group">
    <div class="row-group">
      <div class="group-header">Client Logo</div>
      <branding-settings-form-field
        type="select"
        label="Asset Name"
        [(value)]="settings.clientLogoPath"
        (valueChange)="emit()"
        [items]="assetsListItems"
      ></branding-settings-form-field>
      <branding-settings-form-field
        type="select"
        label="Position"
        [(value)]="settings.clientLogoPosition"
        (valueChange)="emit()"
        [items]="logoPositions"
      ></branding-settings-form-field>
    </div>
  </div>
</div>
