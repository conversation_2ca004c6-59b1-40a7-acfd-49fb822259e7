import { Client } from '@/shared/models';
import { bgPositions, logoPositions, BrandingSettings } from '@/shared/models/configurable-reports/brandingSettings';
import { ReportAsset } from '@/shared/models/configurable-reports/ReportAsset';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'branding-settings-form',
  templateUrl: './branding-settings-form.component.html',
  styleUrls: ['./branding-settings-form.component.scss'],
})
export class BrandingSettingsFormComponent implements OnInit {
  @Input() assets: ReportAsset[];
  @Input() settings: any;
  @Input() client: Client;

  @Output() settingsChange = new EventEmitter();

  defaults: BrandingSettings;
  savedForm: BrandingSettings;
  bgPositions = bgPositions;
  logoPositions = logoPositions;

  debouncedTimer: any;
  private readonly DEBOUNCE_TIME = 500;

  constructor() {}

  ngOnInit() {
    this.savedForm = new BrandingSettings();
    this.defaults = Object.assign(
      {
        solutionTextColour: '#959599',
        titleTextColour: '#005971',
        subTitleTextColour: '#000000',
        metadataTextColour: '#000000',
        metadataDetailsTextColour: '#000000',
        textInputTextColour: '#000000',
        footerBarColour: '#000000',
        coverImagePath: '',
        coverImageFormat: 'LowerTwoThirds',
        clientLogoPath: '',
        clientLogoPosition: 'Right',
      },
      this.settings,
    );
  }

  convertToListItems(assets: ReportAsset[]) {
    return assets.map(x => ({ label: x.fileName, value: x.fileUrl }));
  }

  get assetsListItems() {
    return this.assets.map(x => ({ label: x.fileName, value: x.fileUrl }));
  }

  useDefaultToggle(toggledOn, field) {
    if (toggledOn) {
      this.settings[field] = this.savedForm[field] || this.defaults[field];
      delete this.savedForm[field];
    } else {
      this.savedForm[field] = Object.assign({}, this.settings[field]);
      this.settings[field] = null;
    }
  }

  emit() {
    this.settingsChange.emit(this.settings);
  }

  debouncedEmit() {
    clearTimeout(this.debouncedTimer);
    this.debouncedTimer = setTimeout(() => {
      this.emit();
    }, this.DEBOUNCE_TIME);
  }

  getColorDependsOnBg(hex: string) {
    if (!hex || hex.length !== 7) {
      return 'black';
    }

    const red = parseInt(hex.slice(1, 3), 16);
    const green = parseInt(hex.slice(3, 5), 16);
    const blue = parseInt(hex.slice(5, 7), 16);
    return red * 0.299 + green * 0.587 + blue * 0.114 > 186 ? 'black' : 'white';
  }
}
