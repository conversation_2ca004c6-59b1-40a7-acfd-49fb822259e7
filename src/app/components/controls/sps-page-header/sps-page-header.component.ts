import { PageHeaderService } from '@/services';
import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { filter } from 'rxjs/operators';
import { combineLatest } from 'rxjs';
import { spsRoutes } from '@/shared/models/sps-client/sps-routes';

@Component({
  selector: 'sps-page-header',
  templateUrl: './sps-page-header.component.html',
  styleUrls: ['./sps-page-header.component.scss']
})
export class SpsPageHeaderComponent implements OnInit {
  title: string;
  subtitle: string;
  spsLinks: any[];
  spsClientName: string;
  stakeholderSubLinks: any[];
  collabSubLinks: any[];
  spsSurveyName: string
  showToolbar: boolean = false;
  stakeholderMenus = false;
  collaborationMenus = false;
  stakeholderName: string;
  spsClientId: string;

  constructor(private cd: ChangeDetectorRef, private router: Router, private route: ActivatedRoute, private pageHeaderService: PageHeaderService) { }

  ngOnInit() {
    this.router.events.pipe( 
      filter(event => event instanceof NavigationEnd)
    ).subscribe(() => {
      this.updateMenus();
    });
    combineLatest(
      this.pageHeaderService.spsTitle,
      this.pageHeaderService.spsSubTitle
    ).subscribe(([title, subtitle]) => {
      this.title = title;
      this.subtitle = subtitle;
      this.cd.detectChanges();
      this.updateLinks();
      this.updateMenus();
    });

  }

  updateMenus(){
    const firstChild = this.route.snapshot.firstChild;
    const currentRoute = firstChild.routeConfig.path || '';
    this.showToolbar = false;
    this.stakeholderMenus = false;
    this.collaborationMenus = false;
    if (currentRoute.includes('sps-')) {
      this.showToolbar = true;
    }
    if (currentRoute.includes(spsRoutes.STAKEHOLDER_DETAILS)) {
      this.stakeholderMenus = true;
    }
    else if (currentRoute.includes(spsRoutes.COLLABORATION_DETAILS) || currentRoute.includes(spsRoutes.COLLABORATION_STAKEHOLDER)) {
      this.collaborationMenus = true;
    }
    else{
      this.showToolbar = false;
    }
  }

  updateLinks() {
    this.spsLinks = this.getSPSLinks();
    this.stakeholderSubLinks = this.getStakeholderSubLinks()
    this.collabSubLinks = this.getCollabSubLinks();
  }

  getSPSLinks() {
    let selectedSpsClient = localStorage.getItem('selectedClient');
    const storedUser = JSON.parse(selectedSpsClient);
    this.spsClientName = storedUser.name;
    this.spsClientId = storedUser.id
    return [
      {
        label: 'stakeholder log',
        routerLink: ['/clients/sps-stakeholder-log'],
        href: `clients/sps-stakeholder-log`,
      },
      {
        label: 'collaboration log',
        routerLink: ['/clients/sps-collaboration-list'],
        href: `clients/sps-collaboration-list`,
      },
    ]
  }

  getStakeholderSubLinks() {
    let selectedClient = localStorage.getItem('selectedClient');
    const selectedSpsClient = JSON.parse(selectedClient);
    let stakeholder = localStorage.getItem('stakeholderDetailsData')
    if(stakeholder){
      let stakeholderDetails = JSON.parse(stakeholder);
        return [
          {
            label: 'Stakeholder Details',
            routerLink: ['/clients/sps-stakeholder-details'],
            queryParams: {
              emailId: stakeholderDetails.emailId,
            },
            href: `/clients/sps-stakeholder-details?emailId=${stakeholderDetails.emailId}`,
          }
        ]
      }
    }
   
  getCollabSubLinks() {
    const spsCollabDetails = localStorage.getItem('spsCollabDetails');
    let selectedClient = localStorage.getItem('selectedClient');
    const storedUser = JSON.parse(selectedClient);
    if(spsCollabDetails){
      const collabDetails = JSON.parse(spsCollabDetails);
      return [
        {
          label: 'Collaboration Details',
          routerLink: ['/clients/sps-collaboration-details'],
          queryParams: {
            collabId: collabDetails.collabId
          },
          href: `clients/sps-collaboration-details?collabId=${collabDetails.collabId}`,
        },
        {
          label: 'Collaboration Stakeholders',
          routerLink: ['/clients/sps-collaboration-stakeholder'],
          queryParams: {
            collabId: collabDetails.collabId
          },
          href: `clients/sps-collaboration-stakeholder?collabId=${collabDetails.collabId}`,
        }
      ]
    }
  }
}


