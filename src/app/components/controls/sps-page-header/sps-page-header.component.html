<mat-toolbar-row class="navigation navigation-sub-level">
  <ul class="nav-sub-level">
    <li class="entity">
      <a [routerLink]="['/clients']" [queryParams]="{clientId: spsClientId , search: spsClientName}">
        <mat-icon class="larger">public</mat-icon>
        <span class="entity-name"> {{ spsClientName}} </span>
        <mat-icon>chevron_right</mat-icon>
      </a>
    </li>

    <li *ngFor="let link of spsLinks">
      <a [routerLink]="link.routerLink" [queryParams]="link.queryParams" routerLinkActive="active">
        {{ link.label }}
      </a>
    </li>
  </ul>
</mat-toolbar-row>


<ul class="nav-sub-sub-level strong-glass" *ngIf="showToolbar && stakeholderMenus">
  <li class="entity">
    <a [routerLink]="['/clients/sps-stakeholder-log']" [queryParams]="{clientId: spsClientId }">
      <mat-icon class="larger">public</mat-icon>
      <span class="stakeholder-name"> {{ subtitle }} </span>
      <mat-icon>chevron_right</mat-icon>
    </a>
  </li>
  <li *ngFor="let link of stakeholderSubLinks">
    <a [routerLink]="link.routerLink" [queryParams]="link.queryParams" routerLinkActive="active">
      {{ link.label }}
    </a>
  </li>
</ul>

<ul class="nav-sub-sub-level strong-glass" *ngIf="showToolbar && collaborationMenus">
  <li *ngFor="let link of collabSubLinks">
    <a [routerLink]="link.routerLink" [queryParams]="link.queryParams" routerLinkActive="active">
      {{ link.label }}
    </a>
  </li>
</ul>

<div class="page-header">
  <h1 class="page-title">{{ title }} </h1>
  <div class="client-container">
    <span class="client">Client: </span>
    <span class="sps-client-name">{{ spsClientName}}</span>
  </div>

  <div class="client-container" *ngIf="showToolbar">
    <ng-container *ngIf="collaborationMenus">
      <span class="collaboration">Collaboration:</span> <span class="sub-title">{{ subtitle }}</span>
    </ng-container>
    <ng-container *ngIf="stakeholderMenus">
      <span class="stakeholder">Stakeholder:</span><span class="sub-title">{{ subtitle }}</span>
    </ng-container>
  </div>
</div>