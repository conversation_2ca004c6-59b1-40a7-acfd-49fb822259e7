@import "colors.scss";
@import "theme.scss";

:host {
  display: block;

  .page-header {
    padding: 64px;
    color: white;

    h1 {
      font-weight: bold;
      text-transform: uppercase;
    }
    table.subtitles {
      font-weight: 100;
      line-height: 1.5;
      font-size: 15px;

      .subtitle-key {
        font-weight: 400;
        text-transform: capitalize;
        opacity: 0.7;

        &::after {
          content: ":";
          margin-right: 12px;
        }
      }

      a.subtitle-value {
        font-weight: bold;
        letter-spacing: 1.5px;
        text-shadow: 0px 0px 3px rgba(0, 0, 0, 0.32);
        color: #ffffff;
        padding-bottom: 6px;
      }
    }
  }
}

.mat-menu-item.nav-link {
  padding-right: 36px;
  line-height: 36px;
  height: 36px;
  font-size: 14px;
}

$toolbar_bg: #f2f7f8;
$sub-level-icon-size: 18px;

mat-toolbar-row.navigation {
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  justify-content: stretch;
  min-height: 57px;
}

ul {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  list-style: none;
  padding: 0;
  margin: 0;
  width: 100%;
  height: 100%;
  background: #ffffff;
  min-height: 57px;

  li {
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: center;

    &.entity {
      a {
        color: $primary--green;
      }
    }

    &:hover {
      background-color: darken($toolbar_bg, 5%);
    }

    a {
      position: relative;
      color: black;
      font-size: 12px;
      font-weight: bold;
      font-style: normal;
      font-stretch: normal;
      letter-spacing: 1.2px;
      text-transform: uppercase;
      display: block;
      min-height: 57px;
      line-height: 57px;
      padding: 0 24px;
    }

    a.active:after {
      content: "";
      background-color: black;
      position: absolute;
      bottom: 18px;
      left: 24px;
      height: 2px;
      width: 28px;
    }
  }
}

mat-toolbar-row.navigation-sub-level {
  max-width: 100%;
  overflow-x: auto;
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, .2);
  max-height: 57px;

  .entity-name {
    display: inline-block;
    line-height: 12px;
    margin: 6px;
    max-width: 240px;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
    white-space: normal;
  }

  ul {
    li {
      white-space: nowrap;

      a {
        font-size: 10px;

        mat-icon {
          vertical-align: middle;
          font-size: $sub-level-icon-size;
          width: $sub-level-icon-size;
          height: $sub-level-icon-size;

          &.larger {
            font-size: $sub-level-icon-size * 1.2;
            width: $sub-level-icon-size * 1.2;
            height: $sub-level-icon-size * 1.2;
          }
        }
      }
    }
  }
}

.page-title {
  text-shadow: 0 2px 4px rgba(0, 0, 0, .25);
}

.nav-sub-sub-level {
  flex: 0 0 auto;

  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  color: white;
  height: 36px;
  min-height: 36px;
  border-radius: 0;
  // box-shadow: 0 2px 4px rgba(0, 0, 0, .2);

  li {
    padding: 0 6px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-transform: uppercase;
    white-space: nowrap;
    cursor: pointer;

    a {
      cursor: pointer;
      font-size: 9.5px;
      font-weight: 600;
      font-style: normal;
      font-stretch: normal;
      letter-spacing: 1px;
      color: #000000;
      position: relative;

      mat-icon {
        font-size: $sub-level-icon-size *.9;
        width: $sub-level-icon-size *.9;
        height: $sub-level-icon-size *.9;
      }

      &.active::after {
        content: "";
        background-color: black;
        position: absolute;
        left: 24px;
        height: 2px;
        width: 28px;
      }
    }
  }
}

.stakeholder-name{
  text-transform: capitalize;
  font-size: 10px;
}
.client-container {
  color: white;

  .sps-heading {
    font-size: 36px;
    font-weight: 700;
    padding-bottom: 10px;
  }

  .client,
  .collaboration,
  .stakeholder {
    color: rgba(255, 255, 255, 0.70);
    font-size: 15px;
    font-weight: 400;
    padding: 10px;
  }

  .sps-client-name,
  .sub-title {
    font-size: 15px;
    font-weight: 600;
  }

}