@import "../../../../styles/theme.scss";
@import "../../../../styles/colors.scss";

$dropdown-min-width: 360px;

.row {
  display: flex;
  justify-content: stretch;
  padding: 6px 0;
  width: 100%;

  &.changed {
    background: rgba(0, 0, 0, 0.05);
    font-weight: bold;
    margin: 0 -12px;
    padding: 6px 12px;
  }

  .col {
    min-width: 50%;
    width: auto;
    box-sizing: border-box;

    &.key {
      max-width: 150px;
      min-width: 150px;

      &.v-center {
        align-self: center;
      }
    }

    &.value {
      font-weight: bold;
      margin-left: 24px;
      max-height: 350px;
      overflow: auto;
    }
  }

  &.settings {
    .key {
      max-width: 210px;
      min-width: 210px;
    }
    .value {
      overflow: hidden;
    }
  }
}


.mat-menu-item-select {
  min-width: $dropdown-min-width;
  max-width: 1140px;
}

.menu-button {
  box-sizing: border-box;
  min-width: $dropdown-min-width;
  flex-basis: content;
  background: white;
  margin-right: 12px;
  padding: 6px 12px;
  border: thin solid rgba(145, 145, 145, 0.3);
  letter-spacing: 0.4px;
  color: $dark-primary-text;
  font: unquote($proxima-font);
  display: flex;
  justify-content: space-between;
  align-items: center;

  .mat-select-arrow {
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid $primary--grey-dark;
    margin-left: 12px;
    display: inline-block;
    vertical-align: middle;
  }
  
  &:disabled {
    color: grey;
    cursor: not-allowed;
  }
}

mat-checkbox.mat-checkbox-disabled,
mat-checkbox.mat-checkbox-disabled .mat-checkbox-layout {
  cursor: not-allowed;
}