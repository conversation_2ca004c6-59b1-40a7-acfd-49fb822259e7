import { ClientDetails } from '@/shared/models';
import { Component, Input, Output, EventEmitter, OnChanges } from '@angular/core';
import { NotificationHeader, NotificationType } from '@/shared/models/NotificationHeader';
import { SsoSetting } from '@/shared/models';

@Component({
  selector: 'app-client-default-settings',
  templateUrl: './client-default-settings.component.html',
  styleUrls: ['./client-default-settings.component.scss']
})
export class ClientDefaultSettingsComponent implements OnChanges {
  @Input()
  client: ClientDetails;

  @Input()
  notifications: NotificationHeader[];

  @Output()
  clientUpdateSubmitted = new EventEmitter<ClientDetails>();

  notSelectedLabel = 'Not selected';
  useCustomReflexDomainsTooltip = `Assessments and Demographics use the Reflex platform.
The Reflex platform can be accessed using 2 different URLs - one auto-generated by AWS, the other a custom Korn Ferry URL.
We recommend that clients use the custom Korn Ferry URL ("Use custom reflex domains" is checked), because it has the benefit of using the most up-to-date TLS 1.2 ciphers.
Some clients may have already whitelisted the auto-generated AWS URLs and will need to whitelist the new Korn Ferry URLs before this setting can be changed.
If you are unsure or cannot confirm with client then do not change this setting.`;

  updatedClient: ClientDetails;

  // enum collection
  SsoSetting = SsoSetting;
  NotificationType = NotificationType;
  defaultProctoringConfigurationId = 2; //Medium stake

  STAKE_LABELS: Record<string, string> = {
    High: 'High',
    Medium: 'Medium',
    Low: 'Low',
    HighVerifiedAccess: 'High + Verified Access',
  };

  ngOnChanges() {
    this.updatedClient = Object.assign({}, this.client);
  }

  getTemplateName(templateId: number) {
    const details = this.notifications.find(
      (template) => templateId === template.id
    );
    return details ? details.reference : this.notSelectedLabel;
  }

  getHeaders(type: number) {
    return (this.notifications || []).filter((header) => header.type === type);
  }

  changeSetting(setting: keyof ClientDetails, newValue: ClientDetails[keyof ClientDetails]) {
    this.updatedClient[setting as string] = newValue;
    this.updateDefaultProctoringId();
  }

  isAnyChanged() {
    return Object.keys(this.client).find(
      (setting: keyof ClientDetails) =>
        setting !== 'clients' &&
        this.updatedClient[setting] !== this.client[setting]
    );
  }

  isChanged(setting: keyof ClientDetails) {
    return this.updatedClient[setting] !== this.client[setting];
  }

  submitClientUpdate() {
    this.clientUpdateSubmitted.emit(this.updatedClient);
  }

  updateDefaultProctoringId() {
    const { isProctoringEnabled, defaultProctoringConfigurationId } = this.updatedClient;
    const clientDefaultId = this.client.defaultProctoringConfigurationId;

    if (isProctoringEnabled) {
      if (clientDefaultId === null && defaultProctoringConfigurationId === null) {
        this.updatedClient.defaultProctoringConfigurationId = this.defaultProctoringConfigurationId;
      }
    } else {
      this.updatedClient.defaultProctoringConfigurationId = clientDefaultId === null
        ? null
        : this.defaultProctoringConfigurationId;
    }
  }

  getSelectedProctoringStake() {
    let defaultProctoringId = this.updatedClient.defaultProctoringConfigurationId || this.defaultProctoringConfigurationId;
    let selectProctoring = this.updatedClient.proctoringConfigurations.find(
      (config) => config.proctoringConfigurationId === defaultProctoringId);

      return selectProctoring ? this.STAKE_LABELS[selectProctoring.name] || selectProctoring.name : 'Select';
  }
}