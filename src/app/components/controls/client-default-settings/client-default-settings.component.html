<div>
  <div class="row settings" [class.changed]="isChanged('dataProtectionAutoAcknowledge')">
    <div class="col key">Override Global Privacy Policy:</div>
    <div class="col value">
      <mat-checkbox (click)="$event.stopPropagation()"
        [checked]="updatedClient.dataProtectionAutoAcknowledge" (change)="
              $event
                ? changeSetting('dataProtectionAutoAcknowledge', $event.checked)
                : null
        "></mat-checkbox>
    </div>
  </div>

  <div class="row settings" [class.changed]="isChanged('hideBioDataPage')">
    <div class="col key">Hide Demographics section:</div>
    <div class="col value">
      <mat-checkbox (click)="$event.stopPropagation()"
        [checked]="updatedClient.hideBioDataPage"
        (change)="$event ? changeSetting('hideBioDataPage', $event.checked) : null"></mat-checkbox>
    </div>
  </div>

  <div class="row settings" [class.changed]="isChanged('useCustomReflexDomains')">
    <div class="col key" title={{useCustomReflexDomainsTooltip}}>Use custom reflex domains:</div>
    <div class="col value" title={{useCustomReflexDomainsTooltip}}>
      <mat-checkbox (click)="$event.stopPropagation()"
        [checked]="updatedClient.useCustomReflexDomains"
        (change)="$event ? changeSetting('useCustomReflexDomains', $event.checked) : null"></mat-checkbox>
    </div>
  </div>

  <!-- ENABLE PROCTORING -->
  <div class="row settings" [class.changed]="isChanged('isProctoringEnabled')">
    <div class="col key">Enable Proctoring for the Client:</div>
    <div class="col value">
      <mat-checkbox (click)="$event.stopPropagation()"
        [checked]="updatedClient.isProctoringEnabled"
        (change)="$event ? changeSetting('isProctoringEnabled', $event.checked) : null"></mat-checkbox>
    </div>
  </div>

    <!-- ENABLE TEST PROCTORING (READ ONLY) -->
    <div class="row settings" [class.changed]="isChanged('isTestProctoringEnabled')">
      <div class="col key">Enable Test Proctoring:</div>
      <div class="col value">
        <mat-checkbox
          class="checkbox"
          [checked]="updatedClient.isTestProctoringEnabled"
          disabled="true"
        ></mat-checkbox>
      </div>
    </div>

  <!-- DEFAULT PROCTORING STAKE -->
  <div class="row settings" [class.changed]="isChanged('defaultProctoringConfigurationId')">
    <div class="col v-center key">
      Select Proctoring Stake
    </div>
    <div class="col value">
      <mat-menu #ProctoringStakeMenu="matMenu" class="menu">
        <ng-container *ngFor="let stake of updatedClient.proctoringConfigurations">
          <button mat-menu-item class="mat-menu-item-select"
            (click)="changeSetting('defaultProctoringConfigurationId', +stake.proctoringConfigurationId)">
            {{ STAKE_LABELS[stake.name] || stake.name }} Stake
          </button>
        </ng-container>
      </mat-menu>

      <button class="menu-button" [matMenuTriggerFor]="ProctoringStakeMenu" [disabled]="!updatedClient.isProctoringEnabled">
        <ng-container>{{ getSelectedProctoringStake() }} Stake</ng-container>
        <span class="mat-select-arrow"></span>
      </button>
    </div>
  </div>

  <!-- DEFAULT INVITAION TEMPLATE ID -->
  <div class="row settings" [class.changed]="isChanged('defaultTemplateId')">
    <div class="col v-center key">
      Default invitation template:
    </div>
    <div class="col value">
      <mat-menu #InvitationMenu="matMenu" class="menu">
        <button mat-menu-item class="mat-menu-item-select" *ngFor="let template of getHeaders(NotificationType.INVITATION)"
          (click)="changeSetting('defaultTemplateId', template.id)">
          {{ getTemplateName(template.id) }}
        </button>
      </mat-menu>

      <button class="menu-button" [class.muted]="updatedClient.defaultTemplateId == null"
        [matMenuTriggerFor]="InvitationMenu">
        {{ getTemplateName(updatedClient.defaultTemplateId) }}
        <span class="mat-select-arrow"></span>
      </button>
    </div>
  </div>

  <!-- DEFAULT REMINDER TEMPLATE ID -->
  <div class="row settings" [class.changed]="isChanged('defaultReminderTemplateId')">
    <div class="col v-center key">Default reminder template:</div>
    <div class="col value">
      <mat-menu #ReminderMenu="matMenu" class="menu">
        <button mat-menu-item class="muted" (click)="changeSetting('defaultReminderTemplateId', null)">
          {{ notSelectedLabel }}
        </button>
        <button mat-menu-item class="mat-menu-item-select" *ngFor="let template of getHeaders(NotificationType.REMINDER)"
          (click)="changeSetting('defaultReminderTemplateId', template.id)">
          {{ getTemplateName(template.id) }}
        </button>
      </mat-menu>

      <button class="menu-button" [class.muted]="
         updatedClient.defaultReminderTemplateId == null
      " [matMenuTriggerFor]="ReminderMenu">
        {{
          getTemplateName(updatedClient.defaultReminderTemplateId)
        }}
        <span class="mat-select-arrow"></span>
      </button>
    </div>
  </div>

  <!-- DEFAULT REPORT RELEASE NOTIFICATION ID -->
  <div class="row settings" [class.changed]="isChanged('defaultReportReleaseNotificationId')">
    <div class="col v-center key">Default release template:</div>
    <div class="col value">
      <mat-menu #ReleaseMenu="matMenu" class="menu">
        <button mat-menu-item class="muted" (click)="changeSetting('defaultReportReleaseNotificationId', null)">
          {{ notSelectedLabel }}
        </button>
        <button mat-menu-item class="mat-menu-item-select" *ngFor="let template of getHeaders(NotificationType.RELEASE)"
          (click)="changeSetting('defaultReportReleaseNotificationId', template.id)">
          {{ getTemplateName(template.id) }}
        </button>
      </mat-menu>

      <button class="menu-button" [class.muted]="
        updatedClient.defaultReportReleaseNotificationId == null
      " [matMenuTriggerFor]="ReleaseMenu">
        {{
          getTemplateName(updatedClient.defaultReportReleaseNotificationId)
        }}
        <span class="mat-select-arrow"></span>
      </button>
    </div>
  </div>

  <!-- DEFAULT SSO TEMPLATE ID -->
  <div class="row settings" [class.changed]="isChanged('defaultSsoInvitationTemplateId')">
    <div class="col v-center key">Default SSO Invitation template:</div>
    <div class="col value">
      <mat-menu #SsoInvitationMenu="matMenu" class="menu">
        <button mat-menu-item class="muted" (click)="changeSetting('defaultSsoInvitationTemplateId', null)">
          {{ notSelectedLabel }}
        </button>
        <button mat-menu-item class="mat-menu-item-select" *ngFor="let template of getHeaders(NotificationType.INVITATION_SSO)"
          (click)="changeSetting('defaultSsoInvitationTemplateId', template.id)">
          {{ getTemplateName(template.id) }}
        </button>
      </mat-menu>

      <button class="menu-button"
        [class.muted]="updatedClient.defaultSsoInvitationTemplateId == null" [matMenuTriggerFor]="SsoInvitationMenu">
        {{ getTemplateName(updatedClient.defaultSsoInvitationTemplateId) }}
        <span class="mat-select-arrow"></span>
      </button>
    </div>
  </div>

  <div class="row settings" [class.changed]="isChanged('defaultSsoReminderTemplateId')">
    <div class="col v-center key">Default SSO Reminder template:</div>
    <div class="col value">
      <mat-menu #SsoReminderMenu="matMenu" class="menu">
        <button mat-menu-item class="muted" (click)="changeSetting('defaultSsoReminderTemplateId', null)">
          {{ notSelectedLabel }}
        </button>
        <button mat-menu-item class="mat-menu-item-select" *ngFor="let template of getHeaders(NotificationType.REMINDER_SSO)"
          (click)="changeSetting('defaultSsoReminderTemplateId', template.id)">
          {{ getTemplateName(template.id) }}
        </button>
      </mat-menu>

      <button class="menu-button"
        [class.muted]="updatedClient.defaultSsoReminderTemplateId == null" [matMenuTriggerFor]="SsoReminderMenu">
        {{ getTemplateName(updatedClient.defaultSsoReminderTemplateId) }}
        <span class="mat-select-arrow"></span>
      </button>
    </div>
  </div>

  <div class="row settings" [class.changed]="isChanged('defaultSsoSetting')">
    <div class="col v-center key">Default SSO Settings:</div>
    <div class="col value">
      <mat-menu #SsoMenu="matMenu" class="menu">
        <ng-container *ngFor="let ssoOption of SsoSetting | keyvalue">
          <button mat-menu-item class="mat-menu-item-select" *ngIf="+ssoOption.key == ssoOption.key"
            (click)="changeSetting('defaultSsoSetting', +ssoOption.key)">
            {{ SsoSetting[ssoOption.key] }}
          </button>
        </ng-container>
      </mat-menu>

      <button class="menu-button" [matMenuTriggerFor]="SsoMenu">
        {{ SsoSetting[updatedClient.defaultSsoSetting] }}
        <span class="mat-select-arrow"></span>
      </button>
    </div>
  </div>

  <div class="row settings">
    <div class="col key"></div>
    <div class="col value">
      <button removeIfUnauthorized="CanManageClients" [disabled]="!isAnyChanged()" class="btn btn-primary"
        (click)="submitClientUpdate()">
        Update Settings
      </button>
    </div>
  </div>
</div>