import { ApiService } from '@/services';
import { FileUploadModel } from '@/shared/models';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { HttpErrorResponse, HttpEventType } from '@angular/common/http';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { of } from 'rxjs';
import { catchError, last, map } from 'rxjs/operators';

/**
 * Component to handle file upload to server.
 */
@Component({
  selector: 'file-upload',
  templateUrl: './file-upload.component.html',
  styleUrls: ['./file-upload.component.scss'],
  animations: [
    trigger('fadeInOut', [
      state('in', style({ opacity: 100 })),
      transition('* => void', [animate(300, style({ opacity: 0 }))])
    ])
  ]
})
export class FileUploadComponent implements OnInit {
  /** Link text */
  @Input() text = 'Upload request';

  /** Name used in form which will be sent in HTTP request. */
  @Input() param = 'file';

  @Input() responseType:  'arraybuffer' | 'blob' | 'json' | 'text' = 'blob';

  /** Target URL for file uploading. */
  @Input() target: string;

  /** File extension that accepted, same as 'accept' of <input type="file" /> **/
  @Input() accept = '*';

  /** Optional hint to be shown at right of the button **/
  @Input() hint = '';

  /** Call back function  when upload is complete. */
  @Output() complete = new EventEmitter<string>();

  files: Array<FileUploadModel> = [];

  constructor(private apiService: ApiService) {}

  ngOnInit() {}

  onClick() {
    const fileUpload = document.getElementById(
      'fileUpload'
    ) as HTMLInputElement;
    fileUpload.onchange = () => {
      for (let index = 0; index < fileUpload.files.length; index++) {
        const file = fileUpload.files[index];
        this.files.push({
          data: file,
          state: 'in',
          inProgress: false,
          progress: 0,
          canRetry: false,
          canCancel: true,
          complete: false,
          errorMessage: ''
        });
      }
      this.uploadFiles();
    };
    fileUpload.click();
  }

  cancelFile(file: FileUploadModel) {
    if (file) {
      if (file.subscription) {
        file.subscription.unsubscribe();
      }
      this.removeFileFromArray(file);
    }
  }

  retryFile(file: FileUploadModel) {
    this.uploadFile(file);
    file.canRetry = false;
  }

  private uploadFile(file: FileUploadModel) {
    file.inProgress = true;
    file.errorMessage = '';
    file.subscription = this.apiService.postFile(this.target, file.data, this.responseType)
      .pipe(
        map(event => {
          switch (event.type) {
            case HttpEventType.UploadProgress:
              file.progress = Math.round((event.loaded * 100) / event.total) - 5;
              break;
            case HttpEventType.Response:
              return event;
          }
        }),
        last(),
        catchError((error: HttpErrorResponse) => {
          file.inProgress = false;
          file.canRetry = true;
          file.errorMessage = error.message;
          return of(`${file.data.name} upload failed.`);
        })
      )
      .subscribe((event: any) => {
        if (typeof event === 'object') {
          file.complete = true;
          file.inProgress = false;
          file.progress = 100;
          // this.removeFileFromArray(file);
          this.complete.emit(event.body);
        }
      });
  }

  private uploadFiles() {
    const fileUpload = document.getElementById(
      'fileUpload'
    ) as HTMLInputElement;
    fileUpload.value = '';

    this.files.forEach(file => {
      if (!file.inProgress) {
        this.uploadFile(file);
      }
    });
  }

  private removeFileFromArray(file: FileUploadModel) {
    const index = this.files.indexOf(file);
    if (index > -1) {
      this.files.splice(index, 1);
    }
  }
}
