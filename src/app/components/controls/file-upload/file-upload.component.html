<button mat-button  (click)="onClick()" type="button">
  <mat-icon>file_upload</mat-icon>
  {{text}}
</button>
<br/>
<ul>
  <li *ngFor="let file of files" [@fadeInOut]="file.state">
    <mat-progress-bar mode="determinate"  [value]="file.progress"></mat-progress-bar>
    <span id="file-label">
      {{file.data.name}}
      <a title="Retry" (click)="retryFile(file)" *ngIf="file.canRetry">
        <mat-icon>refresh</mat-icon>
      </a>
      <a title="Cancel" (click)="cancelFile(file)" *ngIf="file.canCancel">
        <mat-icon>cancel</mat-icon>
      </a>
      <span *ngIf="file.inProgress">
        in progress..
      </span>
      <span *ngIf="file.complete">
        complete
      </span>
      <span   class="error" *ngIf="file.canRetry">
        {{file.errorMessage}}
      </span>
    </span>
  </li>
</ul>
<input type="file" id="fileUpload" multiple  name="fileUpload" accept="{{accept}}" style="display:none;" />
