:host {
    flex: 0 0 auto;
    z-index: 1000; // show above spinner

    .kf-footer {
      position: relative;
      height: 45px;
      .kf-footer-content {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 45px;
        line-height: 45px;
        border-top: solid 1px #fff;
        background-color: #000;
        color: #fff;
        font-size: 10px;

        a {
            margin-left: 20px;
            color: #fff;

            &:hover {
                text-decoration: underline;
                cursor: pointer;
            }
        }
      }
    }
}
