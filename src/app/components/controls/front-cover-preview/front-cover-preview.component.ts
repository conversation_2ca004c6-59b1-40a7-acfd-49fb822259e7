import { bgPositions, logoPositions, BrandingSettings } from '@/shared/models/configurable-reports/brandingSettings';
import { ReportAsset } from '@/shared/models/configurable-reports/ReportAsset';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'front-cover-preview',
  templateUrl: './front-cover-preview.component.html',
  styleUrls: ['./front-cover-preview.component.scss'],
})
export class FrontCoverPreviewComponent implements OnInit {
  bgPositions = bgPositions;
  logoPositions = logoPositions;

  debouncedTimer: any;
  private readonly DEBOUNCE_TIME = 500;

  @Input() assets: ReportAsset[];
  @Input() settings: BrandingSettings;
  @Output() settingsChange = new EventEmitter<BrandingSettings>();

  @Input() report: any = {
    title: 'Report Title',
    subTitle: 'Client name',
    solutionName: 'Managerial Recruitment',
    info: [
      {
        name: 'Organization',
        value: 'HayGroup',
      },
      {
        name: 'Success profile',
        value: 'Account Manager Commercial Banking I',
      },
      {
        name: 'Assessed',
        value: 'Apr 30, 2018',
      },
      {
        name: 'Created',
        value: 'May 10, 2018',
      },
    ],
    textInputs: ['Interviewed By', 'Date'],
  };

  constructor() {}

  ngOnInit() {}

  emit() {
    this.settingsChange.emit(this.settings);
  }

  debouncedEmit() {
    clearTimeout(this.debouncedTimer);
    this.debouncedTimer = setTimeout(() => {
      this.emit();
    }, this.DEBOUNCE_TIME);
  }
}
