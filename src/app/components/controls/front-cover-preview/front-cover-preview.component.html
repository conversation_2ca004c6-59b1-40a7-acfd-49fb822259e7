<!-- Fornt-cover interactive preview -->
<div class="front-cover-preview background-{{ settings.coverImageFormat }}">

  <!-- Cover image -->
  <div class="bg" [style.background-image]="settings.coverImagePath | sanitizeCssUrl"></div>

  <!-- Cover title markup (upper 1/3 part) -->
  <div class="cover-title">
    <!-- Client logo -->
    <div class="client-logo {{ settings.clientLogoPosition }}">
      <img [src]="settings.clientLogoPath" />
    </div>
    <!-- Solution Name -->
    <app-color-chooser (colorChange)="debouncedEmit()" [(color)]="settings.solutionTextColour" cssClass="solution-name">
      {{ report.solutionName }}
    </app-color-chooser>
    <!-- Report Title -->
    <app-color-chooser (colorChange)="debouncedEmit()" [(color)]="settings.titleTextColour" cssClass="report-title">
      {{ report.title }}
    </app-color-chooser>
    <!-- Participant Name -->
    <app-color-chooser (colorChange)="debouncedEmit()" [(color)]="settings.subTitleTextColour" cssClass="report-subtitle">
      {{ report.subTitle }}
    </app-color-chooser>
  </div>

  <!-- Report Info Table Markup -->
  <div class="cover-info">
    <div class="flex-row">
      <!-- Report INFO :: Field names -->
      <app-color-chooser (colorChange)="debouncedEmit()" [(color)]="settings.metadataTextColour">
        <div class="line-height-20 property" *ngFor="let item of report.info">{{ item.name }}</div>
      </app-color-chooser>

      <!-- Report INFO :: Field values -->
      <app-color-chooser (colorChange)="debouncedEmit()" [(color)]="settings.metadataDetailsTextColour">
        <div class="line-height-20 value" *ngFor="let item of report.info">{{ item.value }}</div>
      </app-color-chooser>
    </div>

    <div class="flex-row">
      <!-- Text inputs :: Field names -->
      <app-color-chooser (colorChange)="debouncedEmit()" [(color)]="settings.textInputTextColour">
        <div class="line-height-20 value" *ngFor="let item of report.textInputs">{{ item }}</div>
      </app-color-chooser>

      <!-- Text inputs :: Fake inputs -->
      <div>
        <div *ngFor="let item of report.textInputs">
          <input type="text" class="mock-input" disabled />
        </div>
      </div>
    </div>
  </div>

  <!-- Horizontal line above the footer -->
  <app-color-chooser
    cpPosition="top"
    cssClass="footer-line"
    cssProperty="background"
    (colorChange)="debouncedEmit()" [(color)]="settings.footerBarColour"
  ></app-color-chooser>

  <div class="footer">
    <img *ngIf="settings.coBranded" class="kf-logo" src="assets/images/kf-logo.svg" />
    <span class="copyright float-right">© Korn Ferry 2018. All rights reserved. Confidential.</span>
  </div>
</div>

<!-- MAT-MENU -->
<mat-menu #logoAssetsMenu="matMenu" xPosition="before">
  <button
    mat-menu-item
    class="asset-item"
    *ngFor="let asset of assets"
    (click)="settings.clientLogoPath = asset.fileUrl; emit()"
  >
    <img class="thumb" [src]="asset.fileUrl" [alt]="asset.fileUrl" />
    {{ asset.fileName }}
  </button>
</mat-menu>

<mat-menu #coverAssetsMenu="matMenu" xPosition="before">
  <button
    mat-menu-item
    class="asset-item"
    *ngFor="let asset of assets"
    (click)="settings.coverImagePath = asset.fileUrl; emit()"
  >
    <img class="thumb" [src]="asset.fileUrl" [alt]="asset.fileUrl" />
    {{ asset.fileName }}
  </button>
</mat-menu>

<mat-menu #coverMenu="matMenu" xPosition="before">
  <button
    mat-menu-item
    class="asset-item"
    *ngFor="let item of bgPositions"
    (click)="settings.coverImageFormat = item.value; emit()"
  >
    {{ item.label }}
  </button>
</mat-menu>

<mat-menu #logoMenu="matMenu" xPosition="before">
  <button
    mat-menu-item
    class="asset-item"
    *ngFor="let item of logoPositions"
    (click)="settings.clientLogoPosition = item.value; emit()"
  >
    {{ item.label }}
  </button>
</mat-menu>
