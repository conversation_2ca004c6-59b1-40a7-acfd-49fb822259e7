@import '../../../../styles/colors.scss';
@import '../../../../styles/typography.scss';

$footer-height: 72px;

@at-root .select-thumb {
  width: 20px;
}

@at-root .select-thumb {
  width: 25px;
}

@at-root .thumb {
  width: 75px;
  vertical-align: middle;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
}

@at-root .asset-item {
  font: unquote($proxima-font);
  height: auto;
  padding: 6px 12px;
  font-weight: 600;
}

:host {
  .front-cover-preview {
    display: inline-block;
    min-width: 648px; // Letter page size
    max-width: 648px;
    min-height: 840px; // Letter page size
    max-height: 840px;
    padding: 87px;

    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
    color: #4d4d4f;
    font-size: 10pt;
    line-height: 1.6;
    letter-spacing: 0.3pt;
    background: white;

    height: 566pt;
    position: relative;

    .top-control-panel {
      position: absolute;
      z-index: 1;
      right: 0;
      top: 0;
      transform: translateY(-100%);

      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      align-items: center;

      .client-logo-actions,
      .cover-top-control-panel {
        margin-left: 9pt;
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;

        > * {
          margin: 3pt;

          &.mat-icon-button {
            width: auto;
            height: auto;
            line-height: 0;
            opacity: 0.2;

            &:hover {
              opacity: 1;
            }
          }
        }
      }

      .header {
        font-weight: 600;
        color: #c8c8c8;
        font-size: 7pt;
        text-transform: uppercase;
        white-space: nowrap;
      }
    }

    .left-control-panel {
      position: absolute;
      width: 36px;
      left: -48px;
      top: 0;

      a {
        display: block;
        width: 36px;
        font-size: 28px;
        text-align: center;
        color: #005971;
        opacity: 0.2;
        position: relative;

        &::before {
          content: attr(data-info);
          position: absolute;
          left: 0;
          transform: translateX(-100%);
          font-size: 0.6em;
          line-height: 36px;
          display: none;
        }

        &:hover {
          opacity: 1;
          &::before {
            display: block;
          }
        }
      }
    }

    .cover-title {
      height: 33%;
      position: relative;

      display: flex;
      flex-direction: column;
      justify-content: flex-end;

      .client-logo {
        position: absolute;
        top: 20px;

        img {
          max-height: 50pt;
          max-width: 85pt;
        }

        &.Right {
          right: 0;
        }

        &.Left {
          transform: translateY(24pt);
          height: 50pt;
          line-height: 50pt;

          img {
            vertical-align: middle;
          }
        }
      }
    }

    .cover-info {
      height: 66%;
      position: relative;

      .flex-row {
        margin-top: 30pt;
        display: flex;
        flex-direction: row;

        &:first-child {
          margin-top: 16pt;
        }

        > * {
          padding-right: 6pt;
        }

        .line-height-20 {
          line-height: 17px;
        }
        .property {
          text-transform: uppercase;
          font-weight: 600;
          font-size: 7pt;
          letter-spacing: 1.3px;
        }
        .value {
          font-size: 7.5pt;
        }

        .mock-input {
          width: 240px;
          height: 17px;
          transform: translateY(-2px);
          padding: 0;
          background: white;
        }
      }
    }

    .footer {
      height: 73px;
      padding: 0 36px;
      background: black;
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;

      display: flex;
      flex-direction: row;
      justify-content: stretch;
      align-items: center;

      span {
        font-size: 8px;
        line-height: 1.38;
        letter-spacing: 0.2px;
        text-align: right;
        line-height: 15pt;
        color: #919191;

        flex: 1 1 auto;
      }
    }

    .client-logo-custom {
      top: 62pt;
    }

    .co-logo-custom {
      bottom: 6pt;
    }

    .assets-popup {
      &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }

      &::-webkit-scrollbar-thumb {
        height: 15px;
        background-color: #c8c8c8;
      }

      &::-webkit-scrollbar-button {
        width: 8px;
        height: 8px;
        display: none;
      }

      &::-webkit-scrollbar-corner {
        background-color: transparent;
      }

      &::-webkit-scrollbar-track,
      &::-webkit-scrollbar-track-piece {
        background-color: #f8f8f8;
      }

      scrollbar-color: #c8c8c8 #f8f8f8;
      scrollbar-width: thin;

      background: white;
      width: 184px;
      height: auto;

      max-height: 400px;
      overflow-y: auto;
      z-index: 11;

      .asset-preview {
        position: relative;
        display: block;
        color: black;
        padding: 6pt 12pt;

        &:hover {
          background: $primary--blue-light;
        }

        img {
          max-height: 100px;
          max-width: 150px;
        }
      }
    }

    .client-logo-custom,
    .co-logo-custom,
    .bg-image-custom {
      position: absolute;
      width: 40px;
      right: -50px;
      height: 40px !important;
      background-image: url('../../../../assets/images/file-icons/image-icon.svg');
      background-repeat: no-repeat;
      background-size: contain;
      opacity: 0.1;
      cursor: pointer;

      &:hover {
        opacity: 1;
      }

      &.position {
        background-image: url('../../../../assets/images/arrows.svg');
        right: -100px;
      }
    }

    .bg {
      background-size: cover;
      position: absolute;
      left: 0;
      right: 0;
      overflow: hidden;
    }

    &.background-UpperThird .bg-image-custom {
      top: 0;
    }

    &.background-UpperThird .bg {
      top: 0;
      height: calc(33% - #{$footer-height});
    }

    &.background-LowerThird .bg-image-custom {
      top: 66%;
    }

    &.background-LowerThird .bg {
      bottom: $footer-height;
      height: calc(33% - #{$footer-height});
    }

    &.background-LowerTwoThirds .bg-image-custom {
      top: 33%;
    }

    &.background-LowerTwoThirds .bg {
      bottom: $footer-height;
      height: calc(66% - #{$footer-height});
    }

    &.background-FullPage .bg-image-custom {
      top: 0;
      bottom: $footer-height;
    }

    &.background-FullPage .bg {
      top: 0;
      bottom: $footer-height;
    }

    &.background-FullPageOverlay .bg-image-custom {
      top: 0;
      bottom: $footer-height;
    }

    &.background-FullPageOverlay .bg {
      top: 0;
      bottom: $footer-height;
    }

    &.background-NoImage .bg-image-custom {
      top: 0;
      bottom: $footer-height;
    }

    &.background-NoImage .bg {
      display: none;
    }
  }

  ::ng-deep .color-chooser {
    &.report-title {
      font-size: 19pt;
      font-weight: 600;
      line-height: 1.07;
      letter-spacing: 1.5px;
      text-transform: uppercase;
      margin-bottom: 3pt;
    }
    &.report-subtitle {
      font-size: 14pt;
      font-weight: 600;
      line-height: 1.15;
      letter-spacing: 0.5pt;
      margin-bottom: 22pt;
    }
    &.footer-line {
      position: absolute;
      bottom: 70px;
      left: 0;
      right: 0;
      z-index: 2;
      height: 1.5px;
    }
    &.solution-name {
      position: absolute;
      top: 20px;
      font-size: 7pt;
      font-weight: 600;
      line-height: 1.33;
      letter-spacing: 1.8px;
      text-transform: uppercase;

      &::after {
        content: '';
        position: absolute;
        bottom: -3px;
        width: 39px;
        height: 2px;

        left: 0;
        background: #bfbfbf;
      }
    }
  }
}
