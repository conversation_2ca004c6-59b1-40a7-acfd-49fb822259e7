<div class="field-label" [matTooltip]="label">{{ label }}</div>

<!-- nullable ckeckbox -->
<div class="field-toggle">
  <mat-checkbox
    #test
    *ngIf="nullable"
    type="checkbox"
    color="primary"
    [disableRipple]="true"
    [checked]="val"
    (change)="toggleNull()"
  >
  </mat-checkbox>
</div>
<a
  class="field-control tooltip-label"
  *ngIf="nullable && !value"
  (click)="toggleNull()"
  >Click to customize the field
</a>

<div class="field-control" *ngIf="!nullable || (nullable && value)">
  <!-- readonly -->
  <div class="readonly" *ngIf="type === 'readonly'">{{ value }}</div>

  <!-- toggle -->
  <mat-slide-toggle
    *ngIf="type === 'toggle'"
    [class.visible]="val"
    color="primary"
    type="checkbox"
    [disableRipple]="true"
    [checked]="val"
    (change)="val = $event.checked"
  ></mat-slide-toggle>

  <!-- color -->
  <app-color-chooser [cpPosition]="cpPosition" [(color)]="val" *ngIf="type === 'color'">
    <input
      readonly
      type="text"
      class="strong-glass"
      [value]="val"
      [style.background]="val"
      [style.color]="getColorDependsOnBg(value)"
      (input)="val = $event.target.value"
    />
  </app-color-chooser>

  <!-- select -->
  <select
    class="strong-glass"
    *ngIf="type === 'select'"
    (change)="val = $event.target.value"
  >
    <option value="" class="grey">Default</option>
    <option
      *ngFor="let option of items"
      [selected]="value === option.value"
      [value]="option.value"
    >
      {{ option.label }}
    </option>
  </select>

  <!-- text -->
  <input
    type="text"
    class="strong-glass"
    *ngIf="type === 'text'"
    [(ngModel)]="val"
  />
</div>
