:host {
  display: table-row;
  .field-label,
  .field-toggle,
  .field-control {
    display: table-cell;
    padding: 6pt;
  }
  .field-label {
    padding-left: 0;
    padding-bottom: 3pt;
    font-size: 0.9em;
    font-weight: 600;
    white-space: nowrap;
    width: 150px;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .field-toggle {
    padding-left: 0;
    padding-right: 0;
    width: 20px;
  }
  .field-control {
    padding-top: 1pt;
    padding-bottom: 1pt;
    padding-right: 0;
    width: 180px;
    input[type='text'],
    select {
      padding: 6px;
      width: 180px;
      box-sizing: border-box;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .field-control.tooltip-label {
    padding-left: 12px;
    font-size: 12px;
    font-weight: 600;
  }
}
