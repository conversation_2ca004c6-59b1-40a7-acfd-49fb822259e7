import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'branding-settings-form-field',
  templateUrl: './branding-settings-form-field.component.html',
  styleUrls: ['./branding-settings-form-field.component.scss'],
})
export class BrandingSettingsFormFieldComponent implements OnInit {
  @Input() label: string;
  @Input() type: 'toggle' | 'readonly' | 'color' | 'select' | 'text';
  @Input() items: { label: string; value: string }[];
  @Input() nullable: boolean;
  @Input() value: any;
  @Input() default: any;
  @Input() cpPosition = 'right'; // for type==='color' only

  @Output() valueChange = new EventEmitter();

  saved: any;

  constructor() {}

  ngOnInit() {
    if (!this.val && this.type === 'color' && !this.nullable) {
      this.val = this.default;
    }
  }

  toggleNull() {
    if (!this.val) {
      this.val = this.saved || this.default;
      return;
    }

    this.saved = JSON.parse(JSON.stringify(this.val));
    this.val = null;
  }

  get val() {
    return this.value;
  }

  set val(newVal) {
    this.value = newVal;
    this.valueChange.emit(newVal);
  }

  getColorDependsOnBg(hex: string) {
    if (!hex || hex.length !== 7) {
      return 'black';
    }

    const red = parseInt(hex.slice(1, 3), 16);
    const green = parseInt(hex.slice(3, 5), 16);
    const blue = parseInt(hex.slice(5, 7), 16);
    return red * 0.299 + green * 0.587 + blue * 0.114 > 186 ? 'black' : 'white';
  }
}
