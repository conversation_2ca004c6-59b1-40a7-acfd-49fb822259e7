import { forwardRef, AfterViewInit, Component, ElementRef, Injector, Input, NgZone, OnInit } from '@angular/core';
import { ControlValueAccessor, FormControl, NgControl, NG_VALUE_ACCESSOR, Validators } from '@angular/forms';

import { environment } from '@/shared/environments/environment';

declare const grecaptcha: any;

declare global {
  interface Window {
    grecaptcha: any;
    reCaptchaLoad: () => void;
  }
}

export interface RecaptchaConfig {
  theme?: 'dark' | 'light';
  type?: 'audio' | 'image';
  size?: 'compact' | 'normal';
  tabindex?: number;
}

@Component({
  selector: 'recaptcha',
  template: '',
  styleUrls: ['./recaptcha.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => RecaptchaComponent),
      multi: true
    }
  ]
})

export class RecaptchaComponent implements AfterViewInit, OnInit, ControlValueAccessor {
  @Input() config: RecaptchaConfig = {};
  @Input() lang: string;

  private key = environment.ADMIN_API_CAPTCHA_KEY;
  private onChange: Function;
  private onTouched: Function;
  private control: FormControl;
  private widgetId: number;

  constructor(
        private element: ElementRef,
        private ngZone: NgZone,
        private injector: Injector
  ) {

  }

  ngOnInit() {
    this.registerRecaptchaCallback();
    this.addScript();
  }

  ngAfterViewInit() {
    // use Asynchronous update
    // to fix Error: ExpressionChangedAfterItHasBeenCheckedError
    setTimeout(() => {
      // tslint:disable-next-line: deprecation
      this.control = this.injector.get(NgControl).control;
      this.setValidator();
    });
  }

  addScript() {
    const script = document.createElement('script');
    const lang = this.lang ? '&hl=' + this.lang : '';
    script.src = `https://www.google.com/recaptcha/api.js?onload=reCaptchaLoad&render=explicit${lang}`;
    script.async = true;
    script.defer = true;
    document.body.appendChild(script);
  }

  onExpired() {
    this.ngZone.run(() => {
      this.onChange(null);
      this.onTouched(null);
    });
  }

  onSuccess(token: string) {
    this.ngZone.run(() => {
      this.onChange(token);
      this.onTouched(token);
    });
  }

  // registerOnChange is required for custom ControlValueAccessor implementation
  registerOnChange(fn: Function): void {
    this.onChange = fn;
  }

  // registerOnTouched is required for custom ControlValueAccessor implementation
  registerOnTouched(fn: Function): void {
    this.onTouched = fn;
  }

  registerRecaptchaCallback() {
    window.reCaptchaLoad = () => {
      const config = {
        ...this.config,
        'sitekey': this.key,
        'callback': this.onSuccess.bind(this),
        'expired-callback': this.onExpired.bind(this)
      };
      this.widgetId = this.render(this.element.nativeElement, config);
    };
  }

  // writeValue is required for custom ControlValueAccessor implementation
  writeValue(obj: Object): void {
  }

  private render(element: HTMLElement, config: RecaptchaConfig): number {
    return grecaptcha.render(element, config);
  }

  private setValidator() {
    if (this.control) {
      this.control.setValidators(Validators.required);
      this.control.updateValueAndValidity();
    }
  }
}
