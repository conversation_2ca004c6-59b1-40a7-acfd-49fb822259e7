@import "../../../../styles/theme.scss";

.kf-tiles-wrapper {
    flex: 1 1 auto;

    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .kf-tiles {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      margin-bottom: 20px;

      .kf-tile {
        display: block;
        width: 226px;
        height: 331px;
        background: white;
        box-shadow: 0 2px 2px rgba(0, 0, 0, 0.24), 0 0 2px rgba(0, 0, 0, 0.12);
        text-align: center;
        font-size: 12px;
        margin: 5px;

        &:nth-child(1) {
          .kf-tile-logo,
          .kf-tile-hr {
            background-color: #566fd6;
          }

          .kf-tile-header {
            color: #566fd6;
          }
        }

        &:nth-child(2) {
          .kf-tile-logo,
          .kf-tile-hr {
            background-color: #458dde;
          }

          .kf-tile-header {
            color: #458dde;
          }
        }

        &:nth-child(3) {
          .kf-tile-logo,
          .kf-tile-hr {
            background-color: #45b7de;

            img {
              margin-left: 28%;
            }
          }

          .kf-tile-header {
            color: #45b7de;
          }
        }

        &:nth-child(4) {
          .kf-tile-logo,
          .kf-tile-hr {
            background-color: #404040;

            img {
              margin-left: 28%;
            }
          }

          .kf-tile-header {
            color: #404040;
          }
        }

        &:nth-child(5) {
          .kf-tile-logo,
          .kf-tile-hr {
            background-color: #d32534;

            img {
              margin-left: 28%;
            }
          }

          .kf-tile-header {
            color: #d32534;
          }
        }

        &:nth-child(6) {
          .kf-tile-logo,
          .kf-tile-hr {
            background-color: pink;

            img {
              margin-left: 28%;
            }
          }

          .kf-tile-header {
            color: pink;
          }
        }

        &:nth-child(7) {
          .kf-tile-logo,
          .kf-tile-hr {
            background-color: #d32534;

            img {
              margin-left: 28%;
            }
          }

          .kf-tile-header {
            color: #d32534;
          }
        }

        &-logo {
          width: 92px;
          height: 92px;
          margin: 86px auto 24px auto;
          border-radius: 50%;

          img {
            height: 50%;
            margin: 25%;
          }
        }

        &-header {
          text-transform: uppercase;
          font-weight: 100;
          font-size: 16px;
          letter-spacing: 2px;
          margin: 0 24px;
        }

        &-summary {
          color: $dark-disabled-text;
          margin: 12px 24px;
          line-height: 1.5;
          letter-spacing: 0.2px;
        }

        &-hr {
          width: 25px;
          height: 2px;
          margin: 0 auto;
          background: $dark-disabled-text;
        }

        &-link {
          margin: 27.5px auto;
          font-size: 12px;
          font-weight: 600;
          font-style: normal;
          font-stretch: normal;
          line-height: normal;
          letter-spacing: 1.2px;
          color: #007bc7;

          a:hover {
            text-decoration: underline;
            cursor: pointer;
          }
        }
      }
      .green {
        .kf-tile-logo,
          .kf-tile-hr {
            background-color: #32b561 !important;
          }
          .kf-tile-header {
            color: #32b561 !important;
          }
      }
      .yellow {
        .kf-tile-logo,
          .kf-tile-hr {
            background-color: #ced128 !important;
          }
          .kf-tile-header {
            color: #ced128 !important;
          }
      }
      .pink {
        .kf-tile-logo,
          .kf-tile-hr {
            background-color: #FFC0CB !important;
          }
          .kf-tile-header {
            color: #FFC0CB !important;
          }
      }
      .purple {
        .kf-tile-logo,
          .kf-tile-hr {
            background-color: #A74FFF !important;
          }
          .kf-tile-header {
            color: #A74FFF !important;
          }
      }
      .orange {
        .kf-tile-logo,
          .kf-tile-hr {
            background-color: #FFAA06 !important;
          }
          .kf-tile-header {
            color: #FFAA06 !important;
          }
      }
      .blue {
        .kf-tile-logo,
          .kf-tile-hr {
            background-color: #458DDE !important;
          }
          .kf-tile-header {
            color: #458DDE !important;
          }
      }
    }
  }
