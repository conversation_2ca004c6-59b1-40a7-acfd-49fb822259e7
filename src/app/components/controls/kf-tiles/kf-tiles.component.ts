import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { KfTile } from './kf-tile-model';

@Component({
  selector: 'kf-tiles',
  templateUrl: './kf-tiles.component.html',
  styleUrls: ['./kf-tiles.component.scss'],
})
export class KfTilesComponent implements OnInit {
  @Input() tiles: KfTile[];
  @Input() queryParams: any;
  @Output() tileClick = new EventEmitter<string>();

  constructor() {}

  ngOnInit() {}

  changeContext(childRoutes) {
    this.tiles = childRoutes; 
  }

  onTileClick(tile){
    this.tileClick.emit(tile); 
  }
}
