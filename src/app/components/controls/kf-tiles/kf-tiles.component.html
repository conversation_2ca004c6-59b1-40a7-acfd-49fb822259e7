<div class="kf-tiles-wrapper">
  <div class="kf-tiles">
    <ng-container *ngFor="let tile of tiles">
      <a
        *ngIf="!tile.childRoutes"
        class="kf-tile"
        [ngClass]="tile.bgColor"
        [(routerLink)]="tile.routerLink"
        [queryParams]="queryParams"
        [(allowedRoles)]="tile.allowedRoles"
        (click)="onTileClick(tile)"
      >
        <div class="kf-tile-logo"><img src="{{tile.logoUrl}}" alt=""></div>
        <div class="kf-tile-header">{{tile.title}}</div>
      </a>

      <a
        *ngIf="tile.childRoutes"
        class="kf-tile"
        [ngClass]="tile.bgColor"
        (click)="changeContext(tile.childRoutes)"
        [(allowedRoles)]="tile.allowedRoles"
      >
        <div class="kf-tile-logo"><img src="{{tile.logoUrl}}" alt=""></div>
        <div class="kf-tile-header">{{tile.title}}</div>
      </a>
    </ng-container>
  </div>
</div>
