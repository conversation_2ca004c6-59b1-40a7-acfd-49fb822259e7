<mat-toolbar-row
  class="navigation navigation-sub-level"
  *ngIf="subtitles && subtitles.client"
>
  <ul class="nav-sub-level">
    <li class="entity">
      <a
        [routerLink]="
          !subtitles.project && !subtitles.candidate
            ? ['/clients']
            : ['/clients', 'client-details']
        "
        [queryParams]="
          !subtitles.project && !subtitles.candidate
            ? {
                clientId: this.subtitles.client.id,
                search: this.subtitles.client.name
              }
            : { clientId: this.subtitles.client.id }
        "
      >
        <mat-icon class="larger">public</mat-icon>
        <span class="entity-name"> {{ subtitles.client.name }} </span>
        <mat-icon>chevron_right</mat-icon>
      </a>
    </li>

    <li *ngFor="let link of clientLinks">
      <a
        [routerLink]="link.routerLink"
        [queryParams]="link.queryParams"
        [allowedRoles]="link.allowedRoles"
        routerLinkActive="active"
      >
        {{ link.label }}
      </a>
    </li>
  </ul>
</mat-toolbar-row>

<ul class="nav-sub-sub-level strong-glass" *ngIf="subtitles.project || subtitles.candidate || showBrandingNavigation">
  <!-- Branding routes inline -->
  <ng-container *ngIf="showBrandingNavigation">
    <li class="entity">
      <a
        [routerLink]="['/clients', 'client-details']"
        [queryParams]="{ clientId: this.subtitles.client.id }"
      >
        <span class="entity-name"> Branding </span>
        <mat-icon>chevron_right</mat-icon>
      </a>
    </li>

    <li *ngFor="let link of clientBrandingLinks">
      <a
        [routerLink]="link.routerLink"
        [queryParams]="link.queryParams"
        routerLinkActive="active"
      >
        {{ link.label }}
      </a>
    </li>
  </ng-container>

  <!-- Project routes inline -->
  <ng-container *ngIf="subtitles.project && !subtitles.candidate">
    <li
      class="entity"
      [routerLink]="['/clients', 'client-projects']"
      [queryParams]="{ clientId: this.subtitles.client.id }"
    >
      <a>
        <mat-icon class="larger">device_hub</mat-icon>
        <span class="entity-name"> {{ subtitles.project.name }} </span>
        <mat-icon>chevron_right</mat-icon>
      </a>
    </li>

    <li *ngFor="let link of projectLinks">
      <a
        [routerLink]="link.routerLink"
        [queryParams]="link.queryParams"
        [allowedRoles]="link.allowedRoles"
        routerLinkActive="active"
      >
        {{ link.label }}
      </a>
    </li>
  </ng-container>

  <!-- Candidate routes inline -->
  <ng-container *ngIf="subtitles.candidate">
    <li
      class="entity"
      [routerLink]="['/clients', 'client-candidates']"
      [queryParams]="{ clientId: this.subtitles.client.id }"
    >
      <a>
        <mat-icon class="larger">person</mat-icon>
        <span class="entity-name"> {{ subtitles.candidate.name }} </span>
        <mat-icon>chevron_right</mat-icon>
      </a>
    </li>

    <li *ngFor="let link of candidateLinks">
      <a
        [routerLink]="link.routerLink"
        [queryParams]="link.queryParams"
        routerLinkActive="active"
      >
        {{ link.label }}
      </a>
    </li>
  </ng-container>
</ul>

<div class="page-header">
  <h1 class="page-title">{{ title }}</h1>
  <table class="subtitles" *ngIf="subtitles">
    <tr *ngIf="subtitles.client">
      <td>
        <span class="subtitle-key"> Client </span>
      </td>
      <td>
        <mat-menu #clientLinkMenu="matMenu">
          <a
            class="mat-menu-item nav-link"
            [routerLink]="link.routerLink"
            [queryParams]="link.queryParams"
            [allowedRoles]="link.allowedRoles"
            *ngFor="let link of clientLinks"
          >
            <mat-icon> {{ link.icon }} </mat-icon>
            {{ link.label }}
          </a>
        </mat-menu>

        <a class="subtitle-value" [matMenuTriggerFor]="clientLinkMenu">
          {{ subtitles.client.name }}
        </a>
      </td>
    </tr>
    <tr *ngIf="subtitles.project">
      <td>
        <span class="subtitle-key"> Project </span>
      </td>
      <td>
        <mat-menu #projectLinkMenu="matMenu">
          <a
            class="mat-menu-item nav-link"
            [routerLink]="link.routerLink"
            [queryParams]="link.queryParams"
            [allowedRoles]="link.allowedRoles"
            *ngFor="let link of projectLinks"
          >
            <mat-icon> {{ link.icon }} </mat-icon>
            {{ link.label }}
          </a>
        </mat-menu>

        <a class="subtitle-value" [matMenuTriggerFor]="projectLinkMenu">
          {{ subtitles.project.name }}

          <span
            class="tag-label aru"
            *ngIf="subtitles.project.showAruLabel"
            matTooltip="Participants under this project are allowed to reuse existing assessment results"
          >
            ARU
          </span>
          <span
            class="tag-label {{ subtitles.project.productType }}-module"
            matTooltip="This project was created under the {{
              subtitles.project.productType === 'TM' ? 'Assess' : 'Select'
            }} ({{ subtitles.project.productType }}) module"
          >
            {{ subtitles.project.productType }}
          </span>
          <span
            class="tag-label type"
            matTooltip="The project type is {{ subtitles.project.projectType }}"
          >
            {{ subtitles.project.projectType }}
          </span>
        </a>
      </td>
    </tr>
    <tr *ngIf="subtitles.candidate">
      <td>
        <span class="subtitle-key"> Participant </span>
      </td>
      <td>
        <mat-menu #candidateLinkMenu="matMenu">
          <a
            class="mat-menu-item nav-link"
            [routerLink]="link.routerLink"
            [queryParams]="link.queryParams"
            *ngFor="let link of candidateLinks"
          >
            <mat-icon> {{ link.icon }} </mat-icon>
            {{ link.label }}
          </a>
        </mat-menu>

        <a class="subtitle-value" [matMenuTriggerFor]="candidateLinkMenu">
          {{ subtitles.candidate.name | titlecase }}
        </a>
      </td>
    </tr>
  </table>
</div>
