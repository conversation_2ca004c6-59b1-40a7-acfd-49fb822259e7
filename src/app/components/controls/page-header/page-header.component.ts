import { AuthenticationService, PageHeaderService } from '@/services';
import { Auth<PERSON><PERSON>, AuthRolesList, PageHeaderSubtitle } from '@/shared/models';
import { Location } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { combineLatest } from 'rxjs';

@Component({
  selector: 'page-header',
  templateUrl: './page-header.component.html',
  styleUrls: ['./page-header.component.scss'],
})
export class PageHeaderComponent implements OnInit {
  title: string;
  subtitles: PageHeaderSubtitle;
  clientLinks: any[];
  projectLinks: any[];
  candidateLinks: any[];
  clientBrandingLinks: any[];
  showBrandingNavigation: boolean;

  constructor(private pageHeaderService: PageHeaderService, private authenticationService: AuthenticationService, private location: Location) {}

  ngOnInit() {
    combineLatest(
      this.pageHeaderService.title,
      this.pageHeaderService.subtitles,
      this.pageHeaderService.showBrandingNavigation,
    ).subscribe(([title, subtitles, showBrandingNavigation]) => {
      this.title = title;
      this.subtitles = subtitles;
      this.showBrandingNavigation = showBrandingNavigation;
      this.updateLinks();
    });
  }

  updateLinks() {
    this.clientLinks = this.getClientLinks();
    this.projectLinks = this.getProjectLinks();
    this.candidateLinks = this.getCandidateLinks();
    this.clientBrandingLinks = this.getClientBrandingLinks();
  }

  getClientBrandingLinks() {
    if (this.subtitles && this.subtitles.client) {
      return [
        {
          label: 'Branding Assets',
          routerLink: ['/clients/branding-assets'],
          queryParams: {
            clientId: this.subtitles.client.id
          },
          allowedRoles: ['admin', 'reportManagement', 'productDelivery'] as AuthRole[]
        },
        {
          label: 'Report Branding',
          routerLink: ['/clients/report-branding'],
          queryParams: {
            clientId: this.subtitles.client.id
          },
          allowedRoles: ['admin', 'reportManagement', 'productDelivery'] as AuthRole[]
        },
        {
          label: 'Participant Portal Branding',
          routerLink: ['/clients/portal-branding'],
          queryParams: {
            clientId: this.subtitles.client.id
          },
          allowedRoles: ['admin', 'reportManagement', 'productDelivery'] as AuthRole[]
        },
      ]
    }
  }

  getClientLinks() {
    if (this.subtitles && this.subtitles.client) {
      return [
        {
          icon: 'list_alt',
          label: 'Client Details',
          routerLink: ['/clients/client-details'],
          queryParams: {
            clientId: this.subtitles.client.id
          },
          href: `/clients/client-details?clientId=${this.subtitles.client.id}`,
          allowedRoles: AuthRolesList
        },
        {
          label: 'Report Management',
          routerLink: ['/clients/report-management'],
          queryParams: {
            clientId: this.subtitles.client.id
          },
          icon: 'users',
          allowedRoles: ['admin', 'reportManagement'] as AuthRole[]
        },
        {
          label: this.authenticationService.projectsAllowedOnlyForExtract ? 'Project Search' : 'Project Management',
          routerLink: ['/clients/client-projects'],
          queryParams: {
            clientId: this.subtitles.client.id
          },
          icon: 'data',
          allowedRoles: ['admin', 'productDelivery', 'projectParticipantManagement', 'dataScientist', 'participantExtract'] as AuthRole[]
        },
        {
          label: 'Participant search',
          routerLink: ['/clients/client-candidates'],
          queryParams: {
            clientId: this.subtitles.client.id
          },
          icon: 'users',
          allowedRoles: ['admin', 'productDelivery', 'projectParticipantManagement'] as AuthRole[]
        },
        {
          label: 'Client Customisation',
          routerLink: ['/clients/client-customisations'],
          queryParams: {
            clientId: this.subtitles.client.id
          },
          icon: 'users',
          allowedRoles: ['admin', 'productDelivery', 'projectParticipantManagement'] as AuthRole[]
        },
        {
          label: 'Usage Reports',
          routerLink: ['/clients/usage-report'],
          queryParams: {
            clientId: this.subtitles.client.id
          },
          icon: 'configurable',
          allowedRoles: ['admin', 'usageReporting'] as AuthRole[]
        },
      ];
    }

    return [];
  }

  getProjectLinks() {
    const regex = /selectedProjectId=(\d+)/g.exec(this.location.path());
    const selectedProjectId = regex && regex[1] || '';

    if (this.subtitles && this.subtitles.client && this.subtitles.project) {
      let items = [
        {
          icon: 'list_alt',
          label: 'Project Details',
          routerLink: ['/clients/project-details'],
          queryParams: {
            clientId: this.subtitles.client.id,
            projectId: this.subtitles.project.id,
            selectedProjectId: selectedProjectId,
          },
          href: `/clients/project-details?clientId=${this.subtitles.client.id}&projectId=${this.subtitles.project.id}&selectedProjectId=`,
          allowedRoles: ['admin', 'productDelivery', 'projectParticipantManagement'] as AuthRole[],
        },
        {
          icon: 'people',
          label: 'Project Participants',
          routerLink: ['/clients/project-candidates'],
          queryParams: {
            clientId: this.subtitles.client.id,
            projectId: this.subtitles.project.id,
            selectedProjectId: selectedProjectId,
          },
          href: `/clients/project-candidates?clientId=${this.subtitles.client.id}&projectId=${this.subtitles.project.id}`,
          allowedRoles: ['admin', 'productDelivery', 'projectParticipantManagement'] as AuthRole[],
        },
        {
          icon: 'email',
          label: 'Email Schedules',
          routerLink: ['/clients/project-email-schedules'],
          queryParams: {
            clientId: this.subtitles.client.id,
            projectId: this.subtitles.project.id,
            selectedProjectId: selectedProjectId,
          },
          href: `/clients/project-email-schedules?clientId=${this.subtitles.client.id}&projectId=${this.subtitles.project.id}`,
          allowedRoles: ['admin', 'productDelivery', 'projectParticipantManagement'] as AuthRole[],
        }
      ];

      if (this.subtitles.project.projectType.toUpperCase() !== 'SJT' && this.subtitles.project.projectType.toUpperCase() !== 'POTENTIAL') {
        items = items.concat({
          icon: 'file_upload',
          label: 'Participant Details Extract',
          routerLink: ['/clients/data-extracts-upload'],
          queryParams: {
            clientId: this.subtitles.client.id,
            projectId: this.subtitles.project.id,
            selectedProjectId: selectedProjectId,
          },
          href: `/clients/data-extracts-upload?clientId=${this.subtitles.client.id}&projectId=${this.subtitles.project.id}`,
          allowedRoles: ['admin', 'productDelivery', 'projectParticipantManagement', 'participantExtract', 'dataScientist'] as AuthRole[],
        });
      }

      return items;
    }

    return [];
  }

  getCandidateLinks() {
    const regex = /selectedProjectId=(\d+)/g.exec(this.location.path());
    const selectedProjectId = regex && regex[1] || '';

    if (this.subtitles && this.subtitles.candidate) {
      return [
        {
          icon: 'list_alt',
          label: 'Participant Details',
          routerLink: ['/clients/candidate-details'],
          queryParams: {
            clientId: this.subtitles.client.id,
            candidateId: this.subtitles.candidate.id,
          },
          href: `/clients/candidate-details?clientId=${this.subtitles.client.id}&candidateId=${this.subtitles.candidate.id}`,
        },
        {
          icon: 'account_tree',
          label: 'Participant Log & Jobs',
          routerLink: ['/clients/candidate-log'],
          queryParams: {
            clientId: this.subtitles.client.id,
            candidateId: this.subtitles.candidate.id,
          },
          href: `/clients/candidate-log?clientId=${this.subtitles.client.id}&candidateId=${this.subtitles.candidate.id}`,
        },
        this.subtitles.project && {
          icon: 'assessment',
          label: 'Manage Assessments',
          routerLink: ['/clients/candidate-manage-assessments'],
          queryParams: {
            clientId: this.subtitles.client.id,
            projectId: this.subtitles.project.id,
            candidateId: this.subtitles.candidate.id,
            selectedProjectId: selectedProjectId,
          },
          href:
            `/clients/candidate-manage-assessments?` +
            `clientId=${this.subtitles.client.id}&projectId=${this.subtitles.project.id}&candidateId=${this.subtitles.candidate.id}`,
        },
        this.subtitles.project && {
          icon: 'file_download',
          label: 'Report Status',
          routerLink: ['/clients/candidate-report-status'],
          queryParams: {
            clientId: this.subtitles.client.id,
            projectId: this.subtitles.project.id,
            candidateId: this.subtitles.candidate.id,
            selectedProjectId: selectedProjectId,
          },
          href:
            `/clients/candidate-report-status?` +
            `clientId=${this.subtitles.client.id}&projectId=${this.subtitles.project.id}&candidateId=${this.subtitles.candidate.id}`,
        },
      ].filter((link) => link);
    }

    return null;
  }
}
