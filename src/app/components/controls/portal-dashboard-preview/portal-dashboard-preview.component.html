<div class="signin-preview-wrapper" [class.mobile]="isMobileView">
  <div class="header">
    <div [style.background]="getHeaderBarColour()" class="header-bar">
      <img [src]="getLogoImageUrl()" alt="" />
      <div class="hamburger">
        <div
          class="bar"
          [style.background]="settings.dashboard.hamburgerTextColour"
        ></div>
        <div
          class="bar"
          [style.background]="settings.dashboard.hamburgerTextColour"
        ></div>
        <div
          class="bar"
          [style.background]="settings.dashboard.hamburgerTextColour"
        ></div>
      </div>
    </div>
    <div
      [style.background]="getHeaderBrandBarColour()"
      class="header-brand-bar"
    ></div>
  </div>

  <div class="main">
    <div
      class="background"
      [style.background-image]="getBackgroundImageUrl() | sanitizeCssUrl"
    >
      <img class="profile-pic" [src]="profileImageUrl" alt="" />
    </div>

    <div class="welcome">
      <div class="title">Welcome, preview candidate!</div>

      <div class="progress-bar-wrapper">
        <div
          class="progress-bar"
          [style.background]="settings.dashboard.progressBarColour"
        ></div>
      </div>

      <div class="completed-info">
        <b>Your Progress:</b> 2 of 3 Assessments Complete
      </div>
    </div>

    <div class="content-wrapper">
      <div class="content">
        <div class="title">Assessments to complete</div>
        <div class="assessment-table">
          <table>
            <tr>
              <th>Assessment</th>
              <th>Time</th>
              <th>Status</th>
              <th>Action</th>
            </tr>
            <tr *ngFor="let item of settings.dashboard.assessments">
              <td>
                <img [src]="item.url" alt="" class="icon" />
                <div class="info">
                  <div>
                    <b>{{ item.name }}</b>
                  </div>
                  <div>
                    Assess your observable skills and behaviors needed to
                    succeed in a role. You will be asked to rank statements
                    according to how accurately they reflect your behaviors.
                  </div>
                </div>
              </td>
              <td>30 mins</td>
              <td>In progress</td>
              <td>
                <div
                  class="continue-button"
                  [style.background]="settings.general.buttonBackgroundColour"
                  [style.color]="settings.general.buttonTextColour"
                >
                  Continue
                </div>
              </td>
            </tr>
          </table>
        </div>
      </div>
    </div>
  </div>

  <div class="footer">
    <div
      [style.background]="settings.general.footerBrandBarColour"
      class="footer-brand-bar"
    ></div>
    <div class="footer-bar">
      <img src="assets/images/logo-kornferryFooter.svg" alt="" /> © Korn Ferry
      2021 All Rights Reserved
    </div>
  </div>
</div>
