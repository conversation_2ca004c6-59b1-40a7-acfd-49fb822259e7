import { PortalBrandingSettingsData } from '@/shared/models';
import { ReportAsset } from '@/shared/models/configurable-reports/ReportAsset';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'portal-dashboard-preview',
  templateUrl: './portal-dashboard-preview.component.html',
  styleUrls: ['./portal-dashboard-preview.component.scss'],
})
export class PortalDashboardPreviewComponent implements OnInit {
  private readonly defaultBackgroundUrl = 'assets/images/portal-dashboard-bg.jpg';
  private readonly defaultLogoUrl = 'assets/images/logo-kornferry.svg';
  public readonly profileImageUrl = 'assets/images/portal-profile-pic.svg';

  debouncedTimer: any;
  private readonly DEBOUNCE_TIME = 500;

  @Input() isMobileView = false;
  @Input() assets: ReportAsset[];
  @Input() settings: PortalBrandingSettingsData;
  @Output() settingsChange = new EventEmitter<PortalBrandingSettingsData>();

  constructor() {}

  ngOnInit() {}

  getBackgroundImageUrl() {
    return this.settings.dashboard.heroImageUrl || this.defaultBackgroundUrl;
  }

  getLogoImageUrl() {
    return this.settings.general.logoImageUrl || this.defaultLogoUrl;
  }

  getHeaderBrandBarColour() {
    const main = this.settings.general.headerBrandBarColour;
    const left = this.settings.general.headerBrandBarColourLeft;
    const right = this.settings.general.headerBrandBarColourRight;

    return this.settings.general.headerBrandBarUseGradient
      ? `linear-gradient(to right, ${left}, ${right})`
      : main;
  }

  getHeaderBarColour() {
    const main = this.settings.general.headerBarColour;
    const left = this.settings.general.headerBarColourLeft;
    const right = this.settings.general.headerBarColourRight;

    return this.settings.general.headerBarUseGradient
      ? `linear-gradient(to right, ${left}, ${right})`
      : main;
  }

  emit() {
    this.settingsChange.emit(this.settings);
  }

  debouncedEmit() {
    clearTimeout(this.debouncedTimer);
    this.debouncedTimer = setTimeout(() => {
      this.emit();
    }, this.DEBOUNCE_TIME);
  }
}
