@import "../../../../styles/colors.scss";
@import "../../../../styles/theme.scss";
@import "../../../../styles/typography.scss";

:host {
  .signin-preview-wrapper {
    width: 810px;
    height: 490px;

    &.mobile {
      width: 280px;
      .main {
        .signin-form {
          margin: 0 auto;
        }
      }
    }

    background-color: white;
    background-size: cover;
    background-position: 50% 50%;

    display: flex;
    flex-direction: column;
    justify-content: stretch;
    align-items: stretch;

    .header {
      .header-bar {
        height: 42px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 1rem;
        box-sizing: border-box;
        img {
          max-height: 40px;
        }

        .hamburger {
          display: flex;
          justify-content: space-evenly;
          flex-direction: column;
          height: 23px;

          .bar {
            width: 16px;
            height: 1px;
          }
        }
      }
      .header-brand-bar {
        height: 3px;
      }
    }

    .main {
      flex: 1 1 auto;

      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: stretch;

      overflow: auto;
      position: relative;

      .background {
        min-height: 87.5px;
        height: 87.5px;

        background-size: 100% 100%;

        .profile-pic {
          height: 56px;
          width: 56px;
          background-color: #444343;
          box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.5);
          border: 2.5px solid #fff;
          border-radius: 50%;
          position: absolute;
          top: 87.5px;
          left: 50%;
          transform: translate(-50%, -72%);
        }
      }

      .welcome {
        border-bottom: solid thin #e6e2e2;
        .title {
          font-size: 20px;
          font-weight: 700;
          line-height: 1;
          text-align: center;
          color: #3a3a3a;
          margin-top: 30px;
        }

        .progress-bar-wrapper {
          width: 50%;
          height: 6px;
          margin: 18px auto;
          background-color: #e5e1e1;

          .progress-bar {
            width: 66.6%;
            height: 100%;
          }
        }
        .completed-info {
          font-size: 10px;
          text-align: center;
          margin-bottom: 18px;
        }
      }

      .content-wrapper {
        flex-grow: 1;
        background: #fafafa;

        .content {
          width: 70%;
          margin: 12px auto;
          .title {
            margin: 16px 0;
            font-size: 16px;
            line-height: 1.5;
          }
          .assessment-table {
            padding: 12px;
            box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.17);
            background: white;

            table {
              th {
                text-transform: uppercase;
                font-size: 9px;
                font-weight: 600;
                letter-spacing: 0.5px;
                color: #6b7880;

                min-width: 70px;
              }

              td {
                font-size: 9px;
                line-height: 1.15;
                padding-bottom: 12px;
                padding-top: 12px;
                border-bottom: solid thin #e6e2e2;
                vertical-align: middle;

                img {
                  float: left;
                  margin-right: 12px;
                  margin-right: 12px;
                  height: 65px;
                  width: 56px;
                }

                .continue-button {
                  padding: 6px 12px;
                  font-weight: bold;
                  display: inline-block;
                }
              }
            }
          }
        }
      }
    }

    .footer {
      .footer-brand-bar {
        height: 15px;
      }
      .footer-bar {
        height: 48px;
        background: $dark-background;
        display: flex;
        align-items: center;
        padding: 1rem;
        box-sizing: border-box;
        color: white;
        font-size: 6.5px;
        img {
          transform-origin: left center;
          transform: scale(0.75);
        }
      }
    }
  }
}
