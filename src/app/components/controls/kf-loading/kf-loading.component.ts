import { SpinnerService } from '@/services';
import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'kf-loading',
  templateUrl: './kf-loading.component.html',
  styleUrls: ['./kf-loading.component.scss'],
})
export class KFLoadingComponent implements OnInit {
  active: boolean;

  constructor(private spinnerService: SpinnerService) {}

  ngOnInit() {
    this.spinnerService.active.subscribe((state) => {
      this.active = state;
    });
  }
}
