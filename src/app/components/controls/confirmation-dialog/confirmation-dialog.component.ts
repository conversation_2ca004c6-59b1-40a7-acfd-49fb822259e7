import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';

export interface ConfirmationDialogData {
  action: string;
}

@Component({
  selector: 'confirmation-dialog',
  styleUrls: ['confirmation-dialog.component.scss'],
  templateUrl: 'confirmation-dialog.component.html',
})
export class ConfirmationDialogComponent {

  constructor(
    public dialogRef: MatDialogRef<ConfirmationDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmationDialogData) { }
}
