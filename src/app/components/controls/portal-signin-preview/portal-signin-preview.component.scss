@import "../../../../styles/colors.scss";
@import "../../../../styles/theme.scss";
@import "../../../../styles/typography.scss";

:host {
  .signin-preview-wrapper {
    width: 810px;
    height: 490px;

    &.mobile {
      width: 280px;
      .main {
        .signin-form {
          margin: 0 auto;
        }
      }
    }

    background-color: white;
    background-size: cover;
    background-position: 50% 50%;

    display: flex;
    flex-direction: column;
    justify-content: stretch;
    align-items: stretch;

    .header {
      .header-bar {
        height: 42px;
        display: flex;
        align-items: center;
        padding: 1rem;
        box-sizing: border-box;
        img {
          max-height: 40px;
        }
      }
      .header-brand-bar {
        height: 3px;
      }
    }

    .main {
      flex: 1 1 auto;

      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;

      .signin-form {
        margin-left: 12%;
        width: 280px;
        font-size: 11px;
        background-color: white;

        .language-select-container {
          background: #f2f2f2;
          display: flex;
          align-items: center;
          padding: 0.5rem;

          select {
            font-size: 11px;
            padding: 4px;
            margin-right: 0.5rem;
            flex-grow: 1;
          }
          .go {
            min-width: 0;
            padding: 2px;
            width: 2.3rem;
            background: #007da4;
            color: white;
            border-radius: 2px;
            font-size: 11px;
            text-align: center;
            font-weight: 100;
            margin-right: 0;
          }
        }
        .welcome {
          padding: 0.5rem;
          border-bottom: solid thin #f2f2f2;
          .welcome-title {
            font-size: 13px;
            font-weight: bold;
            letter-spacing: -0.5px;
            padding: 0.4em 0;
            line-height: 1.4;
          }
          .welcome-subtitle {
            font-size: 10px;
          }
        }
        .form {
          padding: 0.5rem;
          label {
            font-size: 10px;
          }
          input {
            display: block;
            padding: 0;
            border-radius: 2px;
            width: 100%;
            margin-bottom: 10px;
            height: 1.5rem;
          }
          .signin-button {
            padding: 6px;
            margin-top: 1rem;
            margin-bottom: 0.5rem;
            font-size: 11px;
            border-radius: 3px;
            border-width: 1px;
            text-align: center;
          }
          .forget-link {
            font-weight: bold;
            font-size: 10px;
            color: $primary--blue-medium;
          }
        }
      }
    }

    .footer {
      .footer-brand-bar {
        height: 15px;
      }
      .footer-bar {
        height: 48px;
        background: $dark-background;
        display: flex;
        align-items: center;
        padding: 1rem;
        box-sizing: border-box;
        color: white;
        font-size: 6.5px;
        img {
          transform-origin: left center;
          transform: scale(0.75);
        }
      }
    }
  }
}
