.row {
  display: flex;
  justify-content: stretch;
  padding: 6px 0;
  width: 100%;

  &.changed {
    background: rgba(0, 0, 0, 0.05);
    font-weight: bold;
    margin: 0 -12px;
    padding: 6px 12px;
  }

  .col {
    min-width: 50%;
    width: auto;
    box-sizing: border-box;

    &.key {
      max-width: 150px;
      min-width: 150px;

      &.v-center {
        align-self: center;
      }
    }

    &.value {
      font-weight: bold;
      margin-left: 24px;
      max-height: 350px;
      overflow: auto;
    }
  }

  &.settings {
    .key {
      max-width: 210px;
      min-width: 210px;
    }

    .value {
      overflow: hidden;
    }
  }
}