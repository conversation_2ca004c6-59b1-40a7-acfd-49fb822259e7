<div>
    <div class="row">
        <div class="col key">Client ID:</div>
        <div class="col value">
            {{ client.id }}
        </div>
    </div>

    <div class="row">
        <div class="col key">Client Name:</div>
        <div class="col value">
            {{ client.name }}
        </div>
    </div>

    <div class="row">
        <div class="col key">Created date:</div>
        <div class="col value">
            {{ client.dateCreated | date: "dd MMMM, yyyy" }}
        </div>
    </div>

    <div class="row">
        <div class="col key">UAM ID:</div>
        <div class="col value">
            {{ client.externalRef }}
        </div>
    </div>

    <div class="row" *ngIf="client.clients && client.clients.length">
        <div class="col key">User groups:</div>
        <div class="col value">
            <div *ngFor="let group of client.clients">
                {{ group.name }}
            </div>
        </div>
    </div>
</div>