$toolbar_bg: #f2f7f8;

mat-toolbar {
  background-color: $toolbar_bg;
}

mat-toolbar ul {
  display: flex;
  flex-direction: row;
  justify-content: center;
  box-sizing: border-box;
  list-style: none;
  padding: 0;
  margin: 0;
  width: 100%;

  li {
    display: flex;

    a {
      position: relative;
      padding: 16px 24px;
      color: black;
      font-size: 12px;
      font-weight: 600;
      letter-spacing: 2px;
      text-transform: uppercase;

      &:hover {
        background-color: darken($toolbar_bg, 10%);
      }
    }
  }

  li.is-active {
    a:after {
      content: '';
      background-color: black;
      position: absolute;
      bottom: 16px;
      left: 24px;
      height: 2px;
      width: 30px;
    }
  }
}
