import { AssessmentValidityPeriod } from "@/shared/models/assessment-validity-period/assessment-validity-period";
import { KFTableColumn } from "@/components/controls";

export const VALIDITY_PERIODS_TABLE_COLUMNS: KFTableColumn<AssessmentValidityPeriod>[] = [
  {
    name: 'clientAssessmentValidityPeriodId',
    label: 'ID',
    type: 'text',
    width: '65px'
  },
  {
    name: 'assessmentTypeAsText',
    label: 'ASSESSMENT TYPE',
    type: 'text'
  },
  {
    name: 'periodTypeAsText',
    label: 'PERIOD TYPE',
    type: 'text'
  },
  {
    name: 'periodDuration',
    label: 'PERIOD DURATION',
    type: 'text',
  },
  {
    name: 'clientId',
    label: 'FROM CLIENT ID',
    type: 'text'
  },
  {
    name: 'actions',
    label: 'Actions',
    type: 'rowOptions',
    width: '200px'
  }
]
