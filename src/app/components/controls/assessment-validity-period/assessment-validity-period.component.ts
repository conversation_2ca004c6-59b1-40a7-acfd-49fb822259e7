import { AssessmentValidityPeriod } from "@/shared/models/assessment-validity-period/assessment-validity-period";
import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { KFTableAction } from "@/components/controls";
import { VALIDITY_PERIODS_TABLE_COLUMNS } from "./assessment-validity-period.columns";

@Component({
  selector: 'app-assessment-validity-period',
  templateUrl: './assessment-validity-period.component.html',
  styleUrls: ['./assessment-validity-period.component.scss'],
})
export class AssessmentValidityPeriodComponent implements OnInit {
  @Input()
  filter: string;

  @Input()
  periodsInheritedFromParent: boolean;

  @Input()
  validityPeriods: AssessmentValidityPeriod[];

  @Output()
  onEditPeriodClicked = new EventEmitter<AssessmentValidityPeriod>();

  @Output()
  onDeletePeriodClicked = new EventEmitter<AssessmentValidityPeriod>();

  actions: KFTableAction<AssessmentValidityPeriod>[] = [
    {
      label: 'Edit',
      click: (p) => this.onEditPeriodClicked.emit(p)
    },
    {
      label: 'Delete',
      click: (p) => this.onDeletePeriodClicked.emit(p)
    }
  ];

  allColumns = VALIDITY_PERIODS_TABLE_COLUMNS;
  columns = [];

  ngOnInit() {
    this.columns = this.periodsInheritedFromParent
    ? this.allColumns.filter(c => c.name != 'actions')
    : this.allColumns;
  }
}
