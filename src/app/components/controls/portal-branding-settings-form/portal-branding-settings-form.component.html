<mat-menu #sectionMenu="matMenu" xPosition="before">
  <button mat-menu-item style="width: 300px" *ngFor="let item of views" (click)="onViewSelect(item)">
    {{ item.name }}
  </button>
</mat-menu>

<a class="section-select-button" [matMenuTriggerFor]="sectionMenu">
  <span>{{ selectedView.name }}</span>
  <span class="caret"></span>
</a>

<div class="settings-form">
  <div class="settings-group" *ngIf="selectedView.type === 'general'">
    <div class="row-group">
      <div class="group-header">Logo</div>
      <branding-settings-form-field
        type="select"
        label="Asset name"
        [(value)]="settings.general.logoImageUrl"
        (valueChange)="debouncedEmit()"
        [items]="assetsListItems"
      ></branding-settings-form-field>
    </div>
    <div class="row-group">
      <div class="group-header">Header Bar</div>
      <branding-settings-form-field
        type="toggle"
        default="false"
        label="Use Gradient"
        [(value)]="settings.general.headerBarUseGradient"
        (valueChange)="debouncedEmit()"
      ></branding-settings-form-field>
      <branding-settings-form-field
        type="color"
        *ngIf="!settings.general.headerBarUseGradient"
        default="#ffffff"
        label="Header Bar Colour"
        [(value)]="settings.general.headerBarColour"
        (valueChange)="debouncedEmit()"
      ></branding-settings-form-field>
      <branding-settings-form-field
        type="color"
        *ngIf="settings.general.headerBarUseGradient"
        default="#ffffff"
        label="Gradient Left"
        [(value)]="settings.general.headerBarColourLeft"
        (valueChange)="debouncedEmit()"
      ></branding-settings-form-field>
      <branding-settings-form-field
        type="color"
        *ngIf="settings.general.headerBarUseGradient"
        default="#cccccc"
        label="Gradient Right"
        [(value)]="settings.general.headerBarColourRight"
        (valueChange)="debouncedEmit()"
      ></branding-settings-form-field>
    </div>
    <div class="row-group">
      <div class="group-header">Header Brand Bar</div>
      <branding-settings-form-field
        type="toggle"
        default="false"
        label="Use Gradient"
        [(value)]="settings.general.headerBrandBarUseGradient"
        (valueChange)="debouncedEmit()"
      ></branding-settings-form-field>
      <branding-settings-form-field
        type="color"
        *ngIf="!settings.general.headerBrandBarUseGradient"
        default="#f2f2f2"
        label="Brand Bar Colour"
        [(value)]="settings.general.headerBrandBarColour"
        (valueChange)="debouncedEmit()"
      ></branding-settings-form-field>
      <branding-settings-form-field
        type="color"
        *ngIf="settings.general.headerBrandBarUseGradient"
        default="#cccccc"
        label="Gradient Left"
        [(value)]="settings.general.headerBrandBarColourLeft"
        (valueChange)="debouncedEmit()"
      ></branding-settings-form-field>
      <branding-settings-form-field
        type="color"
        *ngIf="settings.general.headerBrandBarUseGradient"
        default="#ffffff"
        label="Gradient Right"
        [(value)]="settings.general.headerBrandBarColourRight"
        (valueChange)="debouncedEmit()"
      ></branding-settings-form-field>
    </div>
    <div class="row-group">
      <branding-settings-form-field
        type="color"
        default="#ffffff"
        label="Button Text"
        cpPosition="top"
        [(value)]="settings.general.buttonTextColour"
        (valueChange)="debouncedEmit()"
      ></branding-settings-form-field>
      <branding-settings-form-field
        type="color"
        default="#007da4"
        label="Button Background"
        cpPosition="top"
        [(value)]="settings.general.buttonBackgroundColour"
        (valueChange)="debouncedEmit()"
      ></branding-settings-form-field>
      <branding-settings-form-field
        type="color"
        default="#005184"
        label="Button Hover Color"
        cpPosition="top"
        [(value)]="settings.general.buttonHoverColour"
        (valueChange)="debouncedEmit()"
      ></branding-settings-form-field>
      <branding-settings-form-field
        type="color"
        default="#106550"
        label="Footer Brand Bar"
        cpPosition="top"
        [(value)]="settings.general.footerBrandBarColour"
        (valueChange)="debouncedEmit()"
      ></branding-settings-form-field>
    </div>
  </div>

  <div class="settings-group" *ngIf="selectedView.type === 'signin'">
    <div class="row-group">
      <div class="group-header">Large Image</div>
      <branding-settings-form-field
        type="select"
        label="Asset name"
        [(value)]="settings.signIn.desktopImageUrl"
        (valueChange)="debouncedEmit()"
        [items]="assetsListItems"
      ></branding-settings-form-field>
    </div>
    <div class="row-group">
      <div class="group-header">Small Image</div>
      <branding-settings-form-field
        type="select"
        label="Asset name"
        [(value)]="settings.signIn.mobileImageUrl"
        (valueChange)="debouncedEmit()"
        [items]="assetsListItems"
      ></branding-settings-form-field>
    </div>
    <div class="row-group">
      <branding-settings-form-field
        type="text"
        label="Custom Manifest"
        [(value)]="settings.signIn.customManifest"
        (valueChange)="debouncedEmit()"
      ></branding-settings-form-field>
    </div>
  </div>

  <div class="settings-group" *ngIf="selectedView.type === 'dashboard'">
    <div class="row-group">
      <div class="group-header">Background Image</div>
      <branding-settings-form-field
        type="select"
        label="Asset name"
        [(value)]="settings.dashboard.heroImageUrl"
        (valueChange)="debouncedEmit()"
        [items]="assetsListItems"
      ></branding-settings-form-field>
    </div>

    <div class="row-group">
      <div class="group-header">Colours</div>
      <branding-settings-form-field
        type="color"
        default="#3a3a3a"
        label="Hamburger Colour"
        cpPosition="top"
        [(value)]="settings.dashboard.hamburgerTextColour"
        (valueChange)="debouncedEmit()"
      ></branding-settings-form-field>
      <branding-settings-form-field
        type="color"
        default="#01809d"
        label="Progress Bar Colour"
        cpPosition="top"
        [(value)]="settings.dashboard.progressBarColour"
        (valueChange)="debouncedEmit()"
      ></branding-settings-form-field>
    </div>

    <div class="row-group">
      <div class="group-header">Assessment Icons</div>
      <branding-settings-form-field
        type="select"
        *ngFor="let item of settings.dashboard.assessments"
        [label]="item.name"
        [(value)]="item.url"
        (valueChange)="debouncedEmit()"
        [items]="assetsListItems"
      ></branding-settings-form-field>
    </div>

    <div class="row-group">
      <branding-settings-form-field
        type="text"
        label="Custom Manifest"
        [(value)]="settings.dashboard.customManifest"
        (valueChange)="debouncedEmit()"
      ></branding-settings-form-field>
    </div>
  </div>

  <div class="settings-group" *ngIf="selectedView.type === 'manifests'">
    <branding-settings-form-field
      type="text"
      *ngFor="let item of settings.assessmentManifests"
      [label]="item.name"
      [(value)]="item.manifest"
      (valueChange)="debouncedEmit()"
    ></branding-settings-form-field>
  </div>
</div>
