@import "../../../../styles/theme.scss";

:host {
  .settings-form {
    display: block;
    @extend .glass;
    padding: 24px;

    .settings-group {
      .row-group {
        &:not(:last-of-type) {
          margin-bottom: 24px;
        }

        .group-header {
          font-weight: 600;
          color: rgba(0, 0, 0, 0.5);
          margin-bottom: 3pt;
          text-transform: uppercase;
        }
      }
    }
  }

  .section-select-button {
    background: white;
    width: 100%;
    display: block;
    margin-bottom: 12px;
    padding: 12px 24px;
    box-sizing: border-box;
    border: solid thin rgba(0, 0, 0, 0.4);

    .caret {
      float: right;
      border: solid 6px transparent;
      border-top-color: $primary--blue;
      transform: translateY(50%);
      opacity: 0.8;
    }
  }
}
