<div class="kf-select {{ size ? 'size-' + size : '' }}">
  <mat-icon class="input-icon">filter_list</mat-icon>

  <form [formGroup]="optionsForm">
    <mat-select
      *ngIf="multiple"
      multiple
      disableOptionCentering
      formControlName="options"
      [ngModel]="options"
      [placeholder]="placeholder"
      (selectionChange)="onSelect($event)"
    >
      <mat-select-trigger>
        {{ placeholder }}
        <span *ngIf="selection" class="selection-count"> ({{ selection.length }})</span>
      </mat-select-trigger>

      <div class="searchBox">
        <input type="search" formControlName="filterkey" />
        <div class='actions'>
          <a (click)="selectAll()">SELECT ALL</a>
          <a *ngIf="selection.length > 0" (click)="clearAll()">CLEAR ALL</a>
        </div>
      </div>

      <ng-container *ngFor="let item of filteredOptions">
        <h4 class="group-label" *ngIf="!item.id">
          {{ item.label | uppercase }}
        </h4>
        <mat-option *ngIf="item.id" class="group-option" [value]="item.id" [disabled]="item.disabled">
          {{ item.value }}
        </mat-option>
      </ng-container>
    </mat-select>

    <mat-select
      *ngIf="!multiple"
      [placeholder]="placeholder"
      [(value)]="selection"
      disableOptionCentering
      (selectionChange)="onSelect($event)"
    >
      <div class="searchBox">
        <input type="search" formControlName="filterkey" />
      </div>
      <ng-container *ngFor="let item of filteredOptions">
        <h4 class="group-label" *ngIf="!item.id">
          {{ item.label | uppercase }}
        </h4>
        <mat-option *ngIf="item.id" class="group-option" [value]="item.id" [disabled]="item.disabled">
          {{ item.value }}
        </mat-option>
      </ng-container>
    </mat-select>
  </form>
</div>
