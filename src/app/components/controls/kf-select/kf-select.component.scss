@import "../../../../styles/colors.scss";
@import "../../../../styles/typography.scss";

:host {
  &:not(:last-child) {
    margin-right: 12px;
  }

  .kf-select {
    .input-icon {
      z-index: 1;
      width: 0;
      transform: translate(12px, 0px);
      color: $primary--blue;
    }

    .mat-select {
      width: 200px;
      background: white;
      border: thin solid rgba(145, 145, 145, 0.3);

      &::ng-deep .mat-select-trigger {
        padding: 12px;
        font: unquote($proxima-font);
      }

      &::ng-deep .mat-select-value {
        padding-left: 36px;
        font: unquote($proxima-font);

        .selection-count {
          color: rgba(#000, 0.37);
        }

        .mat-select-placeholder {
          color: rgba(#000, 0.87);
        }
      }
    }

    &.size {
      &-md {
        .mat-select {
          width: 200px;
        }
      }
      &-lg {
        .mat-select {
          width: 250px;
        }
      }
      &-xl {
        .mat-select {
          width: 320px;
        }
      }
    }
  }
}

.group-name {
  padding: 12px;
  font-size: 10pt;
  font-weight: bold;
  line-height: 3;
}

.group-label {
  font-size: 14px;
  padding: 0 16px;
  height: 3em;
  line-height: 3em;
}

form {
  display: initial;
}

.searchBox {
  padding: 6pt;
  input {
    width: 100%;
    padding: 6pt;
  }

  .actions {
    display: flex;
    justify-content: space-between;

    a {
      display: inline-block;
      margin: 6pt;
    }
  }
}

::ng-deep .mat-option-text {
  font: unquote($proxima-font);
  font-size: 9pt;
}
