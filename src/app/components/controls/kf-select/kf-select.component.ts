import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup } from '@angular/forms';
import * as _ from 'lodash';

@Component({
  selector: 'kf-select',
  templateUrl: './kf-select.component.html',
  styleUrls: ['./kf-select.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class KfSelectComponent<T extends ISelectOption> implements OnInit {
  // if items should not be splitted by groups - pass empty groupName
  @Input() size: 'md' | 'lg' | 'xl' = 'md';
  @Input() multiple = true;
  @Input() disableSort = false;
  @Input() placeholder: string;
  @Input() options: T[] = [];
  @Input() selection = [];
  @Output() selectionChange = new EventEmitter<T[]>();

  filterkey = '';
  optionsForm: any;

  constructor(private fb: FormBuilder) {}

  ngOnInit() {
    this.options = _.sortBy(this.options, ['value', 'id']);

    this.optionsForm = this.fb.group({
      options: new FormControl(this.selection),
      filterkey: new FormControl(''),
    });
  }

  onSelect(event) {
    this.selection = event.value;
    this.optionsForm.controls.options.setValue(this.selection);
    this.selectionChange.emit(event.value);
  }

  get filteredOptions() {
    return this.optionsForm.controls.filterkey.value
      ? this.options.filter(
          option =>
            option.value &&
            option.value
              .toString()
              .toLowerCase()
              .indexOf(this.optionsForm.controls.filterkey.value.toLowerCase()) >= 0,
        )
      : this.options;
  }

  clearAll() {
    const value = [];
    this.onSelect({ value });
  }

  selectAll() {
    const value = this.options.filter(o => !o.disabled).map(o => o.id).slice();
    this.onSelect({ value });
  }
}

export interface ISelectOption {
  disabled: boolean;
  value: string | number;
  id: string | number;
}
