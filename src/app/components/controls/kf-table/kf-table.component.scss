@import "../../../../styles/theme.scss";
@import "../../../../styles/colors.scss";
@import "../../../../styles/typography.scss";

:host {
  .mat-column-select {
    max-width: 75px;
  }

  mat-table.size-small {
    &:not(.no-max-height) {
      max-height: 250px;
      overflow: auto;
    }
    mat-header-row {
      min-height: 36px;
      mat-header-cell.mat-header-cell {
        font-size: 12px;
        padding: 3px 24px;
      }
    }
    mat-row {
      min-height: 24px;
      mat-cell.mat-cell {
        font-size: 12px;
        padding: 3px 24px;
      }
    }
  }

  .clickable {
    color: $primary--blue;
    // font-weight: bold;
    cursor: pointer;
  }

  .mat-column-actions {
    justify-content: flex-start;
    button,
    a {
      cursor: pointer;
      display: inline-block;
      padding: 6px 12px;
      margin-right: 12px;

      white-space: nowrap;
      vertical-align: middle;

      font: unquote($proxima-font);
      font-size: 0.9em;
      font-weight: bold;
      letter-spacing: 0.5px;
      text-transform: uppercase;
      color: $primary--blue;

      // background: white;
      // color: $primary--blue;
      // border: 1px solid $primary--blue;

      // &:hover {
      //   background: $primary--blue;
      //   color: white;
      // }

      // &.green {
      //   border-color: $secondary--green-dark;
      //   color: $secondary--green-dark;

      //   &:hover {
      //     background-color: $secondary--green-dark;
      //     color: white;
      //   }
      // }

      // &.red {
      //   border-color: $secondary--red;
      //   color: $secondary--red;
      //   padding: 6px;

      //   .mat-icon {
      //     margin-right: 0;
      //   }

      //   &:hover {
      //     background-color: $secondary--red;
      //     color: white;
      //   }
      // }

      // .mat-icon {
      //   vertical-align: middle;
      //   color: inherit;
      // }

      // .mat-icon + span {
      //   margin-left: 6px;
      // }
    }
  }

  .json-string {
    display: block;
    word-wrap: break-word;
    overflow: auto;
    max-height: 6em;
    line-height: 1.5em;
    color: $primary--grey;

    /* width */
    &::-webkit-scrollbar {
      width: 8px;
    }

    /* Track */
    &::-webkit-scrollbar-track {
      background: $primary--grey-light;
    }

    /* Handle */
    &::-webkit-scrollbar-thumb {
      background: $primary--grey-medium;
    }

    /* Handle on hover */
    &::-webkit-scrollbar-thumb:hover {
      background: $primary--grey-dark;
    }
  }

  .stopgap {
    background: white;
    height: 70px;
    border-bottom: solid thin rgba(0, 0, 0, 0.12);
    display: flex;
    justify-content: center;
    align-items: center;
    color: $primary--grey;

    .mat-icon {
      font-size: 32px;
      height: 32px;
      width: 32px;
      color: rgba(0, 0, 0, 0.12);
      margin-right: 12px;
    }

    span {
      color: rgba(0, 0, 0, 0.33);
    }
  }
  //made styling changes for paginator
  ::ng-deep .mat-paginator-page-size .mat-select-value,
  ::ng-deep .mat-paginator-page-size .mat-select-value .mat-select-value-text {
    width: 50% !important;
  }
  ::ng-deep .mat-paginator-page-size .mat-select-arrow {
    margin-top: 8px;
  }
}
