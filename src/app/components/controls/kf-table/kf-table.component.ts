import { SpinnerService } from "@/services";
import { SelectionModel } from "@angular/cdk/collections";
import { DatePipe } from "@angular/common";
import {
  AfterViewInit,
  ChangeDetectorRef,
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  TemplateRef,
  ViewChild,
} from "@angular/core";
import { MatPaginator, MatTableDataSource, Sort } from "@angular/material";
import * as _ from "lodash";

@Component({
  selector: "kf-table",
  templateUrl: "./kf-table.component.html",
  styleUrls: ["./kf-table.component.scss"],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class KfTableComponent<T> implements OnInit, AfterViewInit, OnChanges {
  @Input() size: "small";
  @Input() wideContent: boolean = false;

  @Input() data: T[] = [];
  @Input() columns: KFTableColumn<T>[] = [];
  @Input() actions: KFTableAction<T>[] = [];
  @Input() noMaxHeight: boolean;
  @Input() selectable: boolean;
  @Input() hidePaginator: boolean;
  @Input() expandable: boolean;
  @Input() customPaging: any;
  @Input() rowClasses: any;
  @Input() filter: string;
  @Input() useServerSorting: boolean = false;

  @Output() selectionChanged = new EventEmitter<T[]>();
  @Output() paginatorChanged = new EventEmitter<MatPaginator>();
  @Output() actionClick = new EventEmitter<KFTableAction<T>>();
  @Output() serverSortChanged = new EventEmitter<Sort>();

  @ViewChild("date") date: TemplateRef<any>;
  @ViewChild("text") text: TemplateRef<any>;
  @ViewChild("json") json: TemplateRef<any>;
  @ViewChild("textCapitalized") textCapitalized: TemplateRef<any>;
  @ViewChild("textLowercase") textLowercase: TemplateRef<any>;
  @ViewChild("textUppercase") textUppercase: TemplateRef<any>;
  @ViewChild("array") array: TemplateRef<any>;
  @ViewChild("rowOptions") rowOptions: TemplateRef<any>;
  @ViewChild("boolean") boolean: TemplateRef<any>;

  @ViewChild(MatPaginator)
  set matPaginator(mp: MatPaginator) {
    this.paginator = mp;

    if (!this.customPaging) {
      this.dataSource.paginator = this.paginator;
    } else {
      this.updatePaginator();
    }

    this.paginatorChanged.emit(this.paginator);
  }

  loading = true;
  selection = new SelectionModel<T>(true, []);
  dataSource = new MatTableDataSource<T>();
  paginator: MatPaginator;

  @Input() selectableOption: (arg: T) => boolean = () => true;

  get displayedColumns() {
    const columns = this.columns.map((c) => c.name);
    return this.selectable ? ["select"].concat(columns) : columns;
  }

  get dataColumns() {
    return this.columns;
  }

  constructor(private datePipe: DatePipe, private cdRef: ChangeDetectorRef) {}

  ngOnInit() {}

  ngAfterViewInit(): void {
    this.dataSource.data = this.data || [];
  }

  ngOnChanges(changes: SimpleChanges): void {
    const differentData =
      changes.data &&
      !_.isEqual(changes.data.previousValue, changes.data.currentValue);
    if (differentData) {
      this.dataSource.data = this.data || [];
      if (this.paginator && this.customPaging) {
        this.updatePaginator();
      }
      this.selection.clear();
      this.selectionChanged.emit(this.selection.selected);
    }
    this.dataSource.filterPredicate = (data: T, filter: string) => {
      if (!filter) {
        return true;
      }

      const red = this.columns.find((c) => {
        if (!data[c.name] || c.excludeFromSearch) {
          return false;
        }

        const a = data[c.name].toString().toLowerCase();
        const b = filter.toLowerCase();
        return a.includes(b);
      });
      return !!red;
    };
    this.dataSource.filter = this.filter;
  }

  private updatePaginator() {
    if (this.customPaging) {
      this.paginator.length = this.customPaging.totalResultRecords || 0;
      this.paginator.pageSize = this.customPaging.pageSize || 10;
      this.paginator.pageIndex = this.customPaging.pageIndex - 1 || 0;
      this.cdRef.detectChanges(); // Ensure change detection is triggered
    }
  }

  getValue(item: T, path: string | string[]) {
    if (!path || !path.length) {
      return item.toString();
    }

    if (typeof path === "string") {
      path = [path];
    }

    return path.reduce((prev, curr) => prev[curr], item);
  }

  getJsonValue(item: T, path: string | string[]) {
    const value = this.getValue(item, path);

    return typeof value === "string" ? JSON.parse(value) : value;
  }

  getColumnStyle(column: KFTableColumn<T>) {
    if (column.width == null) {
      return "";
    }

    const styles = {
      width: column.width,
      "max-width": column.width,
      "min-width": column.width,
    };
    return styles;
  }

  getRowClass(row) {
    return !this.rowClasses
      ? ""
      : Object.keys(this.rowClasses).filter((c) =>
          typeof this.rowClasses[c] === "function"
            ? this.rowClasses[c](row)
            : this.rowClasses[c]
        );
  }

  // returns column template
  getTemplate(column: KFTableColumn<T>) {
    return column.type === "rowOptions"
      ? this[column.type]
      : column.template || this[column.type] || this.text;
  }

  pageChanged(event) {
    this.paginatorChanged.emit(this.paginator);
  }

  /** Whether the number of selected elements matches the total number of rows. */
  isAllSelected() {
    const numDisabled = this.dataSource.data.filter((row) => !this.selectableOption(row)).length;
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return (numSelected + numDisabled) === numRows;
  }

  /** Selects all rows if they are not all selected; otherwise clear selection. */
  masterToggle() {
    this.isAllSelected()
      ? this.selection.clear()
      : this.dataSource.data.forEach(
          (row) => this.selectableOption(row) && this.selection.select(row)
        );
    this.selectionChanged.emit(this.selection.selected);
  }

  /** Selects/unselect separate item */
  toggle(item: T) {
    if (!this.selectableOption(item)) {
      return;
    }

    this.selection.toggle(item);
    this.selectionChanged.emit(this.selection.selected);
  }
  
  sortData(sort: Sort) {
    if (this.useServerSorting) {
      this.serverSortChanged.emit(sort);
    } else {
      this.clientSortData(sort);
    }
  }

  clientSortData(sort: Sort) {
    const data = this.data.slice();
    if (!sort.active || sort.direction === "") {
      this.data = data;
      return;
    }

    this.data = data.sort((a, b) => {
      const isAsc = sort.direction === "asc";
      const column = sort.active;
      const compare =
        this.columns.find((c) => c.name === column).compare || this.compare;
      return compare(a[column], b[column], isAsc);
    });
    this.dataSource.data = this.data;
    this.paginator = this.customPaging || this.paginator;
  }

  compare(a, b, isAsc: boolean) {
    if (typeof a === "number" && typeof b === "number") {
      return (a - b) * (isAsc ? 1 : -1);
    }

    if (typeof a === "string" && typeof b === "string") {
      return (a.toLowerCase() < b.toLowerCase() ? -1 : 1) * (isAsc ? 1 : -1);
    }

    const stringValueA = a === null || a === undefined ? "" : a.toString();
    const stringValueB = b === null || b === undefined ? "" : b.toString();

    return (
      (stringValueA.toLowerCase() < stringValueB.toLowerCase() ? -1 : 1) *
      (isAsc ? 1 : -1)
    );
  }

  formatDate(value, format) {
    if (!value) {
      return "-";
    }

    const date = new Date(value);
  
    // If format is 'local', return detailed local time with GMT offset
    if (format === 'local') {
      const userTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      return new Intl.DateTimeFormat('en-US', {
        timeZone: userTimeZone,
        year: 'numeric',
        month: 'short',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
        timeZoneName: 'short',
      }).format(date);
    }
  
    return format
      ? this.datePipe.transform(date, format)
      : date.toLocaleString();
  }
}

export class KFTableAction<T> {
  stopPropagation?: boolean;
  click?: (t: T) => void;
  url?: (t: T) => string;
  displayCondition?: (t: T) => boolean;
  label: string;
  icon?: string;
  css?: string;
}

export class KFTableColumn<T> {
  label: string;
  name: string;
  type:
    | "array"
    | "json"
    | "text"
    | "textCapitalized"
    | "textLowercase"
    | "textUppercase"
    | "number"
    | "date"
    | "rowOptions"
    | "boolean";
  sortable?: boolean;
  excludeFromSearch?: boolean;
  compare?: (a, b, asc) => number;
  click?: (item: T) => void;
  url?: (t: T) => string;
  // path to each item (separate property names)
  path?: string[];

  // fixed column width (px)
  width?: string;

  // this template will be applied for each cell in column (if set)
  template?: TemplateRef<any>;

  // format will be applied for date cells (if set)
  dateFormat?: string;
}
