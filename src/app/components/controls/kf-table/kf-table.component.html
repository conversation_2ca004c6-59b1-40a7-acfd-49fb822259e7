<mat-table
  [class.wide-content]="wideContent"
  [class.size-small]="size === 'small'"
  [class.no-max-height]="noMaxHeight"
  [dataSource]="dataSource"
  matSort
  (matSortChange)="sortData($event)"
>
  <ng-container cdkColumnDef="select">
    <mat-header-cell *cdkHeaderCellDef class="master-select">
      <mat-checkbox
        color="primary"
        (change)="$event ? masterToggle() : null"
        [checked]="selection.hasValue() && isAllSelected()"
        [indeterminate]="selection.hasValue() && !isAllSelected()"
        [disableRipple]="true"
      >
      </mat-checkbox>
    </mat-header-cell>
    <mat-cell *cdkCellDef="let row" class="select">
      <mat-checkbox
        color="primary"
        (click)="$event.stopPropagation()"
        (change)="$event ? toggle(row) : null"
        [checked]="selection.isSelected(row)"
        [disabled]="!selectableOption(row)"
        [disableRipple]="true"
      >
      </mat-checkbox>
    </mat-cell>
  </ng-container>

  <ng-container *ngFor="let column of dataColumns" [cdkColumnDef]="column.name">
    <ng-container *ngIf="column.sortable">
      <mat-header-cell *cdkHeaderCellDef [ngClass]="column.type" [style.max-width]="column.width" [style.min-width]="column.width" mat-sort-header>
        {{ column.label }}
      </mat-header-cell>
    </ng-container>
    <ng-container *ngIf="!column.sortable">
      <mat-header-cell *cdkHeaderCellDef [ngClass]="column.type" [style.max-width]="column.width" [style.min-width]="column.width">
        {{ column.label }}
      </mat-header-cell>
    </ng-container>
    <mat-cell *cdkCellDef="let element" [ngClass]="column.type" [class.custom]="column.template" [style.max-width]="column.width" [style.min-width]="column.width">
      <div  *ngIf="!column.url"
        [class.clickable]="column.click" (click)="column.click && column.click(element)"
      >
        <ng-container
          *ngTemplateOutlet="
            getTemplate(column);
            context: { $implicit: { element: element, columnName: column.name, path: column.path, column: column } }
          "
        ></ng-container>
      </div>
      <a *ngIf="column.url"
        [class.clickable]="column.click" (click)="column.click && column.click(element);$event.preventDefault()"
        [href]="column.url(element)"
      >
        <ng-container
          *ngTemplateOutlet="
            getTemplate(column);
            context: { $implicit: { element: element, columnName: column.name, path: column.path, column: column } }
          "
        ></ng-container>
      </a>
    </mat-cell>
  </ng-container>


  <mat-header-row *cdkHeaderRowDef="displayedColumns"></mat-header-row>
  <mat-row
    *cdkRowDef="let row; columns: displayedColumns"
    (click)="$event.stopPropagation()"
    [ngClass]="getRowClass(row)"
    [cdkDetailRow]="row"
    [cdkDetailRowTpl]="expandable && detailRow"
  ></mat-row>
</mat-table>

<div *ngIf="!dataSource.data || !dataSource.data.length" class="stopgap">
  <mat-icon>desktop_access_disabled</mat-icon>
  <span>
    No results found
  </span>
</div>

<mat-paginator
  *ngIf="size !== 'small' && !hidePaginator"
  [pageSize]="10"
  [pageSizeOptions]="[5, 10, 20]"
  [showFirstLastButtons]="true"
  (page)="pageChanged($event)"
></mat-paginator>

<!-- CELL TEMPLATES -->
<ng-template #json let-cell>
  <code class="json-string">
    {{ getJsonValue(cell.element, cell.path || cell.columnName) | json }}
  </code>
</ng-template>

<ng-template #text let-cell>
  {{ getValue(cell.element, cell.path || cell.columnName) }}
</ng-template>

<ng-template #textCapitalized let-cell>
  {{ getValue(cell.element, cell.path || cell.columnName) | titlecase }}
</ng-template>

<ng-template #textUppercase let-cell>
  {{ getValue(cell.element, cell.path || cell.columnName) | uppercase }}
</ng-template>

<ng-template #textLowercase let-cell>
  {{ getValue(cell.element, cell.path || cell.columnName) | lowercase }}
</ng-template>

<ng-template #date let-cell>
  {{ formatDate(getValue(cell.element, cell.path || cell.columnName), cell.column.dateFormat) }}
</ng-template>

<ng-template #array let-cell>
  <mat-chip-list>
    <mat-chip *ngFor="let item of getValue(cell.element, cell.path || cell.columnName)">{{ getValue(item, cell.itemPath) }}</mat-chip>
  </mat-chip-list>
</ng-template>

<ng-template #boolean let-cell>
  <mat-icon *ngIf="getValue(cell.element, cell.path || cell.columnName)">check</mat-icon>
  <mat-icon *ngIf="!getValue(cell.element, cell.path || cell.columnName)">close</mat-icon>
</ng-template>

<ng-template #rowOptions let-cell>
  <!-- <ng-container *ngFor="let action of actions">
      <button
      *ngIf="(!action.displayCondition || action.displayCondition(cell.element)) && !action.url"
      [class]="action.css"
      (click)="action.click(cell.element); action.stopPropagation && $event.stopPropagation()"
    >
      <mat-icon>{{ action.icon }}</mat-icon>
      <span>{{ action.label }}</span>
    </button>
    <a mat-button
      *ngIf="(!action.displayCondition || action.displayCondition(cell.element)) && action.url"
      [class]="action.css"
      (click)="action.click(cell.element); action.stopPropagation && $event.stopPropagation();$event.preventDefault()"
      [href]="action.url(cell.element)"
    >
      <mat-icon>{{ action.icon }}</mat-icon>
      <span *ngIf="action.label">{{ action.label }}</span>
  </a>
  </ng-container> -->
  <mat-menu #optionsMenu="matMenu" class="row-options-menu">
  <ng-container *ngFor="let action of actions">
    <a
      mat-menu-item
      class="menu-item"
      *ngIf="(!action.displayCondition || action.displayCondition(cell.element)) && action.url"
      (click)="action.click(cell.element); action.stopPropagation && $event.stopPropagation()"
      [href]="action.url(cell.element)"
    >
        <mat-icon *ngIf="action.icon">{{ action.icon }}</mat-icon>
        <span>{{ action.label }}</span>
    </a>
    <a
      mat-menu-item
      class="menu-item"
      *ngIf="(!action.displayCondition || action.displayCondition(cell.element)) && !action.url"
      (click)="action.click(cell.element); action.stopPropagation && $event.stopPropagation()"
    >
        <mat-icon *ngIf="action.icon">{{ action.icon }}</mat-icon>
        <span>{{ action.label }}</span>
    </a>
  </ng-container>
  </mat-menu>

  <a class="options-button" [matMenuTriggerFor]="optionsMenu">
    <ng-container *ngIf="cell.column.template">
      <ng-container *ngTemplateOutlet="cell.column.template; context: { $implicit: cell }"></ng-container>
    </ng-container>
    <mat-icon *ngIf="!cell.column.template">more_horiz</mat-icon>
  </a>
</ng-template>

<ng-template #detailRow let-row>
  <ng-content select=".details-row"></ng-content>
</ng-template>
