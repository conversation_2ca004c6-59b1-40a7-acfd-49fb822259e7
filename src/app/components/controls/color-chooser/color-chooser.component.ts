import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { style } from '@angular/animations';

@Component({
  selector: 'app-color-chooser',
  templateUrl: './color-chooser.component.html',
  styleUrls: ['./color-chooser.component.scss'],
})
export class ColorChooserComponent implements OnInit {
  @Input() cssProperty = 'color';
  @Input() cssClass = '';
  @Input() cpPosition = 'bottom';

  @Input() color: string;
  @Output() colorChange = new EventEmitter<string>();

  constructor() {}

  ngOnInit() {
    this.style[this.cssProperty] = this.color;
  }

  get style() {
    const css = {};
    css[this.cssProperty] = this.color;
    return css;
  }

  onColorChange() {
    this.style[this.cssProperty] = this.color;
    this.colorChange.emit(this.color);
  }
}
