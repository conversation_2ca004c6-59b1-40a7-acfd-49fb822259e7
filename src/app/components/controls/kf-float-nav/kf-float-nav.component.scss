:host .kf-float-nav {
  position: fixed;
  top: 0;
  left: 0;
  min-width: 240px;
  height: 42px;
  line-height: 42px;
  background-color: #fff;
  font-size: 10px;

  z-index: 2000;
  display: none;

  &.active {
    display: block;
  }

  img {
    height: 22px;
    vertical-align: top;
    padding: 10px 12px;
    border-right: solid thin #eef3f6;
  }

  a {
    padding: 0 24px;
    color: black;
    display: inline-block;
    height: 42px;
    line-height: 42px;
    text-transform: uppercase;
    letter-spacing: 2px;
    font-weight: 600;
    position: relative;

    &.float-nav-item {
      text-transform: none;
      letter-spacing: 1px;
      color: #222222;
    }

    .mat-icon {
      vertical-align: middle;
      margin-left: 0;
    }

    &.active::after {
      content: '';
      background-color: black;
      position: absolute;
      bottom: 10px;
      left: 24px;
      height: 2px;
      width: 28px;
    }

    &:hover {
      background: #eef3f6;
      cursor: pointer;
    }
  }
}
