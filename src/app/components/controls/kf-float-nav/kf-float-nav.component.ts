import { AuthRole, Client } from '@/shared/models';
import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';

@Component({
  selector: 'kf-float-nav',
  templateUrl: './kf-float-nav.component.html',
  styleUrls: ['./kf-float-nav.component.scss'],
})
export class KfFloatNavComponent implements OnInit, OnDestroy {
  @Input() location: string;
  @Input() client: Client;
  @Input() navItems: KfFloatNavItem[];
  @Output() navItemClick = new EventEmitter<string>();

  primaryNavTabs = [
    {
      label: 'Report Management',
      allowedRoles: ['admin', 'reportManagement'] as AuthRole[],
      routerLink: 'reports',
    },
    {
      label: 'Data extract',
      allowedRoles: ['admin', 'dataScientist', 'participantExtract', 'productDelivery'] as AuthRole[],
      routerLink: 'projects',
    },
    {
      label: 'Branding',
      allowedRoles: ['admin', 'reportManagement', 'productDelivery'] as AuthRole[],
      routerLink: 'branding',
    },
    {
      label: 'Report Preview',
      allowedRoles: ['admin', 'reportManagement'] as AuthRole[],
      routerLink: 'preview',
    },
  ];
  isActive: boolean;
  isMenuOpened: boolean;

  constructor() {}

  onWindowScroll() {
    const scrolled = window.pageYOffset || document.documentElement.scrollTop;
    this.isActive = scrolled > 44;
  }

  onMenuOpened() {
    this.isMenuOpened = true;
  }

  onMenuClosed() {
    this.isMenuOpened = false;
  }

  onNavItemClick(eventName: string) {
    this.navItemClick.emit(eventName);
  }

  ngOnInit() {
    this.onWindowScroll();
    window.addEventListener('scroll', this.onWindowScroll.bind(this), true);
  }

  ngOnDestroy() {
    window.removeEventListener('scroll', this.onWindowScroll, true);
  }
}

export class KfFloatNavItem {
  label: string;
  value: string;
}
