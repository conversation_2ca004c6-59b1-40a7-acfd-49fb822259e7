<div class="kf-float-nav" [class.active]="isActive || isMenuOpened">
  <div class="kf-float-nav-content">
    <img src="assets/images/logo_cut.svg" alt="" />
    <a [routerLink]="['/clients', client.id]">
      {{ client.name }}
    </a>

    <ng-container *ngIf="!navItems">
      <a
        *ngFor="let item of primaryNavTabs"
        [(allowedRoles)]="item.allowedRoles"
        [routerLink]="['/clients', client.id, item.routerLink]"
        routerLinkActive="active"
      >
        {{ item.label }}
      </a>
    </ng-container>

    <a *ngIf="navItems" [matMenuTriggerFor]="clientMenu" (menuOpened)="onMenuOpened()" (menuClosed)="onMenuClosed()">
      <mat-icon>keyboard_arrow_down</mat-icon>
      {{ location }}
    </a>

    <a class="float-nav-item" *ngFor="let item of navItems" [routerLink]="[item.value]">
      {{ item.label }}
    </a>
  </div>
</div>

<mat-menu #clientMenu="matMenu" xPosition="after" yPosition="above">
  <a
    *ngFor="let item of primaryNavTabs"
    mat-menu-item
    [(allowedRoles)]="item.allowedRoles"
    [routerLink]="['/clients', client.id, item.routerLink]"
  >
    {{ item.label }}
  </a>
</mat-menu>
