<page-header *ngIf="showHeader"></page-header>

<div class="details-page-fit-content white-glass form">

<div class="container">
  <form [formGroup]="form" (ngSubmit)="onSubmit()">
    <div class="form-control">
      <div class="left-column">
        <label>Start date:</label>
        <input type="date" formControlName="startDate" [max]="today | date : 'yyyy-MM-dd'"/>
      </div>
      <div class="right-column">
        <label>End date:</label>
        <input type="date" formControlName="endDate" [min]="startDate | date : 'yyyy-MM-dd'" [max]="today | date : 'yyyy-MM-dd'"/>
      </div>
    </div>

    <div class="validation-group">
      <div class="note">NOTE:</div>
      <div [class]="getClassBothRequired()">Both fields are required</div>
      <div [class]="getClassPeriod()">End Date should be greater or equal to Start Date</div>
    </div>

    <div class="form-control">
      <div class="left-column">
      </div>
      <div class="right-column">
        <input type="hidden" formControlName="clientId" />
        <input type="submit" class="btn btn-primary" [disabled]="!form.valid" value="Download" />
      </div>
    </div>
  </form>
</div>
</div>
