@import "../../../../styles/theme.scss";
@import "../../../../styles/colors.scss";

:host {
  flex: 1 1 auto;

  display: flex;
  flex-direction: column;
  justify-content: stretch;

  .container {
    .form-control {
      margin: 1em 0;
      display: flex;
      justify-content: flex-start;
      .left-column {
        width: 200px;
        button {
          &:hover {
            background: $primary--blue;
            color: white;
          }
        }
      }
      .center-column {
        width: 200px;
        input[type="date"] {
          &:not(:focus) {
            &.ng-touched.ng-invalid {
              box-shadow: 0px 0px 3px 0px $secondary--red;
            }
            &.ng-untouched.ng-invalid {
              box-shadow: 0px 0px 3px 0px $primary--blue-light;
            }
          }
        }
      }
      label {
        margin: 10px 0px;
        display: block;
      }
    }
    .validation-group {
      padding: 12px 0;
      font-size: .9em;

      div {
        margin: 5px 0px;
        color: $primary--grey;

        &:not(.note) {
          margin-left: 20px;
          position: relative;

          &::before {
            position: absolute;
            left: -20px;
          }
        }

        &.error {
          color: $secondary--red;
          font-weight: 600;
          &::before {
            content: '✕';
          }
        }
        &.default {
          &::before {
            content: '•';
          }
        }
        &.success {
          color: $secondary--green;
          &::before {
            content: '✔';
          }
        }
      }
    }
  }

  input[type="submit"] {
    margin: 0;
  }
}
