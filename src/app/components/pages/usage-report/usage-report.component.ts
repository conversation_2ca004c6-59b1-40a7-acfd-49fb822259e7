import { AlertService, SpinnerService, ApiService } from "@/services";
import { ClientDetails } from "@/shared/models";
import { Component, OnInit } from "@angular/core";
import {
  FormBuilder,
  FormGroup,
  Validators,
  ValidationErrors,
} from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { Observable } from "rxjs";
import { DatePipe } from "@angular/common";
import * as FileSaver from "file-saver";
import * as _ from "lodash";

/**
 * Page to download Usage Report
 */
@Component({
  selector: "app-usage-report",
  templateUrl: "./usage-report.component.html",
  styleUrls: ["./usage-report.component.scss"],
})
export class UsageReportComponent implements OnInit {
  client: ClientDetails;
  form: FormGroup;
  today: Date = new Date();

  constructor(
    private fb: FormBuilder,
    private spinnerService: SpinnerService,
    private alertService: AlertService,
    private route: ActivatedRoute,
    private apiService: ApiService
  ) {}

  ngOnInit() {
    this.route.data.subscribe((resolved) => {
      this.client = resolved.client;
    });

    this.form = this.fb.group({
      clientId: [""],
      startDate: ["", Validators.required],
      endDate: ["", Validators.required],
    });

    if (this.client !== null) {
      this.form.get("clientId").setValue(this.client.id);
    }

    this.form.setValidators((group: FormGroup): ValidationErrors => {
      const errors = {
        startDateRequired: !this.startDate,
        endDateRequired: !this.endDate,
        shouldBePeriod: this.endDate < this.startDate,
      };
      return _.pickBy(errors);
    });
  }

  onSubmit() {
    this.spinnerService.activate();
    this.downloadReport().subscribe(
      (result) => {
        this.onFileComplete(result);
        this.spinnerService.deactivate();
      },
      (error) => {
        this.alertService.error(error.message);
        this.spinnerService.deactivate();
      }
    );
  }

  getClassBothRequired() {
    if (!(this.startDateTouched || this.startDate) || !(this.endDateTouched || this.endDate)) {
      return "default";
    }

    return this.form.errors && (this.form.errors.startDateRequired || this.form.errors.endDateRequired)
      ? "error"
      : "success";
  }

  getClassPeriod() {
    if (!this.startDate || !this.endDate) {
      return "default";
    }

    return this.form.errors && this.form.errors.shouldBePeriod
      ? "error"
      : "success";
  }

  get showHeader() {
    return this.client == null;
  }

  get startDate() {
    const value = this.form.controls.startDate.value;
    return value && new Date(`${value} 0:00:00`);
  }

  get endDate() {
    const value = this.form.controls.endDate.value;
    return value && new Date(`${value} 0:00:00`);
  }

  get startDateTouched() {
    return this.form.controls.startDate.touched;
  }

  get endDateTouched() {
    return this.form.controls.endDate.touched;
  }

  downloadReport(): Observable<any> {
    const params = {
      o_startDate: this.dateString(this.startDate),
      o_endDate: this.dateString(this.endDate),
      o_clientIds: this.client && this.client.externalRef
    };

    const url = this.apiService.buildRelativeUrlWithParams("extract/ADUUsageReport", params);
    return this.apiService.getWithFileResponse(url);
  }

  onFileComplete(data: any) {
    const name = `${this.client ? this.client.name : "all clients"} - Usage Report.xlsx`;
    const blob = new Blob([data], { type: "application/vnd.ms-excel" });
    const file = new File([blob], name, { type: "application/vnd.ms-excel" });

    this.alertService.success(`${name} has been downloaded`);
    FileSaver.saveAs(file);
  }

  dateString(date: Date) {
    const datepipe: DatePipe = new DatePipe("en-US");
    return datepipe.transform(date, "yyyy-MM-dd");
  }
}
