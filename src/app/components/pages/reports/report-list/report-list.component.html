<page-header></page-header>

<ng-container *ngIf="!outletActive">
  <div class="page-content">
    <div class="table-header" *ngIf="!loading && !outlet.isActivated">
      <input
        matInput
        placeholder="Filter"
        class="searchKey white-glass"
        [(ngModel)]="filter"
      />
      <a class="btn btn-primary glass" [routerLink]="['/reports', 'new']">New report</a>
    </div>
    <kf-table
      *ngIf="reports"
      [data]="reports"
      [filter]="filter"
      [columns]="reportsColumns"
      [actions]="actions"
    ></kf-table>
  </div>
</ng-container>

<router-outlet
  #outlet="outlet"
  (activate)="outletActive = true"
  (deactivate)="outletActive = false; refreshTable()"
></router-outlet>
