import { KFTableAction } from '@/components/controls/kf-table/kf-table.component';
import { PageHeaderService, SpinnerService } from '@/services';
import { ReportService } from '@/services/report.service';
import { Report } from '@/shared/models/reports/report';
import { Component, HostBinding, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { ReportDetailsComponent } from '../report-details/report-details.component';
import { REPORTS_COLUMNS } from './report-list.columns';

@Component({
  selector: 'app-report-list',
  templateUrl: './report-list.component.html',
  styleUrls: ['./report-list.component.scss'],
})
export class ReportListComponent implements OnInit {
  loading: boolean;
  reports: Report[] = [];
  currentSubscription: any;
  debounced: any;
  outletActive: boolean;
  filter = '';
  @HostBinding('class') get themeClass() {
    return this.loading ? 'loading' : '';
  }

  reportsColumns = REPORTS_COLUMNS;
  actions: KFTableAction<Report>[] = [
    {
      icon: 'list_alt',
      label: 'Details',
      css: '',
      displayCondition: () => true,
      click: (t: Report) => {
        this.showReportDetails(t);
      },
    },
    {
      icon: 'edit',
      label: 'Edit',
      css: '',
      displayCondition: () => true,
      click: (t: Report) => {
        this.router.navigate(['/reports', t.reportId, 'edit']);
      },
    },
  ];

  constructor(
    public dialog: MatDialog,
    private route: ActivatedRoute,
    private router: Router,
    private spinnerService: SpinnerService,
    private pageHeaderService: PageHeaderService,
    private reportService: ReportService,
  ) { }

  get activeRouteName(): string {
    return this.route.snapshot.firstChild
      ? this.route.snapshot.firstChild.routeConfig.data.title
      : this.route.snapshot.routeConfig.data.title;
  }

  ngOnInit() {
    this.refreshTable();
    this.pageHeaderService.setTitle(this.activeRouteName);
  }

  refreshTable() {
    this.spinnerService.activate();
    this.currentSubscription = this.reportService.getAllReports().subscribe(reports => {
      this.reports = reports;
      this.spinnerService.deactivate();
    });
  }

  public showReportDetails(report) {
    const dialogRef = this.dialog.open(ReportDetailsComponent, {
      width: '65%',
      data: report,
    });
  }
}
