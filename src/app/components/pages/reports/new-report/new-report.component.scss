@import "../../../../../styles/theme.scss";
@import "../../../../../styles/colors.scss";

:host {
  flex: 1 1 auto;

  display: flex;
  flex-direction: column;
  justify-content: stretch;

  .flex {
    display: flex;
    justify-content: space-between;
  }

  .validation {
    color: $secondary--red;
    font-size: 12px;
  }

  select,
  .label,
  .validation {
    padding: 8px 0;
  }

  input[type="text"],
  select {
    padding: 8px 12px;
    width: 100%;
  }

  .form-control {
    margin: 1em 0;
    display: flex;
    justify-content: flex-start;
    .label {
      width: 200px;
    }
    .input {
      width: 500px;
      input[type="text"],
      select {
        &:not(:focus) {
          &.ng-touched.ng-invalid {
            box-shadow: 0px 0px 3px 0px $secondary--red;
          }
          &.ng-untouched.ng-invalid {
            box-shadow: 0px 0px 3px 0px $primary--blue-light;
          }
        }
      }
      label {
        &:not(:focus) {
          &.ng-touched.ng-invalid {
            input {
              box-shadow: 0px 0px 3px 0px $secondary--red;
            }
          }
          &.ng-untouched.ng-invalid {
            input {
              box-shadow: 0px 0px 3px 0px $primary--blue-light;
            }
          }
        }
      }

      &.two-columns {
        column-count: 2;
      }
    }
    .validation {
      width: 300px;
    }
  }

  input,
  a {
    &.btn {
      display: inline;
      margin-right: 10px;
    }
  }
  .info-container {
    padding: 17px 10px;
    width: 300px;
    max-height: 500px;
    position: relative;
    h5 {
      padding-left: 5px;
      font-weight: bold;
      margin-bottom: 24px;
    }
    .message {
      padding: 12px;
      overflow-y: auto;
      max-height: 600px;
      color: $primary--blue-medium;
      overflow-x: hidden;
      font-size: 12px;
      border: solid thin #eeeeee;
    }
  }
  .spacer {
    //width: 300px;
  }
}
