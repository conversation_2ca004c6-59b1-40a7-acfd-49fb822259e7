<div class="details-page-content white-glass flex">
  <form [formGroup]="form" (ngSubmit)="onSubmit()">
    <input type="hidden" formControlName="reportId" [attr.disabled]="loading || null" />
    <div class="form-control">
      <div class="label">Manifest:</div>
      <div class="input">
        <input type="text" formControlName="manifestId" [attr.disabled]="loading || null" />
      </div>
    </div>
    <div class="form-control">
      <div class="label">Report key:</div>
      <div class="input">
        <input type="text" formControlName="reportKeyString" [attr.disabled]="loading || null" />
        <div *ngIf="form.touched && form.controls.reportKeyString.invalid" class="validation">
          <div *ngIf="form.controls.reportKeyString.errors.required">
            You need to specify report key
          </div>
          <div *ngIf="form.controls.reportKeyString.errors.keyInUse">
            You can't reuse existing report keys
          </div>
        </div>
      </div>
    </div>
    <div class="form-control">
      <div class="label">Is participant report:</div>
      <div class="input">
        <input type="checkbox" formControlName="isParticipantReport" [attr.disabled]="loading || null" />
      </div>
    </div>
    <div class="form-control" *ngIf="newReportOptions.languages.length">
      <div class="label">Languages:</div>
      <div class="input two-columns">
        <div *ngFor="let a of languageControls; let i = index;">
          <label formArrayName="languageIds">
            <input type="checkbox" [formControlName]="i" [value]="a" />
            {{newReportOptions.languages[i].name}}
          </label>
        </div>
        <div *ngIf="form.touched && form.controls.languageIds.invalid" class="validation">
          <div *ngIf="form.controls.languageIds.errors.required">
            You need to select at least one language
          </div>
        </div>
      </div>
    </div>
    <div class="form-control">
      <div class="label">Participant category:</div>
      <div class="input">
        <select formControlName="participantCategory" [attr.disabled]="loading || null">
          <option disabled>Select category...</option>
          <option *ngFor="let category of newReportOptions.participantCategories" [value]="category">{{ category }}</option>
        </select>
        <div *ngIf="form.controls.participantCategory.touched && form.controls.participantCategory.invalid"
          class="validation">
          Participant category required
        </div>
      </div>
    </div>
    <div class="form-control">
      <div class="label">Blended score type:</div>
      <div class="input">
        <select formControlName="blendedScoreTypes" [attr.disabled]="loading || null">
          <option disabled>Select level...</option>
          <option *ngFor="let t of newReportOptions.blendedScoreTypes" [value]="t">{{ t }}</option>
        </select>
        <div *ngIf="form.controls.blendedScoreTypes.touched && form.controls.blendedScoreTypes.invalid"
          class="validation">
          Blended score type required
        </div>
      </div>
    </div>
    <div class="form-control">
      <div class="label">Report labels:</div>
      <div class="input">
        <input type="text" formControlName="reportLabels" [attr.disabled]="loading || null" />
      </div>
    </div>
    <div class="form-control">
      <div class="label">Name reference:</div>
      <div class="input">
        <input type="text" formControlName="nameReference" [attr.disabled]="loading || null" />
      </div>
    </div>
    <div class="form-control">
      <div class="label"></div>
      <div class="input">
        <input type="submit" class="btn btn-primary" [disabled]="!form.valid" value="Submit" />
        <a class="btn" [routerLink]="['/reports']">Cancel</a>
      </div>
    </div>
  </form>
  <div class="info-container">
    <h5>Report keys in use:</h5>
    <div class="message">
      <div *ngFor="let k of newReportOptions.reportKeysInUse">{{k}}</div>
    </div>
  </div>
</div>
