import { AlertService, PageHeaderService, SpinnerService } from '@/services';
import { ReportService } from '@/services/report.service';
import { ParticipantCategory } from '@/shared/models/client-reports/enums/participant-category';
import { NewReportOptions } from '@/shared/models/reports/newReportOptions';
import { Report } from '@/shared/models/reports/report';
import { Component, HostBinding, OnInit } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-new-report',
  templateUrl: './new-report.component.html',
  styleUrls: ['./new-report.component.scss'],
})
export class NewReportComponent implements OnInit {
  @HostBinding('class') get themeClass() {
    return this.loading ? 'loading' : '';
  }

  form: FormGroup;
  newReportOptions: NewReportOptions = new NewReportOptions();
  report: Report; // current report in case if we edit existing report
  loading: boolean;
  formData: Map<string, any> = new Map<string, any>();

  private localStorageKey = 'newReportFormData';

  constructor(
    private fb: FormBuilder,
    private service: ReportService,
    private alertService: AlertService,
    private spinnerService: SpinnerService,
    private pageHeaderService: PageHeaderService,
    private route: ActivatedRoute,
    private router: Router,
  ) { }

  ngOnInit() {
    this.pageHeaderService.setTitle(this.route.snapshot.data.title);
    this.router.onSameUrlNavigation = 'reload';
    this.spinnerService.activate();
    this.form = this.fb.group({
      reportId: [0],
      manifestId: ['', [Validators.required, Validators.maxLength(255)]],
      reportKeyString: [
        '',
        [
          Validators.required,
          this.getReportKeyValidator(this),
          Validators.maxLength(255),
          Validators.pattern(/[\w-_.]+$/),
        ],
      ],
      isParticipantReport: [''],
      languageIds: new FormArray([], Validators.required),
      blendedScoreTypes: ['', Validators.required],
      reportLabels: ['', [Validators.required, Validators.maxLength(255)]],
      nameReference: ['', [Validators.required, Validators.maxLength(255)]],
      participantCategory: ['', [Validators.required, Validators.maxLength(255)]]
    });

    this.loadOptions();

    this.form.valueChanges.subscribe(v => {
      if (!this.loading && this.isNewReport) {
        sessionStorage.setItem(this.localStorageKey, JSON.stringify(this.form.value));
      }
    });  

    if (this.isNewReport) {
      this.form.reset();
    }

  }

  loadOptions() {
    this.route.data.subscribe(resolved => {
      Object.assign(this, resolved);
      const control = this.form.controls.languageIds as FormArray;
      while (control.length) {
        control.removeAt(0);
      }
      this.newReportOptions.languages.forEach(a => {
        control.push(new FormControl(false));
      });
      if (this.report) {
        const reportFormDto = this.bindToFormDto(this.report, this.newReportOptions);
        for (const key in this.form.controls) {
          if (reportFormDto.hasOwnProperty(key)) {
            if (key === 'participantCategory') {
              this.form.get(key).setValue(ParticipantCategory[reportFormDto[key]]);
            }
            else {
              this.form.get(key).setValue(reportFormDto[key]);
            }
          }
        }
      } else {
        this.loadFormValues();
      }
      this.spinnerService.deactivate();
    });
  }

  bindToFormDto(r: Report, ro: NewReportOptions): any {
    return {
      reportId: r.reportId || 0,
      reportKeyString: r.reportKey,
      reportLabels: r.reportLabels,
      nameReference: r.nameReference,
      isParticipantReport: r.isParticipantReport,
      languageIds: ro.languages.map(x => r.languages.some(l => l.id === x.id)),
      manifestId: r.manifestId,
      blendedScoreTypes: r.blendedScoreTypes || null,
      participantCategory: r.participantCategory
    };
  }

  loadFormValues() {
    const storedFormData = sessionStorage.getItem(this.localStorageKey);
    if (storedFormData) {
      const parsedData = JSON.parse(storedFormData);

      for (const control in this.form.controls) {
        if (parsedData.hasOwnProperty(control)) {
          this.form.get(control).setValue(parsedData[control]);
        }
      }
    }
  }

  get languageControls() {
    return this.form.get('languageIds')['controls'];
  }

  get isNewReport() {
    return this.report == null;
  }

  getReportKeyValidator(context: NewReportComponent) {
    return function reportKeyValidator(control: AbstractControl) {
      const value = control.value;

      if (
        value &&
        context.newReportOptions.reportKeysInUse.some(
          k => k.localeCompare(value, undefined, { sensitivity: 'base' }) === 0,
        ) &&
        (context.isNewReport
          ? true
          : context.report.reportKey.localeCompare(value, undefined, { sensitivity: 'base' }) !== 0)
      ) {
        return { keyInUse: true };
      }

      return null;
    };
  }

  getFormData() {
    this.formData = this.form.value;
    const laguageIds = this.formData['languageIds']
      .map((selected, index) => {
        return selected ? this.newReportOptions.languages[index].id : null;
      })
      .filter(id => id !== null);

    this.formData['languageIds'] = laguageIds;
    this.formData['participantCategory'] = ParticipantCategory[this.formData['participantCategory']];
    const scoreTypes = this.formData['blendedScoreTypes'];
    this.formData['blendedScoreTypes'] = Array.isArray(scoreTypes) ? scoreTypes : [scoreTypes];

    return this.formData;
  }

  onSubmit() {
    this.spinnerService.activate();
    this.service.createOrUpdateReport(this.getFormData()).subscribe(
      result => {
        this.spinnerService.deactivate();
        this.alertService.success(result);
        this.router.navigate(['reports']);
      },
      error => {
        this.spinnerService.deactivate();
        this.alertService.error(error.message);
      },
    );
  }
}
