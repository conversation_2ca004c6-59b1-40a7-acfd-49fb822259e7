import { ParticipantCategory } from '@/shared/models/client-reports/enums/participant-category';
import { Report } from '@/shared/models/reports/report';
import { Component, Inject, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';

@Component({
  selector: 'app-report-details',
  templateUrl: './report-details.component.html',
  styleUrls: ['./report-details.component.scss']
})
export class ReportDetailsComponent implements OnInit {
  @ViewChild('number') number: TemplateRef<any>;
  @ViewChild('text') text: TemplateRef<any>;
  @ViewChild('array') array: TemplateRef<any>;
  @ViewChild('stringList') stringList: TemplateRef<any>;
  @ViewChild('boolean') boolean: TemplateRef<any>;
  @ViewChild('enum') enum: TemplateRef<any>;
  loading = false;

  properties = [
    {
      name: 'reportId',
      label: 'Id',
      type: 'number'
    },
    {
      name: 'manifestId',
      label: 'Manifest Id',
      type: 'text'
    },
    {
      name: 'reportKey',
      label: 'Report Key',
      type: 'text'
    },
    {
      name: 'nameReference',
      label: 'Name Reference',
      type: 'text'
    },
    {
      name: 'isParticipantReport',
      label: 'Is Participant',
      type: 'boolean'
    },
    {
      name: 'languages',
      label: 'Languages',
      type: 'array',
      path: 'name'
    },
    {
      name: 'blendedScoreTypes',
      label: 'Blended Score Types',
      type: 'array',
    },
    {
      name: 'participantCategory',
      label: 'Participant Category',
      type: 'enum',
    },
    {
      name: 'reportLabels',
      label: 'Labels',
      type: 'stringList'
    },
  ];

  constructor(
    public dialogRef: MatDialogRef<ReportDetailsComponent>,
    @Inject(MAT_DIALOG_DATA) public report: Report
  ) {
  }

  ngOnInit() {
    // this.spinnerService.activate();
    /*this.route.data.subscribe(resolved => {
      Object.assign(this, resolved);
      this.spinnerService.deactivate();
    });*/
  }

  getTemplate(id: string) {
    return this[id] || this.text;
  }

  getValue(item, path: string) {
    if (!path) {
      return item.toString();
    }

    if ((typeof item) === 'string') {
      return (item as String).split(',');
    }

    const props = path.split('.');
    return props.reduce((prev, curr) => prev[curr], item);
  }

  getEnum(value: number) {
    return ParticipantCategory[value];
  }
}
