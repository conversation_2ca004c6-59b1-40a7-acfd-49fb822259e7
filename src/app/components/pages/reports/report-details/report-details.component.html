<ng-template #number let-property>
  <tr>
    <th>{{property.label}}</th>
    <td>{{report[property.propertyName]}}</td>
  </tr>
</ng-template>
<ng-template #text let-property>
  <tr>
    <th>{{property.label}}</th>
    <td>{{report[property.propertyName]}}</td>
  </tr>
</ng-template>
<ng-template #array let-property>
  <tr>
    <th>{{property.label}}</th>
    <td>
      <mat-chip-list>
        <mat-chip *ngFor="let item of report[property.propertyName]">{{getValue(item, property.path)}}</mat-chip>
      </mat-chip-list>
    </td>
  </tr>
</ng-template>
<ng-template #stringList let-property>
  <tr>
    <th>{{property.label}}</th>
    <td>
      <mat-chip-list>
        <mat-chip *ngFor="let item of report[property.propertyName].split(',')">{{item}}</mat-chip>
      </mat-chip-list>
    </td>
  </tr>
</ng-template>

<ng-template #boolean let-property>
  <tr>
    <th>{{property.label}}</th>
    <td>
      {{ report[property.propertyName] ? 'Yes' : 'No' }}
    </td>
  </tr>
</ng-template>

<ng-template #enum let-property>
  <tr>
    <th>{{property.label}}</th>
    <td>
      {{getEnum(report[property.propertyName])}}     
    </td>
  </tr>
</ng-template>

<h2 mat-dialog-title>Report details</h2>
<table>
  <tbody>
    <ng-container *ngFor="let property of properties">
      <ng-container
        *ngTemplateOutlet="getTemplate(property.type); context:{$implicit: {element: element, label: property.label, propertyName: property.name, path: property.path}}">
      </ng-container>
    </ng-container>
  </tbody>
</table>
<mat-dialog-actions>
  <button mat-button [routerLink]="['reports',report.reportId, 'edit']" mat-dialog-close class="btn-primary">
    Edit <mat-icon>edit</mat-icon>
  </button>
  <button mat-button mat-dialog-close>Close</button>
</mat-dialog-actions>
