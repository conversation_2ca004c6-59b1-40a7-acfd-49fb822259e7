import { AppMaterialModule } from '@/app.material.module';
import { SharedModule } from '@/shared/shared.module';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BrowserModule } from '@angular/platform-browser';
import { RouterModule } from '@angular/router';

import { DragDropModule } from '@angular/cdk/drag-drop';
import {
  MatCheckboxModule,
  MatDialogModule,
  MatSlideToggleModule,
  MAT_DIALOG_DEFAULT_OPTIONS,
} from '@angular/material';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ColorPickerModule } from 'ngx-color-picker';
import {
  BrandingAssetsComponent,
  CandidateDetailsComponent,
  CandidateLogComponent,
  CandidateManageAssessmentsComponent,
  CandidateReportStatusComponent,
  ClientCandidatesComponent,
  ClientDetailsComponent,
  ClientProjectsComponent,
  ClientSearchComponent,
  DataExtractsUploadComponent,
  LoginComponent,
  NewReportComponent,
  PortalBrandingComponent,
  ProjectCandidatesComponent,
  ProjectDetailsComponent,
  ReportBrandingComponent,
  ReportDetailsComponent,
  ReportListComponent,
  ReportManagementComponent,
  ReportPreviewDownloadComponent,
  UsersComponent,
  ClientNormsComponent,
  NormDetailsComponent,
  UsageReportComponent,
  ClientCustomisationsComponent,
  ClientCustomProjectTypesComponent,
  SystemLogComponent,
} from '.';
import { ControlsModule } from '../controls/controls.module';
import { DialogsModule } from '../dialogs/dialogs.module';
import { ProjectEmailSchedulesComponent } from './clients/project-email-schedules/project-email-schedules.component';
import { CustomProjectTypeDetailsComponent } from './clients/custom-project-type-details/custom-project-type-details.component';
import { ClientTilesComponent } from './clients/client-tiles/client-tiles.component';
import { SpsCollaborationComponent } from './clients/sps-collaboration/sps-collaboration.component';
import { SpsCollaborationDetailsComponent } from './clients/sps-collaboration-details/sps-collaboration-details.component';
import { SpsStakeholderLogComponent } from './clients/sps-stakeholder-log/sps-stakeholder-log.component';
import { SpsCollaborationStakeholdersComponent } from './clients/sps-collaboration-stakeholders/sps-collaboration-stakeholders.component';
import { SpsStakeholderDetailsComponent } from './clients/sps-stakeholder-details/sps-stakeholder-details.component';



@NgModule({
  imports: [
    AppMaterialModule,
    BrowserAnimationsModule,
    BrowserModule,
    CommonModule,
    DragDropModule,
    FormsModule,
    MatCheckboxModule,
    MatDialogModule,
    MatSlideToggleModule,
    ReactiveFormsModule,
    RouterModule,
    SharedModule,
    AppMaterialModule,
    ColorPickerModule,
    ControlsModule,
    DialogsModule,
  ],
  declarations: [
    BrandingAssetsComponent,
    CandidateDetailsComponent,
    CandidateLogComponent,
    CandidateManageAssessmentsComponent,
    CandidateReportStatusComponent,
    ClientCandidatesComponent,
    ClientDetailsComponent,
    ClientProjectsComponent,
    ClientSearchComponent,
    DataExtractsUploadComponent,
    LoginComponent,
    NewReportComponent,
    PortalBrandingComponent,
    ProjectCandidatesComponent,
    ProjectEmailSchedulesComponent,
    ProjectDetailsComponent,
    ReportBrandingComponent,
    ReportDetailsComponent,
    ReportListComponent,
    ReportManagementComponent,
    ReportPreviewDownloadComponent,
    UsersComponent,
    ClientNormsComponent,
    NormDetailsComponent,
    UsageReportComponent,
    ClientCustomisationsComponent,
    ClientCustomProjectTypesComponent,
    CustomProjectTypeDetailsComponent,
    ClientTilesComponent,
    SpsCollaborationComponent,
    SpsCollaborationDetailsComponent,
    SpsStakeholderLogComponent,
    SpsCollaborationStakeholdersComponent,
    SpsStakeholderDetailsComponent,
    SystemLogComponent
  ],
  exports: [
    BrandingAssetsComponent,
    CandidateDetailsComponent,
    CandidateLogComponent,
    CandidateManageAssessmentsComponent,
    CandidateReportStatusComponent,
    ClientCandidatesComponent,
    ClientProjectsComponent,
    ClientSearchComponent,
    DataExtractsUploadComponent,
    LoginComponent,
    NewReportComponent,
    PortalBrandingComponent,
    ProjectCandidatesComponent,
    ProjectEmailSchedulesComponent,
    ProjectDetailsComponent,
    ReportBrandingComponent,
    ReportDetailsComponent,
    ReportListComponent,
    ReportManagementComponent,
    ReportPreviewDownloadComponent,
    UsersComponent,
    ClientNormsComponent,
    NormDetailsComponent,
    UsageReportComponent,
    ClientTilesComponent,
    SpsCollaborationComponent,
    SpsCollaborationDetailsComponent,
    SpsStakeholderLogComponent,
    SpsCollaborationStakeholdersComponent,
    SystemLogComponent
  ],
  entryComponents: [ReportDetailsComponent],
  providers: [
    { provide: MAT_DIALOG_DEFAULT_OPTIONS, useValue: { hasBackdrop: true } },
  ],
})
export class PagesModule {
  constructor() {}
}
