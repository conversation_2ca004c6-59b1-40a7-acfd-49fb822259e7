﻿<div class="login-content">
  <div class="login-section">
    <div class="login-box">
      <div class="login-header">
        <img src="/assets/images/logo.svg" alt="KornFerry">
        <hr />
      </div>
      <div class="login-form">
        <ng-container [ngSwitch]="template">
          <ng-container *ngSwitchCase="'login'" [ngTemplateOutlet]="login"></ng-container>
          <ng-container *ngSwitchCase="'register'" [ngTemplateOutlet]="register"></ng-container>
          <ng-container *ngSwitchCase="'forgotPassword'" [ngTemplateOutlet]="forgotPassword"></ng-container>
          <ng-container *ngSwitchCase="'updatePassword'" [ngTemplateOutlet]="updatePassword"></ng-container>
        </ng-container>
      </div>
    </div>
  </div>
</div>

<!-- Sign-in form -->
<ng-template #login>
  <form [formGroup]="loginForm" (ngSubmit)="signInSubmit(loginForm)">
    <h2 class="welcome">Welcome</h2>
    <h4>Please Sign-In</h4>

    <div class="form-field">
      <label>Username</label>
      <input id="username" type="text" matInput formControlName="username" placeholder="Username" />
      <div class="mat-errors">
        <mat-error *ngIf="loginForm.controls.username.errors?.required">Username is required</mat-error>
      </div>
    </div>

    <div class="form-field">
      <label>Password</label>
      <input id="password" type="password" matInput formControlName="password" placeholder="Password" />
      <div class="mat-errors">
        <mat-error *ngIf="loginForm.controls.password.errors?.required">Password is required</mat-error>
      </div>
    </div>

    <div class="form-field">
      <button [disabled]="loginForm.invalid || loading" class="btn btn-primary">Sign In</button>
    </div>

    <div class="form-field actions">
      <a (click)="changeForm('register')" class="float-right">Register</a>
      <a (click)="changeForm('forgotPassword')" class="">Forgot Password?</a>
    </div>
  </form>
</ng-template>



<!-- Sign-up form -->
<ng-template #register>
  <form [formGroup]="registerForm" (ngSubmit)="registerSubmit(registerForm)">
    <h2 class="welcome">Register</h2>

    <div class="form-field">
      <label>First Name</label>
      <input id="firstName" type="text" matInput formControlName="firstName" placeholder="First Name" />
      <div class="mat-errors">
        <mat-error *ngIf="registerForm.controls.firstName.errors?.required">First Name is required</mat-error>
      </div>
    </div>

    <div class="form-field">
      <label>Last Name</label>
      <input id="lastName" type="text" matInput formControlName="lastName" placeholder="Last Name" />
      <div class="mat-errors">
        <mat-error *ngIf="registerForm.controls.lastName.errors?.required">Last Name is required</mat-error>
      </div>
    </div>

    <div class="form-field">
      <label>Username</label>
      <input id="username" type="text" matInput formControlName="username" placeholder="Username" />
      <div class="mat-errors">
        <mat-error *ngIf="registerForm.controls.username.errors?.required">Username is required</mat-error>
      </div>
    </div>

    <div class="form-field">
      <label>Email</label>
      <input id="email" type="email" matInput formControlName="email" placeholder="Email" />
      <div class="mat-errors">
        <mat-error *ngIf="registerForm.controls.email.errors?.required">Email is required</mat-error>
      </div>
    </div>

    <div class="form-field">
      <label>Password</label>
      <input id="password" type="password" matInput formControlName="password" placeholder="Password" />
      <div class="mat-errors">
        <mat-error *ngIf="registerForm.controls.password.errors?.required">Password is required</mat-error>
        <mat-error *ngIf="registerForm.controls.password.errors?.minlength">Password must be at least 8 characters</mat-error>
        <mat-error *ngIf="
          registerForm.controls.password.errors?.hasNumber || registerForm.controls.password.errors?.hasUppercase
          || registerForm.controls.password.errors?.hasLowercase || registerForm.controls.password.errors?.hasSpecial
        ">
          Password must contain letters in both cases, digits and special symbols
        </mat-error>
      </div>
    </div>

    <div class="form-field" *ngIf="googleCaptchaEnabled">
      <div class="recaptcha">
        <recaptcha formControlName="captcha"></recaptcha>
      </div>
    </div>

    <div class="form-field">
      <button [disabled]="registerForm.invalid || loading" class="btn btn-primary">Register</button>
    </div>

    <div class="form-field">
      <a (click)="changeForm('login')">Return to Sign-in</a>
    </div>
  </form>
</ng-template>



<!-- Reset password -->
<ng-template #forgotPassword>
  <form [formGroup]="forgotPasswordForm" (ngSubmit)="forgotPasswordSubmit(forgotPasswordForm)">
    <h2 class="welcome">Reset password</h2>
    <p>If you have forgotten your username, please contact your administrator.</p>

    <div class="form-field">
      <label>Username</label>
      <input id="username" type="text" matInput formControlName="username" placeholder="Username" />
      <div class="mat-errors">
        <mat-error *ngIf="forgotPasswordForm.controls.username.errors?.required">Username is required</mat-error>
      </div>
    </div>

    <div class="form-field">
      <button [disabled]="forgotPasswordForm.invalid || loading" class="btn btn-primary">Send code</button>
    </div>

    <div class="form-field">
      <a (click)="changeForm('login')">Return to Sign-in</a>
      <a (click)="changeForm('updatePassword')" class="float-right">Have reset code</a>
    </div>
  </form>
</ng-template>



<!-- Update Password -->
<ng-template #updatePassword>
  <form [formGroup]="updatePasswordForm" (ngSubmit)="updatePasswordSubmit(updatePasswordForm)">
    <h2 class="welcome">Reset password</h2>
    <p>Confirm your username and choose a new password</p>

    <div class="form-field">
      <label>Username</label>
      <input id="username" type="text" matInput formControlName="username" placeholder="Username" />
      <div class="mat-errors">
        <mat-error *ngIf="updatePasswordForm.controls.username.errors?.required">Username is required </mat-error>
      </div>
    </div>

    <div class="form-field">
      <label>New Password</label>
      <input id="password" type="password" matInput formControlName="password" placeholder="Password" />
      <div class="mat-errors">
        <mat-error *ngIf="updatePasswordForm.controls.password.errors?.required">Password is required </mat-error>
      </div>
    </div>

    <div class="form-field">
      <label>Reset code</label>
      <input id="confirmationCode" type="text" matInput formControlName="confirmationCode" placeholder="Reset code" />
      <div class="mat-errors">
        <mat-error *ngIf="updatePasswordForm.controls.confirmationCode.errors?.required">Reset code is required </mat-error>
      </div>
    </div>

    <div class="form-field">
      <button [disabled]="updatePasswordForm.invalid || loading" class="btn btn-primary">Change password</button>
    </div>

    <div class="form-field">
      <a (click)="changeForm('login')">Return to Sign-in</a>
    </div>
  </form>
</ng-template>
