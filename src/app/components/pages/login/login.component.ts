﻿import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';

import { AlertService, AuthenticationService, SpinnerService, UserService } from '@/services';
import { UpdatePasswordRequest } from '@/shared/models';
import { CustomValidators } from '@/shared/validators/customValidators';
import { first } from 'rxjs/operators';
import { environment } from "@/shared/environments/environment";

@Component({
    templateUrl: 'login.component.html',
    styleUrls: ['login.component.scss']
})
export class LoginComponent implements OnInit {
    returnUrl: string;
    loading: boolean;

    template: ('login' | 'register' | 'forgotPassword' | 'updatePassword') = 'login';

    forgotPasswordForm: FormGroup;
    updatePasswordForm: FormGroup;
    registerForm: FormGroup;
    loginForm: FormGroup;
    googleCaptchaEnabled: boolean;
      
    constructor(
        private formBuilder: FormBuilder,
        private route: ActivatedRoute,
        private router: Router,
        private spinnerService: SpinnerService,
        private authenticationService: AuthenticationService,
        private userService: UserService,
        private alertService: AlertService
    ) {
        // redirect to home if already logged in
        if (this.authenticationService.currentUser) {
            this.router.navigate(['/']);
        }
    }

    ngOnInit() {
        this.loginForm = this.formBuilder.group({
            username: ['', Validators.required],
            password: ['', Validators.required],
            captcha: ['']
        });

        this.registerForm = this.formBuilder.group({
            firstName: ['', Validators.required],
            lastName: ['', Validators.required],
            email: ['', Validators.required],
            username: ['', Validators.required],
            password: ['', [
                Validators.required,
                CustomValidators.hasNumber,
                CustomValidators.hasUppercase,
                CustomValidators.hasLowercase,
                CustomValidators.hasSpecial,
                Validators.minLength(8)
            ]],
            captcha: ['']
        });

        this.forgotPasswordForm = this.formBuilder.group({
            username: ['', Validators.required]
        });

        this.updatePasswordForm = this.formBuilder.group({
            username: ['', Validators.required],
            password: ['', Validators.required],
            confirmationCode: ['', Validators.required]
        });

        // get return url from route parameters or default to '/'
        this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/';
        if (this.returnUrl === "/clients") this.returnUrl = "/client-tiles";

        // set default value of GOOGLE_CAPTCHA_ENABLED to true if it is undefined or null
        this.googleCaptchaEnabled= this.isGoogleCaptchaEnabled()
    }

    isGoogleCaptchaEnabled() {
        if (environment.GOOGLE_CAPTCHA_ENABLED === null ||
          environment.GOOGLE_CAPTCHA_ENABLED === undefined) {
          return true;
        }
      
        const captchaToBoolean = environment.GOOGLE_CAPTCHA_ENABLED.toString() === 'false' ? false : true;
        return captchaToBoolean;     
      }

    // convenience getter for easy access to form fields
    changeForm(name) {
        this.template = name;
    }

    buildMessage(error) {
        return `An error occurs when you trying to sign in: <p>${JSON.stringify(error, null, 2)}</p>`;
    }

    signInSubmit(form) {
        // stop here if form is invalid
        if (form.invalid) {
            return;
        }
        this.spinnerService.activate();
        this.authenticationService.login(form.controls.username.value, form.controls.password.value)
            .subscribe(
                data => {
                    this.spinnerService.deactivate();
                    this.router.navigateByUrl(this.returnUrl);
                },
                error => {
                    this.spinnerService.deactivate();
                    this.alertService.error(error.message || this.buildMessage(error));
                });
    }

    registerSubmit(form) {
        // stop here if form is invalid
        if (form.invalid) {
            return;
        }

        this.spinnerService.activate();
        this.userService.register(form.value)
            .pipe(first())
            .subscribe(
                data => {
                    this.alertService.success('Registration successful', true);
                    setTimeout(() => {
                        this.template = 'login';
                        this.spinnerService.deactivate();
                    }, 1000);
                },
                error => {
                    this.alertService.error(error.message || this.buildMessage(error));
                    this.spinnerService.deactivate();
                });
    }

    forgotPasswordSubmit(form) {
        // stop here if form is invalid
        if (form.invalid) {
            return;
        }

        this.spinnerService.activate();
        this.userService.resetPassword(form.controls.username.value).subscribe(
            data => {
                this.alertService.success('reset code has been emailed');
                this.spinnerService.deactivate();
                this.router.navigate(['updatePassword']);
            },
            error => {
                this.alertService.error(error.message || this.buildMessage(error));
                this.spinnerService.deactivate();
            }
        );
    }

    updatePasswordSubmit(form) {
        // stop here if form is invalid
        if (form.invalid) {
            return;
        }

        this.spinnerService.activate();
        this.userService.updatePassword({
            userName: form.controls.username.value,
            password: form.controls.password.value,
            confirmationCode: form.controls.confirmationCode.value
        } as UpdatePasswordRequest)
            .subscribe(
                data => {
                    this.alertService.success('update request successful');
                    this.spinnerService.deactivate();
                    this.router.navigate(['login']);
                },
                error => {
                    this.alertService.error(error.message || this.buildMessage(error));
                    this.spinnerService.deactivate();
                });
    }
}
