import { KFTableColumn } from '@/components/controls';
import {
  AlertService,
  AuthenticationService,
  PageHeaderService,
  SpinnerService,
  UserService,
} from '@/services';
import { RegisteredUser } from '@/shared/models';
import { AuthRoles as displayOrder, getAliasForAuthRole, getDisplayOrderForAuthRole } from "@/shared/models/authRoles";
import {
  Component,
  OnDestroy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';

/**
 * Component to list all the registered users in site.
 */
@Component({
  selector: 'app-users',
  templateUrl: './users.component.html',
  styleUrls: ['./users.component.scss'],
})
export class UsersComponent implements OnInit, OnDestroy {
  currentSubscription: Subscription;
  users: RegisteredUser[] = [];
  columns: KFTableColumn<RegisteredUser>[];
  groups: string[];
  selectedUserGroups: any[];
  searchKey: string;
  paginationToken: any;
  debouncedTimer: any;
  altLoading: boolean;
  isSuperAdmin = false;

  @ViewChild('GROUPS_TPL') GROUPS_TPL: TemplateRef<any>;

  setFilter(value: string) {
    this.searchKey = value;
    this.paginationToken = null;
    this.debounced(() => {
      this.loadUsers();
    });
  }

  constructor(
    private userService: UserService,
    private spinnerService: SpinnerService,
    private alertService: AlertService,
    private authService: AuthenticationService,
    private pageHeaderService: PageHeaderService,
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
    this.columns = this.userColumns;
    this.loadUsers();
    this.isSuperAdmin = this.authService.hasRole('admin');
    this.userService.getAllGroups().subscribe((groups) => {
        this.groups = groups.sort((a, b) => getDisplayOrderForAuthRole(getAliasForAuthRole(a)) - getDisplayOrderForAuthRole(getAliasForAuthRole(b)));
    });
    this.pageHeaderService.setTitle(this.route.snapshot.data.title);
  }

  ngOnDestroy() {
    if (this.currentSubscription) {
      this.currentSubscription.unsubscribe();
    }
  }

  debounced(action) {
    clearTimeout(this.debouncedTimer);
    this.debouncedTimer = setTimeout(action, 350);
  }

  loadUsers() {
    this.spinnerService.activate();
    this.userService.getAll(this.paginationToken, this.searchKey).subscribe(
      (usersResponse) => {
        this.users = this.paginationToken
          ? this.users.concat(...usersResponse.users)
          : usersResponse.users;
        this.paginationToken = usersResponse.paginationToken;
        this.spinnerService.deactivate();
      },
      () => {
        this.alertService.error(
          'Please try to change filters and then try to load again'
        );
        this.spinnerService.deactivate();
      }
    );
  }

  loadUserGorups(user: RegisteredUser) {
    this.altLoading = true;
    this.selectedUserGroups = [];

    this.userService.getUserGroups(user.userName).subscribe(
      (res) => {
        this.selectedUserGroups = res;
        this.altLoading = false;
      },
      () => {
        this.altLoading = false;
      }
    );
  }

  toggleGroupForUser(group: string, user: RegisteredUser) {
    if (!this.isSuperAdmin && group === 'superAdmin') {
      return;
    }

    this.spinnerService.activate();
    const isRemoveAction = this.isUserInGroup(group);
    const actionFactory = isRemoveAction
      ? this.userService.removeUserFromGroup
      : this.userService.addUserToGroup;

    actionFactory.call(this.userService, user.userName, group).subscribe(
      () => {
        if (isRemoveAction) {
          this.selectedUserGroups.splice(
            this.selectedUserGroups.indexOf(group),
            1
          );
        } else {
          this.selectedUserGroups.push(group);
        }
        this.spinnerService.deactivate();
      },
      () => {
        this.spinnerService.deactivate();
      }
    );
  }

  isUserInGroup(group: string) {
    return !!(
      this.selectedUserGroups && this.selectedUserGroups.indexOf(group) + 1
    );
  }

  confirmUser(row: RegisteredUser) {
    this.spinnerService.activate();
    this.userService.confirmUser(row.userName).subscribe(
      () => {
        row.status = 'CONFIRMED';
        this.spinnerService.deactivate();
      },
      () => {
        this.spinnerService.deactivate();
      }
    );
  }

  get userColumns(): KFTableColumn<RegisteredUser>[] {
    return [
      {
        type: 'text',
        name: 'userName',
        label: 'Username',
      },
      {
        type: 'text',
        name: 'firstName',
        label: 'First Name',
      },
      {
        type: 'text',
        name: 'lastName',
        label: 'Last Name',
      },
      {
        type: 'text',
        name: 'email',
        label: 'Email',
      },
      {
        type: 'text',
        name: 'status',
        label: 'status',
        width: '200px',
      },
      {
        type: 'text',
        name: 'groups',
        label: 'groups',
        width: '200px',
        template: this.GROUPS_TPL,
      },
    ];
  }
}
