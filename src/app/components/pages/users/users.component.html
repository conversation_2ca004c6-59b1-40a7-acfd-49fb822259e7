<page-header></page-header>

<div class="page-content">
  <div class="filter">
    <mat-icon>search</mat-icon>
    <input
      mat-input
      placeholder="Search by username starts with (case-sensitive)"
      [(ngModel)]="searchKey"
      (ngModelChange)="setFilter($event)"
    />
  </div>

  <div class="table-container">
    <kf-table
      [data]="users"
      [columns]="columns"
      [hidePaginator]="true"
    ></kf-table>
  </div>
</div>

<div class="load-more-section" *ngIf="paginationToken">
  <button class="btn btn-secondary glass" (click)="loadUsers()">Load more</button>
</div>

<ng-template #GROUPS_TPL let-cell>
  <button
    *ngIf="cell.element.status === 'UNCONFIRMED'"
    class="green"
    (click)="confirmUser(cell.element)"
  >
    <mat-icon>add</mat-icon>
    <span>Confirm User</span>
  </button>

  <div *ngIf="cell.element.status === 'CONFIRMED'">
    <mat-menu #groupsMenu="matMenu">
      <div *ngIf="!altLoading">
        <button
          mat-menu-item
          class="mat-menu-group-item"
          [disabled]="altLoading || (!isSuperAdmin && group === 'superAdmin')"
          *ngFor="let group of groups"
          (click)="
            toggleGroupForUser(group, cell.element); $event.stopPropagation()
          "
        >
          <mat-icon
            [class.disabled-icon]="!isSuperAdmin && group === 'superAdmin'"
          >
            {{
              isUserInGroup(group, cell.element)
                ? "check_box"
                : "check_box_outline_blank"
            }}
          </mat-icon>
          <span class="group-name">
            {{ group | usergroup }}
          </span>
        </button>
      </div>

      <h5 mat-menu-item *ngIf="altLoading">loading...</h5>
    </mat-menu>

    <button
      [matMenuTriggerFor]="groupsMenu"
      (onMenuOpen)="loadUserGorups(cell.element)"
    >
      <mat-icon>people</mat-icon>
      <span>Edit groups</span>
    </button>
  </div>
</ng-template>
