@import "../../../../styles/colors.scss";
@import "../../../../styles/theme.scss";
@import "../../../../styles/typography.scss";
:host {
  flex: 1 1 auto;
  overflow: auto;
  max-height: calc(100% - 100px);

  .table-container {
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
  }

  .filter {
    position: relative;
    margin-bottom: 16px;
    @extend .glass;

    mat-icon {
      position: absolute;
      top: 0;
      left: 0;
      padding: 12px;
      color: $primary--blue;
    }
    input {
      display: block;
      width: 100%;
      padding-left: 50px;
    }
  }

  .mat-column-groups {
    mat-select {
      padding: 9px 6px 9px 15px;
      box-sizing: border-box;
      background: white;
      border: solid thin rgba(black, 0.55);
      min-width: 155px;
      max-width: 155px;
      font-family: ProximaNova;
      font-size: small;
    }
  }

  button {
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    margin-right: 12px;
    min-width: 157px;

    white-space: nowrap;
    vertical-align: middle;

    font: unquote($proxima-font);
    font-size: 0.9em;
    font-weight: bold;
    letter-spacing: 0.5px;
    text-transform: uppercase;

    background: white;
    color: $primary--blue;
    border: 1px solid $primary--blue;

    &:hover {
      background: $primary--blue;
      color: white;
    }

    &.green {
      border-color: $secondary--green-dark;
      color: $secondary--green-dark;

      &:hover {
        background-color: $secondary--green-dark;
        color: white;
      }
    }

    &.red {
      border-color: $secondary--red;
      color: $secondary--red;
      padding: 6px;

      .mat-icon {
        margin-right: 0;
      }

      &:hover {
        background-color: $secondary--red;
        color: white;
      }
    }

    .mat-icon {
      margin-right: 12px;
      vertical-align: middle;
      color: inherit;
    }

    span {
      flex: 1 1 auto;
      text-align: center;
    }
  }
}

.group-name {
  font-size: 13px;
}

.load-more-section {
  margin-bottom: 48px;
  text-align: center;

  button {
    padding: 12px 24px;
  }
}

@at-root .mat-menu-group-item {
  box-sizing: content-box;
  height: 36px;
  line-height: 36px;
  padding: 3px 12px 3px 6px;
  width: 200px;

  .mat-icon {
    margin-right: 6px;
  }
}

@at-root .mat-option {
  font-family: ProximaNova;
}

@at-root .disabled-icon {
  opacity: 0.4;
}
