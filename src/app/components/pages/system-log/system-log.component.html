<page-header></page-header>
<div class="page-content glass">
  <div class="current-view">
    <a class="active">Logs</a>
  </div>
  <div class="table">
    <div class="table-filter-panel">
      <span>Filter from:</span>
      <ng-container>
        <input
          [class.grey]="!context.startDateString"
          type="date"
          [(ngModel)]="context.startDateString"
          [max]="context.endDateString || defaultEndDate"
        />
        <span> to: </span>
        <input
          [class.grey]="!context.endDateString"
          type="date"
          [(ngModel)]="context.endDateString"
          [max]="defaultEndDate"
        />

        <kf-select
          placeholder="Levels"
          [selection]="context.selectedLevel"
          [multiple]="false"
          (selectionChange)="context.selectedLevel = $event"
          [options]="context.levelOptions"
        ></kf-select>

        <kf-select
          placeholder="Log Type"
          [selection]="context.selectedType"
          [multiple]="false"
          (selectionChange)="context.selectedType = $event"
          [options]="context.typeOptions"
        ></kf-select>

      <div class="input-wrapper">
        <input
          type="text"
          placeholder="Logger"
          [(ngModel)]="context.loggerFilter"
        />
        <span
          class="clear-icon"
          *ngIf="context.loggerFilter"
          (click)="context.loggerFilter = ''"
          >×</span>
      </div>

      <div class="input-wrapper">
        <input
          type="text"
          placeholder="Message"
          [(ngModel)]="context.messageFilter"
        />
        <span
          class="clear-icon"
          *ngIf="context.messageFilter"
          (click)="context.messageFilter = ''"
          >×</span>
      </div>

      <div class="input-wrapper">
        <input
          type="text"
          placeholder="Application"
          [(ngModel)]="context.applicationFilter"
        />
        <span
          class="clear-icon"
          *ngIf="context.applicationFilter"
          (click)="context.applicationFilter = ''"
          >×</span>
      </div>

        <button class="btn btn-primary" (click)="onFilterChange()" [disabled]="!hasMainFilters()">FILTER</button>
        <button class="btn btn-secondary"  (click)="clearFilters()" [disabled]="!hasFilters()">CLEAR</button>
      </ng-container>
    </div>

    <kf-table
      *ngIf="context.filteredRecords"
      [wideContent]="true"
      [data]="context.filteredRecords"
      [columns]="context.columns"
      [customPaging]="context.customPaging"
      (paginatorChanged)="onPageChange($event)"
      (serverSortChanged)="onSortChange($event)"
      [useServerSorting]="true"
    ></kf-table>
  </div>
</div>

<ng-template #LEVEL_TPL let-cell>
  <span class="level {{ cell.element.level | lowercase }}">
    {{ cell.element.level }}
  </span>
</ng-template>
