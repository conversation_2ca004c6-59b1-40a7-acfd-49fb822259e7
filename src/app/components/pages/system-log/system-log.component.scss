@import "../../../../styles/colors.scss";
@import "../../../../styles/theme.scss";
@import "../../../../styles/typography.scss";

:host {
  flex: 1;

  .current-view {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 12px 24px;

    font-size: 12px;
    font-weight: bold;
    letter-spacing: 1px;
    text-transform: uppercase;

    span {
      color: rgba(0, 0, 0, 0.33);
      padding: 6px 0;
    }

    a {
      padding: 6px 0;
      margin-left: 48px;
      position: relative;

      &.active {
        &::after {
          content: "";
          width: 22px;
          height: 2px;
          position: absolute;
          background: $primary--blue;
          bottom: 3px;
          left: 0;
        }
      }
    }
  }

  .table {
    box-shadow: 0 0 1px rgba(0, 0, 0, 0.3);
    width: 100%;

    .table-filter-panel {
      display: flex;
      flex-wrap: wrap;
      ;
      align-items: center;
      justify-content: flex-start;
      background: white;
      border-bottom: solid thin rgba(0, 0, 0, 0.1);
      padding: 12px 24px;

      kf-table {
        width: 100%;
        overflow-x: auto;
        padding: 0 6px;
      }

      .input-wrapper {
        position: relative;
        display: inline-block;

        input {
          padding-right: 20px;
        }

        .clear-icon {
          position: absolute;
          top: 50%;
          right: 6px;
          transform: translateY(-50%);
          font-size: 14px;
          color: #999;
          cursor: pointer;
          user-select: none;
        }

        .clear-icon:hover {
          color: #333;
        }
      }

      input[type="date"] {
        padding: 10px;
      }

      input[type="text"] {
        width: 120px;
      }

      ::ng-deep .kf-select .mat-select {
        max-width: 150px;
      }


      button {
        background: #007bc7;
        color: white;
        border-width: 0;
        min-width: 60px;
      }

      button:disabled {
        cursor: auto;
        background-color: rgba(145, 145, 145, 0.05);
        border-color: rgba(0, 0, 0, 0.1);
        color: #919191;
      }

      .btn-secondary {
        background-color: transparent;
        color: $primary--blue;
        border: 1px solid $primary--blue;
      }

      >* {
        margin-right: 8px;
      }
    }
  }

  .status-icon-container {
    width: 60px;
    display: flex;
    justify-content: center;

    .status {
      display: flex;
      align-items: center;

      mat-icon {
        margin-right: 10px;
      }
    }

    .red {
      color: $secondary--red;
    }

    .yellow {
      color: $secondary--orange;
    }

    .green {
      color: $secondary--green-dark;
    }

    .grey {
      color: $primary--grey !important;
    }

    .blue {
      color: $primary--blue;
    }
  }

  .level {
    margin: -2px -4px;
    padding: 2px 4px;
    border-radius: 3px;

    &.trace {
       color: #000000; 
    }

    &.debug {
      color: #0545fa;
    }

    &.info {
      color: #2e7d32;
    }

    &.warn {
      color: #f0ad4e;
    }

    &.error {
      color: #d9534f;
    }

    &.fatal {
      color: #c82333;
    }

  }

  :ng-deep.mat-select-trigger {
    padding: 10px;
  }

}

::ng-deep .cdk-column-message div,
::ng-deep .cdk-column-logId div,
::ng-deep .cdk-column-logger div,
::ng-deep .cdk-column-application div {
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
}