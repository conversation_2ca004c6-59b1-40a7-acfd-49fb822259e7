import { KFTableColumn } from "@/components/controls";
import { SystemLogDetailsComponent } from "@/components/dialogs";
import { AlertService, ProjectService, SpinnerService } from "@/services";
import { SystemLogRecord } from "@/shared/models";
import { Component, OnInit, TemplateRef, ViewChild } from "@angular/core";
import { MatDialog } from "@angular/material";
import { ActivatedRoute } from "@angular/router";
import { SystemLogComponentContext } from "./system-log.component.context";

@Component({
  selector: "app-system-log",
  templateUrl: "./system-log.component.html",
  styleUrls: ["./system-log.component.scss"],
})
export class SystemLogComponent implements OnInit {
  @ViewChild("LEVEL_TPL") LEVEL_TPL: TemplateRef<any>;
  context = new SystemLogComponentContext();
  defaultStartDate: string;
  defaultEndDate: string;
  readonly MAX_PAGE_LIMIT = 1000;

  constructor(
    private route: ActivatedRoute,
    private service: ProjectService,
    private spinnerService: SpinnerService,
    private alertService: AlertService,
    public dialog: MatDialog
  ) {}

  ngOnInit() {
    this.context.columns = this.logColumns;

    this.defaultStartDate = this.getDefaultStartDate();
    this.defaultEndDate = this.getDefaultEndDate();

    this.context.startDateString = this.defaultStartDate;
    this.context.endDateString = this.defaultEndDate;

    this.context.typeOptions = [
      { id: "Job", value: "Job" },
      { id: "Integration", value: "Integration" },
      { id: "Website", value: "Website" },
    ];

    this.context.levelOptions = this.logsLevelOptions;
    this.route.data.subscribe((resolved) => {
      this.context.client = resolved.client;

      this.refreshData();
    });
  }

  refreshData() {
    const context = this.context;

    const isDefaultPaging =
      context.customPaging.pageIndex === 1 &&
      context.customPaging.pageSize === 10;
    const isDefaultSorting =
      context.sortColumn === "Created" && context.sortDescending === true;
    const hasFilters = this.hasMainFilters();

    const isInitialLoad = !hasFilters && isDefaultPaging && isDefaultSorting;

    if (isInitialLoad) {
      // First load with no filters — skip API call
      context.filteredRecords = [];
      context.customPaging.totalResultRecords = 0;
      context.hasInitialEmptyLoad = true;
      return;
    }

    // Prevent future API calls if nothing changed and no filters exist
    if (!hasFilters && context.hasInitialEmptyLoad) {
      return;
    }

    const queryParams = this.buildQueryParams();

    this.spinnerService.activate();
    this.service.getSystemLogs(queryParams).subscribe({
      next: function (res: any) {
        const context = this.context;
        context.filteredRecords = (res && res.systemLogResults) || [];
        context.customPaging = {
          pageIndex: (res && res.pageNumber) || 1,
          pageSize: (res && res.pageSize) || 10,
          totalResultRecords: (res && res.totalResultRecords) || 0,
          totalPages: (res && res.totalPages) || 0,
        };
        this.spinnerService.deactivate();
      }.bind(this),
      error: function (err) {
        this.spinnerService.deactivate();
        this.alertService.error((err && err.message) || "Failed to load logs");
      }.bind(this),
    });
  }

  private buildQueryParams(): any {
    const context = this.context;
    const params: any = {
      PageNumber: context.customPaging.pageIndex,
      PageSize: context.customPaging.pageSize,
      SortColumn: context.sortColumn || "Created",
      SortDescending: context.sortDescending !== false,
      StartDate: context.startDateString + "T00:00",
      EndDate: context.endDateString + "T23:59",
    };

    if (context.selectedLevel) params["Level.Value"] = context.selectedLevel;
    if (context.selectedType) params["LogType.Value"] = context.selectedType;
    if (context.loggerFilter && context.loggerFilter.trim() !== "") {
      params["Logger.Value"] = context.loggerFilter.trim();
      params["Logger.Op"] = "Like";
    }
    if (context.messageFilter && context.messageFilter.trim() !== "") {
      params["Message.Value"] = context.messageFilter.trim();
      params["Message.Op"] = "Like";
    }
    if (context.applicationFilter && context.applicationFilter.trim() !== "") {
      params["Application.Value"] = context.applicationFilter.trim();
      params["Application.Op"] = "Like";
    }

    return params;
  }

  openSystemLogDetails(record: SystemLogRecord) {
    const logId = record.logId;
    const logDate = record.created;
    const queryParams = {
      logId,
      logDate,
    };

    this.spinnerService.activate();

    this.service.getSystemLogDetails(queryParams).subscribe({
      next: (res) => {
        this.spinnerService.deactivate();
        this.dialog.open(SystemLogDetailsComponent, {
          width: "900px",
          data: res,
        });
      },
      error: (err) => {
        const errorMessage =
          (err && err.message) || "Failed to load log details";
        this.spinnerService.deactivate();
        this.alertService.error(errorMessage);
      },
    });
  }

  get logColumns(): KFTableColumn<SystemLogRecord>[] {
    return [
      {
        type: "text",
        name: "logId",
        label: "ID",
        sortable: true,
        width: "300px",
        click: (record: SystemLogRecord) => {
          this.openSystemLogDetails(record);
        },
      },
      {
        type: "text",
        name: "type",
        label: "Type",
        sortable: true,
        width: "80px",
      },

      {
        type: "textCapitalized",
        name: "level",
        label: "Level",
        sortable: true,
        width: "80px",
        template: this.LEVEL_TPL,
      },
      {
        type: "text",
        name: "logger",
        label: "Logger",
        sortable: true,
        width: "210px",
      },
      {
        type: "date",
        name: "created",
        label: "Created",
        width: "180px",
        dateFormat: "dd MMM yyyy, HH:mm:ss",
        sortable: true,
      },
      {
        type: "text",
        name: "message",
        label: "Message",
        sortable: true,
        width: "250px",
      },
      {
        type: "text",
        name: "machine",
        label: "Machine",
        sortable: true,
        width: "150px",
      },
      {
        type: "text",
        name: "application",
        label: "Application",
        sortable: true,
        width: "150px",
      },
    ];
  }

  get logsLevelOptions() {
    return [
      { value: "Debug", id: "Debug" },
      { value: "Error", id: "Error" },
      { value: "Fatal", id: "Fatal" },
      { value: "Info", id: "Info" },
      { value: "Trace", id: "Trace" },
      { value: "Warn", id: "Warn" },
    ];
  }

  onPageChange(event: any) {
    const { pageSize, pageIndex } = event;
    const newPageIndex = pageIndex + 1;

    if (newPageIndex > this.MAX_PAGE_LIMIT) {
      this.alertService.error(
        `You can only view up to 10,000 records. Try filtering or sorting to access more data.`
      );
      return;
    }

    if (
      this.context.customPaging.pageSize !== pageSize ||
      this.context.customPaging.pageIndex !== newPageIndex
    ) {
      this.context.customPaging.pageSize = pageSize;
      this.context.customPaging.pageIndex = newPageIndex;
      this.refreshData();
    }
  }

  onSortChange(event: { active: string; direction: "asc" | "desc" | "" }) {
    const { active, direction } = event;

    if (!active || !direction) return;

    const sortMap: Record<string, string> = {
      type: "LogType",
      level: "Level",
      logger: "Logger",
      created: "Created",
      message: "Message",
      machine: "Machine",
      application: "Application",
      logId: "LogId",
    };

    this.context.sortColumn =
      sortMap[active] || active.charAt(0).toUpperCase() + active.slice(1);
    this.context.sortDescending = direction === "desc";
    this.context.customPaging.pageIndex = 1;

    this.refreshData();
  }

  clearFilters() {
    this.context.selectedLevel = null;
    this.context.selectedType = null;
    this.context.loggerFilter = "";
    this.context.messageFilter = "";
    this.context.applicationFilter = "";
    this.context.startDateString = this.getDefaultStartDate();
    this.context.endDateString = this.getDefaultEndDate();
    this.context.sortColumn = "Created";
    this.context.sortDescending = true;
    this.context.customPaging.pageIndex = 1;
    this.refreshData();
  }

  hasFilters(): boolean {
    return (
      !!this.context.selectedLevel ||
      !!this.context.selectedType ||
      !!this.context.loggerFilter ||
      !!this.context.messageFilter ||
      !!this.context.applicationFilter ||
      this.context.startDateString !== this.getDefaultStartDate() ||
      this.context.endDateString !== this.getDefaultEndDate()
    );
  }

  hasMainFilters(): boolean {
    return (
      !!this.context.selectedLevel ||
      !!this.context.selectedType ||
      !!this.context.loggerFilter ||
      !!this.context.messageFilter ||
      !!this.context.applicationFilter
    );
  }

  private getDefaultStartDate(): string {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    return yesterday.toISOString().split("T")[0];
  }

  private getDefaultEndDate(): string {
    return new Date().toISOString().split("T")[0];
  }

  onFilterChange() {
    const { startDateString, endDateString } = this.context;

    if (startDateString && endDateString && startDateString > endDateString) {
      this.alertService.error("End date cannot be less than Start date");
      return;
    }
    this.context.customPaging.pageIndex = 1;
    this.refreshData();
  }
}
