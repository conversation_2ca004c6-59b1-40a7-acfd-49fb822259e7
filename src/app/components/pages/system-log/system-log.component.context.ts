import { KFTableColumn } from "@/components/controls";
import { SystemLogRecord, Client } from "@/shared/models";

export class SystemLogComponentContext {
  client: Client;
  startDateString: string;
  endDateString: string;
  selectedLevel: string = "Info";
  selectedType: string = "";
  loggerFilter: string = "";
  messageFilter: string = "";
  applicationFilter: string = "";
  columns: KFTableColumn<SystemLogRecord>[] = [];
  filteredRecords: SystemLogRecord[] = [];
  levelOptions: { id: string; value: string }[] = [];
  typeOptions: { id: string; value: string }[] = [];
  customPaging = {
    pageIndex: 1,
    pageSize: 10,
    totalResultRecords: 0,
    totalPages: 0,
  };
  sortColumn: string = "Created";
  sortDescending: boolean = true;
  hasInitialEmptyLoad: boolean = false;
}
