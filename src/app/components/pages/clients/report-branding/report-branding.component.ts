import { SpinnerService } from '@/services';
import { BrandingService } from '@/services/branding.service';
import { AuthRole, Client } from '@/shared/models';
import {
  bgPositions,
  logoPositions,
  BrandingData,
  BrandingSettings,
} from '@/shared/models/configurable-reports/brandingSettings';
import { ReportAsset } from '@/shared/models/configurable-reports/ReportAsset';
import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { saveAs } from 'file-saver';
import * as _ from 'lodash';

@Component({
  selector: 'app-report-branding',
  styleUrls: ['./report-branding.component.scss'],
  templateUrl: './report-branding.component.html',
})
export class ReportBrandingComponent implements OnInit {
  get currentState(): BrandingSettings {
    return _.cloneDeep(this.branding.settings);
  }

  set currentState(settings: BrandingSettings) {
    this.branding.settings =  _.cloneDeep(settings);
    this.dataChange.emit(this.branding);
  }

  @Output() public dataChange = new EventEmitter();
  @Output() public assetsChange = new EventEmitter();

  public bgPositions = bgPositions;
  public logoPositions = logoPositions;

  public loading: boolean;
  public routes = [
    {
      title: 'Assets',
      routerLink: ['/clients/branding-assets'],
      queryParams: {},
      allowedRoles: ['admin', 'reportManagement', 'productDelivery'] as AuthRole[]
    },
    {
      title: 'Branding',
      routerLink: ['/clients/report-branding'],
      queryParams: {},
      allowedRoles: ['admin', 'reportManagement', 'productDelivery'] as AuthRole[]
    },
  ];

  public savedForm: BrandingSettings;
  public defaultForm = {
    clientLogoPath: '',
    clientLogoPosition: 'Right',
    coverImageFormat: 'LowerTwoThirds',
    coverImagePath: '',
    footerBarColour: null,
    metadataDetailsTextColour: null,
    metadataTextColour: null,
    solutionTextColour: null,
    subTitleTextColour: null,
    textInputTextColour: null,
    titleTextColour: null,
  } as BrandingSettings;
  public assets: ReportAsset[];
  private branding: BrandingData;
  client: Client;
  private initialVersion: BrandingSettings;
  private stateIndex = -1;
  private history = [];

  constructor(
    private brandingService: BrandingService,
    private spinnerService: SpinnerService,
    private route: ActivatedRoute
  ) {}

  public ngOnInit() {
    this.route.data.subscribe((resolved) => {
      Object.assign(this, resolved);
      this.branding = resolved.branding;
      this.client = resolved.client;
      this.assets = resolved.assets;
      this.routes.forEach(
        (route) => (route.queryParams = { clientId: this.client.id })
      );

      if (this.branding.id === 0) {
        this.currentState = this.defaultForm;
      }
      this.initialVersion = this.currentState;
      this.save();
    });
  }

  public save() {
    this.history[++this.stateIndex] = this.currentState;
    this.history.splice(this.stateIndex + 1);
  }

  public undo() {
    this.stateIndex = Math.max(this.stateIndex - 1, 0);
    this.currentState = this.history[this.stateIndex];
  }

  public redo() {
    this.stateIndex = Math.min(this.stateIndex + 1, this.history.length - 1);
    this.currentState = this.history[this.stateIndex];
  }

  public reset() {
    this.currentState = this.initialVersion;
    this.save();
  }

  public clean() {
    this.currentState = this.defaultForm;
    this.save();
  }

  public confirmSettings() {
    this.spinnerService.activate();
    this.brandingService.updateSettings(this.branding).subscribe((newID) => {
      this.spinnerService.deactivate();
      this.initialVersion = this.currentState;
      this.branding.id = newID;
    });
  }

  public downloadPreview(reportId) {
    this.spinnerService.activate();
    this.brandingService
      .downloadPreview(reportId, this.client.id, this.branding.settings)
      .subscribe(
        (result) => {
          this.spinnerService.deactivate();
          saveAs(result, 'Preview.pdf');
        },
        (err) => {
          this.spinnerService.deactivate();
        }
      );
  }

  public onAssetsChange($event) {
    this.assets = $event;
    this.assetsChange.emit(this.assets);
  }
}
