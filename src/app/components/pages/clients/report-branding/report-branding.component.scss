@import "../../../../../styles/theme.scss";
@import "../../../../../styles/typography.scss";
@import "../../../../../styles/colors.scss";
@import "../../../../../styles/buttons.scss";

$fullhd: 1800px;

@mixin fullhd {
  @media (min-width: #{$fullhd}) {
    @content;
  }
}

:host .branding {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  padding: 0 64px 48px 64px;
  user-select: none;
  position: relative;

  img {
    -moz-window-dragging: none;
    -webkit-user-drag: none;
  }

  .data-actions {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
    margin: 12px 0;

    .state-indicator {
      font-size: 12px;
      white-space: nowrap;
      margin: 6pt 0;

      .mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }

    .state-indicator {
      color: white;
    }

    button {
      @extend .glass;

      width: 150px;
      white-space: nowrap;
      background-color: $primary--blue;
      margin-left: 12px;
      margin-right: 0;

      &.mat-icon-button {
        margin-top: 12pt;
      }
    }
  }

  .history-actions {
    @extend .glass;

    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;

    a {
      display: block;
      font-size: 11px;
      width: 20%;
      font-weight: bold;
      text-align: center;
      padding: 9px;
      padding-bottom: 6px;

      span {
        display: block;
      }

      .mat-icon {
        opacity: 0.8;
      }

      &:not(:last-of-type) {
        border-right: solid thin rgba(0, 0, 0, 0.075);
      }

      &:not(:first-of-type) {
        border-left: solid thin rgba(255, 255, 255, 0.15);
      }

      &.preview {
        color: $primary--blue;

        span {
          font-weight: bold;
        }
      }

      &:hover {
        background: rgba(0, 0, 0, 0.05);
      }

      &:active {
        background: rgba(0, 0, 0, 0.15);

        &:not(:last-of-type) {
          border-right: solid thin rgba(255, 255, 255, 0.075);
        }

        &:not(:first-of-type) {
          border-left: solid thin rgba(0, 0, 0, 0.0325);
        }
      }
    }
  }

  .branding-name-row {
    @extend .glass;

    display: block;
    padding: 24px;
    margin: 10px 0px;
  }

  .front-cover-preview-form {
    flex-grow: 1;
    display: flex;
    justify-content: center;
  }

  .upload-assets-form.desktop {
    display: none;

    @include fullhd {
      display: block;
    }
  }
}

.upload-assets-form.tablets {
  display: block;

  @include fullhd {
    display: none;
  }
}
