@import "../../../../../styles/theme.scss";
@import "../../../../../styles/colors.scss";

:host {
  flex: 1 1 auto;

  display: flex;
  flex-direction: column;
  justify-content: stretch;

  .form {
    input[type="text"] {
      padding: 6px 12px;
    }

    input[type="checkbox"].checkbox-margin {
      margin: 12px 0;
    }

    select {
      padding: 6px 9px;
    }

    input[type="text"],
    select {
      @extend .glass;

      width: 100%;
    }

    .form-control {
      margin: 6px 0;
      display: flex;
      justify-content: flex-start;
      align-items: center;

      .label {
        width: 200px;
        min-width: 200px;
      }
      .input {
        width: 300px;
        min-width: 300px;

        input[type="text"],
        select {
          &:not(:focus) {
            &.ng-touched.ng-invalid {
              box-shadow: 0px 0px 3px 0px $secondary--red;
            }
          }
        }
        label {
          &:not(:focus) {
            &.ng-touched.ng-invalid {
              input {
                box-shadow: 0px 2px 4px 0px $secondary--red;
              }
            }
          }
        }
      }
      .validation {
        flex-grow: 1;
        color: $secondary--red;
        font-size: 12px;
        margin-left: 12px;
      }
    }

    .error-container {
      border-top: solid thin #e5e1e1;
      padding-top: 24px;
      margin-top: 24px;

      .message {
        @extend .glass;

        margin-top: 12px;
        padding: 12px;
      }
    }
  }
}
