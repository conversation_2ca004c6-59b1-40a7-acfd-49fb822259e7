<div class="details-page-content white-glass form">
  <form [formGroup]="form" (ngSubmit)="onSubmit()">
    <div class="form-control">
      <div class="label">Manifest:</div>
      <div class="input">
        <input
          type="text"
          formControlName="manifestId"
          [attr.disabled]="loading || null"
        />
      </div>
    </div>
    <div class="form-control" *ngIf="reportPreviewOptions">
      <div class="label">CMS environment</div>
      <div class="input">
        <select
          formControlName="cmsEnvironment"
          [attr.disabled]="loading || null"
        >
          <option disabled>Select CMS environment...</option>
          <option
            *ngFor="let env of reportPreviewOptions.cmsEnvironment"
            [value]="env"
          >
            {{ env }}
          </option>
        </select>
      </div>
      <div
        *ngIf="
          form.controls.cmsEnvironment.touched &&
          form.controls.cmsEnvironment.invalid
        "
        class="validation"
      >
        CMS environment required
      </div>
    </div>
    <div class="form-control">
      <div class="label">Client name:</div>
      <div class="input">
        <input type="hidden" formControlName="clientId" />
        <input
          type="text"
          formControlName="clientName"
          [attr.disabled]="loading || null"
        />
      </div>
    </div>
    <div class="form-control">
      <div class="label">Participant name:</div>
      <div class="input">
        <input
          type="text"
          formControlName="candidateName"
          [attr.disabled]="loading || null"
        />
      </div>
    </div>
    <div class="form-control" *ngIf="reportPreviewOptions">
      <div class="label">Show fit scores:</div>
      <div class="input">
        <input
          class="checkbox-margin"
          type="checkbox"
          formControlName="showFitScores"
          [checked]="reportPreviewOptions.showFitScores"
          [attr.disabled]="loading || null"
        />
      </div>
    </div>
    <div class="form-control" *ngIf="reportPreviewOptions">
      <div class="label">Scoring pattern</div>
      <div class="input">
        <select formControlName="scoringType" [attr.disabled]="loading || null">
          <option disabled>Select scoring pattern...</option>
          <option
            *ngFor="let sp of reportPreviewOptions.scoreTypes"
            [value]="sp"
          >
            {{ sp }}
          </option>
        </select>
      </div>
      <div
        *ngIf="
          form.controls.scoringType.touched && form.controls.scoringType.invalid
        "
        class="validation"
      >
        Scoring pattern required
      </div>
    </div>
    <div class="form-control" *ngIf="reportPreviewOptions">
      <div class="label">Language</div>
      <div class="input">
        <select
          formControlName="reportLanguageId"
          [attr.disabled]="loading || null"
        >
          <option disabled>Select language...</option>
          <option
            *ngFor="let l of reportPreviewOptions.languages"
            [value]="l.id"
          >
            {{ l.name }}
          </option>
        </select>
      </div>
      <div
        *ngIf="
          form.controls.reportLanguageId.touched &&
          form.controls.reportLanguageId.invalid
        "
        class="validation"
      >
        Language required
      </div>
    </div>
    <div class="form-control" *ngIf="reportPreviewOptions">
      <div class="label">Project type:</div>
      <div class="input">
        <select formControlName="projectType" [attr.disabled]="loading || null">
          <option disabled>Select project...</option>
          <option
            *ngFor="let t of reportPreviewOptions.projects"
            [value]="t.type"
          >
            {{ t.name }}
          </option>
        </select>
      </div>
      <div
        *ngIf="
          form.controls.projectType.touched && form.controls.projectType.invalid
        "
        class="validation"
      >
        Project type required
      </div>
    </div>
    <div
      class="form-control"
      *ngIf="reportPreviewOptions && requireSuccessProfile"
    >
      <div class="label">Success profile</div>
      <div class="input">
        <select
          formControlName="successProfileId"
          [attr.disabled]="loading || null"
        >
          <option disabled>Select success profile...</option>
          <option
            *ngFor="let t of reportPreviewOptions.successProfiles"
            [value]="t.id"
          >
            {{ t.name }}
          </option>
        </select>
      </div>
      <div
        *ngIf="
          form.controls.successProfileId.touched &&
          form.controls.successProfileId.invalid
        "
        class="validation"
      >
        Success profile required
      </div>
    </div>
    <div
      class="form-control"
      *ngIf="
        projectAssessmentOptions &&
        projectAssessmentOptions.scoreDisplay.requiresScoreDisplay
      "
    >
      <div class="label">Score display:</div>
      <div class="input">
        <select
          formControlName="scoreDisplay"
          [attr.disabled]="loading || null"
        >
          <option disabled>Select scoring display...</option>
          <option
            *ngFor="
              let sd of projectAssessmentOptions.scoreDisplay.scoreDisplayTypes
            "
            [value]="sd"
          >
            {{ sd }}
          </option>
        </select>
      </div>
      <div
        *ngIf="form.touched && form.controls.scoreDisplay.invalid"
        class="validation"
      >
        Score display required
      </div>
    </div>
    <div class="form-control" *ngIf="assessmentControls.length">
      <div class="label">Assessments:</div>
      <div class="input">
        <div *ngFor="let a of assessmentControls; let i = index">
          <label formArrayName="kfasAssessmentIds">
            <input
              type="checkbox"
              [formControlName]="i"
              [attr.disabled]="
                loading ||
                projectAssessmentOptions.projectAssessments[i].isMandatory ||
                null
              "
            />
            {{ projectAssessmentOptions.projectAssessments[i].name }}
          </label>
        </div>
      </div>
      <div
        *ngIf="form.touched && form.controls.kfasAssessmentIds.invalid"
        class="validation"
      >
        <div *ngIf="form.controls.kfasAssessmentIds.errors.required">
          You need to select at least one assessment
        </div>
        <div
          *ngIf="
            form.controls.kfasAssessmentIds.errors.maxAssessmentsAllowedExceeded
          "
        >
          You can select no more than
          {{
            form.controls.kfasAssessmentIds.errors.maxAssessmentCount
          }}
          assessments
        </div>
      </div>
    </div>
    <div class="form-control" *ngIf="projectAssessmentOptions.hasRoleLevels">
      <div class="label">Current level:</div>
      <div class="input">
        <select
          formControlName="currentLevel"
          [attr.disabled]="loading || null"
        >
          <option disabled>Select level...</option>
          <option
            *ngFor="let t of projectAssessmentOptions.levels"
            [value]="t.code"
          >
            {{ t.name }}
          </option>
        </select>
      </div>
      <div
        *ngIf="
          form.controls.currentLevel.touched &&
          form.controls.currentLevel.invalid
        "
        class="validation"
      >
        Level required
      </div>
    </div>
    <div class="form-control" *ngIf="projectAssessmentOptions.hasRoleLevels">
      <div class="label">Target level:</div>
      <div class="input">
        <select formControlName="targetLevel" [attr.disabled]="loading || null">
          <option disabled>Select level...</option>
          <option
            *ngFor="let t of projectAssessmentOptions.levels"
            [value]="t.code"
          >
            {{ t.name }}
          </option>
        </select>
      </div>
      <div
        *ngIf="
          form.controls.targetLevel.touched && form.controls.targetLevel.invalid
        "
        class="validation"
      >
        Level required
      </div>
    </div>
    <div class="form-control" *ngIf="reportPreviewOptions">
      <div class="label">Continue on warning:</div>
      <div class="input">
        <input
          class="checkbox-margin"
          type="checkbox"
          formControlName="continueOnWarning"
          [attr.disabled]="loading || null"
        />
      </div>
      <div *ngIf="form.controls.continueOnWarning.value" class="validation">
        You will not be able to see warnings messages created during report
        generation
      </div>
    </div>
    <div class="form-control">
      <div class="label"></div>
      <div class="input">
        <input
          type="submit"
          class="btn btn-primary"
          [disabled]="!form.valid"
          value="Download"
        />
      </div>
    </div>
  </form>
  <div class="error-container" *ngIf="formattedError.length">
    <h5>Preview failed. Please check the following errors and try again.</h5>
    <div class="message error">
      <div *ngFor="let e of formattedError">{{ e }}</div>
    </div>
  </div>
</div>
