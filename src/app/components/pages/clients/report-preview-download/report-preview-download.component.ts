import { SpinnerService } from '@/services';
import { AlertService } from '@/services/alert.service';
import { ReportPreviewService } from '@/services/report-preview.service';
import { Client } from '@/shared/models';
import { ProjectAssessmentOptions } from '@/shared/models/configurable-reports/projectAssessment';
import {
  ReportPreviewOptions,
  ReportPreviewOptionsProject,
} from '@/shared/models/configurable-reports/reportPreviewOptions';
import { Component, HostBinding, OnInit } from '@angular/core';
import {
  AbstractControl,
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { saveAs } from 'file-saver';
import * as _ from 'lodash';

@Component({
  selector: 'app-report-preview-download',
  templateUrl: './report-preview-download.component.html',
  styleUrls: ['./report-preview-download.component.scss'],
})
export class ReportPreviewDownloadComponent implements OnInit {
  private error: string;
  client: Client;
  private selectedProject: ReportPreviewOptionsProject;
  private formData: Map<string, any> = new Map<string, any>();

  loading = false;
  projectAssessmentOptions: ProjectAssessmentOptions = new ProjectAssessmentOptions();
  requireSuccessProfile: boolean;
  form: FormGroup;
  reportPreviewOptions: ReportPreviewOptions = new ReportPreviewOptions();
  debouncedTimer: any;
  latestProjectType: any;
  latestReportLanguageId: any;
  latestSuccessProfileId: any;
  savedValues: any;

  get formattedError() {
    return this.error ? this.error.split('\n') : [];
  }

  @HostBinding('class') get themeClass() {
    return this.loading ? 'loading' : '';
  }

  constructor(
    private fb: FormBuilder,
    private service: ReportPreviewService,
    private spinnerService: SpinnerService,
    private alertService: AlertService,
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
    this.spinnerService.activate();
    this.route.data.subscribe((resolved) => {
      this.spinnerService.deactivate();
      Object.assign(this, resolved);
    });

    this.form = this.fb.group({
      manifestId: ['', Validators.required],
      cmsEnvironment: ['', Validators.required],
      clientId: ['', Validators.required],
      clientName: ['', Validators.required],
      candidateName: ['', Validators.required],
      projectType: ['', Validators.required],
      showFitScores: [true],
      successProfileId: [
        '',
        this.getConditionalRequiredValidator(() => this.requireSuccessProfile),
      ],
      kfasAssessmentIds: new FormArray([], this.getAssessmentsValidator(this)),
      scoringType: [''],
      reportLanguageId: ['', Validators.required],
      scoreDisplay: [
        '',
        this.getConditionalRequiredValidator(
          () =>
            this.projectAssessmentOptions &&
            this.projectAssessmentOptions.scoreDisplay.requiresScoreDisplay
        ),
      ],
      currentLevel: [
        '',
        this.getConditionalRequiredValidator(
          () =>
            this.projectAssessmentOptions &&
            this.projectAssessmentOptions.hasRoleLevels
        ),
      ],
      targetLevel: [
        '',
        this.getConditionalRequiredValidator(
          () =>
            this.projectAssessmentOptions &&
            this.projectAssessmentOptions.hasRoleLevels
        ),
      ],
      continueOnWarning: [false],
    });

    this.form.get('clientId').setValue(this.client.id);
    this.form.get('clientName').setValue(this.client.name);

    this.form.get('projectType').valueChanges.subscribe((value) => {
      this.selectedProject = this.reportPreviewOptions.projects.find(
        (x) => x.type === value
      );
      this.requireSuccessProfile =
        this.selectedProject && this.selectedProject.requiresSuccessProfile;
      this.loadAssessments();
    });
    this.form.get('successProfileId').valueChanges.subscribe(() => {
      this.loadAssessments();
    });
    this.form.get('reportLanguageId').valueChanges.subscribe(() => {
      this.loadAssessments();
    });
  }

  get assessmentControls() {
    return this.form.get('kfasAssessmentIds')['controls'];
  }

  loadAssessments() {
    clearTimeout(this.debouncedTimer);
    this.debouncedTimer = setTimeout(() => {
      this.debouncedLoadAssessments();
    }, 250);
  }

  debouncedLoadAssessments() {
    if (this.loading) {
      return;
    }
    const projectType = this.form.controls.projectType.value;
    const successProfileId = this.form.controls.successProfileId.value;
    const reportLanguageId = this.form.controls.reportLanguageId.value;
    const requireSP = this.requireSuccessProfile;
    if (!projectType || (requireSP && !successProfileId) || !reportLanguageId) {
      return;
    }

    if (
      this.latestProjectType === projectType &&
      this.latestReportLanguageId === reportLanguageId &&
      this.latestSuccessProfileId === (requireSP ? successProfileId : null)
    ) {
      return;
    }

    this.latestProjectType = projectType;
    this.latestReportLanguageId = reportLanguageId;
    this.latestSuccessProfileId = requireSP ? successProfileId : null;

    this.spinnerService.activate();
    this.form.disable();
    this.service
      .getAssessments(
        this.client.id,
        projectType,
        reportLanguageId,
        requireSP ? successProfileId : null
      )
      .subscribe(
        (result) => {
          this.projectAssessmentOptions = result;
          const control = this.form.controls.kfasAssessmentIds as FormArray;

          while (control.length) {
            control.removeAt(0);
          }
          result.projectAssessments.forEach((a) => {
            control.push(
              new FormControl(a.isMandatory || a.isRecommended || false)
            );
          });

          this.form.enable();
          this.spinnerService.deactivate();
        },
        (error) => {
          this.alertService.error(error.message);
          this.projectAssessmentOptions = new ProjectAssessmentOptions();

          this.form.enable();
          this.spinnerService.deactivate();
        }
      );
  }

  getFormData() {
    const formData = this.form.value;
    formData.kfasAssessmentIds = formData.kfasAssessmentIds
      .map((v, i) =>
        Object.assign(this.projectAssessmentOptions.projectAssessments[i], {
          checked: v,
        })
      )
      .filter((v) => v.checked)
      .map((v) => v.kfasAssessmentId);

    for (const member in formData) {
      if (formData.hasOwnProperty(member)) {
        this.formData.set(member, formData[member]);
      }
    }
    return this.formData;
  }

  onSubmit() {
    this.spinnerService.activate();
    this.service.getPdf(this.getFormData()).subscribe(
      (result) => {
        this.spinnerService.deactivate();
        this.error = '';
        saveAs(result, 'Preview.pdf');
      },
      (error) => {
        this.error = error.message;
        this.spinnerService.deactivate();
      }
    );
  }

  getAssessmentsValidator(context) {
    return function assessmentsValidator(control: AbstractControl) {
      // don't show errors if no assessments
      if (
        !context.projectAssessmentOptions ||
        !context.projectAssessmentOptions.projectAssessments ||
        !context.projectAssessmentOptions.projectAssessments.length
      ) {
        return null;
      }

      const selectedValuesCount = control.value.filter((x) => x).length;

      if (!selectedValuesCount) {
        return { required: true };
      }

      if (
        selectedValuesCount >
        context.projectAssessmentOptions.maxAssessmentsAllowed
      ) {
        return {
          maxAssessmentsAllowedExceeded: true,
          maxAssessmentCount:
            context.projectAssessmentOptions.maxAssessmentsAllowed,
        };
      }

      return null;
    };
  }

  getConditionalRequiredValidator(condition: Function) {
    return function validator(control: AbstractControl) {
      if (condition()) {
        return Validators.required(control);
      } else {
        return null;
      }
    };
  }

  encodeGetParams(obj) {
    return Object.keys(obj)
      .map((k) => `${encodeURIComponent(k)}=${encodeURIComponent(obj[k])}`)
      .join('&');
  }
}
