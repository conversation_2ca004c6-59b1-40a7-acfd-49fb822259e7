<div class="details-page-content white-glass">
  <div class="row">
    <div class="col">
      <div class="group-title">General Information</div>
      <div class="group" *ngFor="let item of generalInfo">
        <div class="label">{{ item.label }}</div>
        <div class="value">
          {{ item.value || "-" }}
        </div>
      </div>
      <div class="stakeholder-info">
        Stakeholder List
        <span class="show-stakeholder" (click)="navigateCollabStakeholder()">Show Stakeholder List</span>
      </div>
    </div>

    <div class="col">
      <div class="group-title">Settings</div>
      <div class="group" *ngFor="let item of settings">
        <div class="label long">{{ item.label }}</div>
        <div class="value">
          <span *ngIf="item.type === 'text'">
            {{ item.value }}
          </span>
          <span *ngIf="item.type === 'date'">
            {{ item.value ? (item.value | date: "d MMM yyyy") : "No end date" }}
          </span>
        </div>
      </div>

      <div class="group-modules">
        <div class="label long group-title">Modules</div>
        <div *ngFor="let module of modules">
          <div class="modules-title">{{ module }}</div>
        </div>
      </div>
    </div>
  </div>
</div>