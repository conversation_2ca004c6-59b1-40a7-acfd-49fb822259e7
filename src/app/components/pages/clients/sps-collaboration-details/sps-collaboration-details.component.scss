@import "../../../../../styles/theme.scss";
@import "../../../../../styles/colors.scss";

$label-width: 200px;
$label-width-long: 228px;
$label-width-short: 100px;
$value-padding: 6px;
$value-width: 200px;
$side-margin: 48px 64px;

:host {
  flex: 1;
}

.group-title {
  color: $primary--grey-medium;
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 1.5px;
  font-weight: 400;
}

.row {
  display: flex;

  &:not(:last-child) {
    margin-bottom: 12px;
  }

  .col {
    flex: 50%;
  }
}

.group {
  display: flex;
}

.label {
  min-width: $label-width;
  padding: $value-padding 0;

  &.short {
    width: $label-width-short;
  }

  &.long {
    width: $label-width-long;
  }
}

.value {
  font-weight: bold;
  padding: $value-padding 0;
  width: 235px;
}

.group {
  &.modules {

    .label,
    .value {
      padding: 0;
      line-height: 24px;
    }

    .group-title {
      font-weight: 400;
      margin-top: 12px;
    }

    &:last-of-type {
      margin-bottom: 12px;
    }
  }
}

.stakeholder-info {
  padding-top: 8px;

  .show-stakeholder {
    padding-left: 15%;
    font-weight: 600;
    color: $primary--blue;
    cursor: pointer;
  }
}

.modules-title{
  padding-bottom: 8px;
}