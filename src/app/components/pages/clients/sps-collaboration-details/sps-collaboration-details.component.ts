import { Component, OnInit } from '@angular/core';
import { CollaborationDetailsComponentContext } from './sps-collaboration-details.component.context';
import { ActivatedRoute, Router } from '@angular/router';
import { AlertService, PageHeaderService, SpinnerService } from '@/services';
import { SharedService } from '@/shared/services/shared.service';

@Component({
  selector: 'app-sps-collaboration-details',
  templateUrl: './sps-collaboration-details.component.html',
  styleUrls: ['./sps-collaboration-details.component.scss']
})
export class SpsCollaborationDetailsComponent implements OnInit {
  public context = new CollaborationDetailsComponentContext();
  modules = ['Competencies', 'Job Analysis', 'Culture Sort'];
  constructor(private router: Router, private pageHeaderService: PageHeaderService, private route: ActivatedRoute, private sharedService:SharedService, private spinnerService: SpinnerService, private alertService: AlertService,) { }

  ngOnInit() {
    this.route.data.subscribe(
      (resolved) => {
        this.context.client = resolved.client;
        this.context.collaboration = resolved.collaboration;
        this.spinnerService.deactivate();
        const collabId = this.context.collaboration.data.collabDetails[0].collabId;
        const surveyName = this.context.collaboration.data.collabDetails[0].spTitle;
        const collabDetails = {
          collabId: collabId,
          surveyName: surveyName
        };
        localStorage.setItem('spsCollabDetails', JSON.stringify(collabDetails));
        this.pageHeaderService.setSPSDetails('COLLABORATION DETAILS', surveyName)
      },
      (error) => {
        this.alertService.error(error.message);
        this.spinnerService.deactivate();
      }
    );
    
  }

  get generalInfo() {
    return [
      { label: 'Collaboration ID', value: this.context.collaboration.data.collabDetails[0].collabId },
      { label: 'Survey Name', value: this.context.collaboration.data.collabDetails[0].spTitle },
      { label: 'Original SP ID', value: this.context.collaboration.data.collabDetails[0].originalSpId },
      { label: 'New SP ID', value: this.context.collaboration.data.collabDetails[0].spId },
      { label: 'Type ', value: this.context.collaboration.data.collabDetails[0].sourceSystemName },
      { label: 'Assess/Select Project ID', value: this.context.collaboration.data.collabDetails[0].projectId },
      { label: 'Owner', value: this.context.collaboration.data.collabOwnerEmailId },
    ];
  }

  get settings() {
    return [
      {
        label: 'Created Date',
        value: this.context.collaboration.data.collabDetails[0].createdDate,
        type: 'text',
        display: true,
      },
      {
        label: 'Survey Completion Date',
        value: this.context.collaboration.data.collabDetails[0].dueDate,
        type: 'text',
        display: true,
      },
      {
        label: 'Do you want to use the scores from the Success Profile you are editing to fill in the job Analysis survey?',
        value: this.context.collaboration.data.isPrepopulatedJAScore ? 'Yes, pre-populate scores from this Success Profile.' : 'No',
        type: 'text',
        display: true,
      },
      {
        label: 'Level',
        value: this.context.collaboration.data.Level,
        type: 'text',
        display: true,
      },
      {
        label: 'Sub-Level',
        value: this.context.collaboration.data.SubLevel,
        type: 'text',
        display: true

      },
      {
        label: 'Function',
        value: this.context.collaboration.data.Function,
        type: 'text',
        display: true,
      },
      {
        label: 'Sub-Function',
        value: this.context.collaboration.data.SubFunction,
        type: 'text',
        display: true,
      },
      {
        label: 'Location',
        //API call needs to be done to get the 'location' value (Backend dependency)
        value: '',
        type: 'text',
        display: true,
      },

    ].filter((item) => item.display);
  }
  toTitleCase(str) {
    return str.replace(/\w\S*/g, function (txt) {
      return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
    });
  }

  navigateCollabStakeholder() {
    this.router.navigate(['/clients/sps-collaboration-stakeholder'], {
      relativeTo: this.route,
      queryParams: {
        collabId: this.context.collaboration.data.collabDetails[0].collabId,
      },
    });

  }
}

