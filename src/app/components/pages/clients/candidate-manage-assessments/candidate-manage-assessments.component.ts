import { AlertService, LanguagesService, ParticipantService } from '@/services';
import { Client, AssessmentStatusEnum} from '@/shared/models';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import * as _ from 'lodash';
import { forkJoin } from 'rxjs';

@Component({
  selector: 'app-candidate-manage-assessments',
  templateUrl: './candidate-manage-assessments.component.html',
  styleUrls: ['./candidate-manage-assessments.component.scss'],
})
export class CandidateManageAssessmentsComponent implements OnInit {
  public client: Client;
  public project: any;
  public participant: any;
  public AssessmentStatusEnum = AssessmentStatusEnum;

  public assessments = [];
  allLanguages = [];
  availablePercentages = [];
  selectedSp: any;

  constructor(
    private route: ActivatedRoute,
    private participantService: ParticipantService,
    private languagesService: LanguagesService,
    private alertService: AlertService
  ) {}

  ngOnInit() {
    this.route.data.subscribe(
      (resolved) => {
        this.client = resolved.client;
        this.project = resolved.project;
        this.participant = resolved.participant;
        this.allLanguages = this.languagesService.getLanguages();
        this.selectedSp =
          this.project.successProfiles && this.project.successProfiles.length
            ? this.route.snapshot.queryParams.selectedProjectId
              ? this.project.successProfiles.find(
                  (sp) =>
                    sp && sp.projectId.toString() ===
                    this.route.snapshot.queryParams.selectedProjectId
                )
              : this.project.successProfiles[0]
            : null;
        this.refreshAssessments();
        this.availablePercentages = Array.from(
          { length: 21 },
          (item, k) => k * 5
        );
      },
      (error) => {
        this.alertService.error(error.message);
      }
    );
  }

  onLanguageChange(assessment, locale) {
    assessment.selectedLocale = locale;
    if (assessment.selectedLocale === assessment.locale) {
      delete assessment.selectedLocale;
    }
  }

  get selectedProjectId() {
    return this.selectedSp ? this.selectedSp.projectId : this.project.projectId;
  }

  firstCapital(str: string): string {
    if (!str) return '';
    const strs = str.split('_');
    return strs.reduce((prev, curr) => {
      return `${prev} ${curr[0].toUpperCase() + curr.slice(1).toLowerCase()}`;
    }, '').trim();
  }

  getTime(time: number): string {
    const minutes = Math.floor(time / 60);
    const seconds = Math.round(time % 60);

    return `${minutes} min ${seconds !== 0 ? `${seconds} sec` : ''}`;
  }

  getTimeAllotedWithPercentage(assessment, percent) {
    const baseTime =
      (assessment.timeAllotedMinutes * 60) /
      (1 + assessment.additionalTimePercent / 100);
    return this.getTime(baseTime * (1 + percent / 100));
  }

  onSpChange(sp: any) {
    this.selectedSp = sp;
    this.refreshAssessments();
  }

  onPercentChange(assessment, percent) {
    assessment.newPercentage = percent;
  }

  getIconStyle(status: string): string[] {
    let icon = 'add';
    let colorClass = 'color-secondary--green-dark';
    switch (status) {
      case AssessmentStatusEnum.ABOVE_AVERAGE:
        icon = 'keyboard_arrow_up';
        colorClass = 'color-secondary--red';
        break;
      case AssessmentStatusEnum.BELOW_AVERAGE:
        icon = 'keyboard_arrow_down';
        colorClass = 'color-secondary--green-dark';
        break;
      case AssessmentStatusEnum.COMPLETED:
        icon = 'check_circle_outline';
        colorClass = 'color-secondary--green-dark';
        break;
      case AssessmentStatusEnum.LOCKED:
        icon = 'lock';
        colorClass = 'color-secondary--orange';
        break;
      case AssessmentStatusEnum.IN_PROGRESS:
        icon = 'av_timer';
        colorClass = 'color-secondary--yellow';
        break;
      case AssessmentStatusEnum.NOT_STARTED:
      case AssessmentStatusEnum.TERMINATED:
        icon = 'block';
        colorClass = 'color-secondary--red';
        break;
    }
    return [icon, colorClass];
  }

  get readyToResetSjts() {
    return this.assessments
      .filter((a) => a.readyToReset && a.type === 'sjt')
      .map((a) => a.kfasAssessmentId);
  }

  get readyToReset() {
    return this.assessments
      .filter((a) => a.readyToReset && a.type !== 'sjt')
      .map((a) => a.kfasAssessmentId);
  }

  get readyToReenableSjts() {
    return this.assessments
      .filter((a) => a.readyToReenable && a.type === 'sjt')
      .map((a) => a.kfasAssessmentId);
  }

  get readyToReenable() {
    return this.assessments
      .filter((a) => a.readyToReenable && a.type !== 'sjt')
      .map((a) => a.kfasAssessmentId);
  }

  get readyToUpdateLanguage() {
    return this.assessments
      .filter((a) => a.selectedLocale !== undefined)
      .map((a) => ({
        languageId: (
          this.allLanguages.find(
            (lang) => lang.locale === a.selectedLocale
          ) || { id: 0 }
        ).id,
        assessmentId: a.assessmentId,
      }));
  }

  get readyToUpdateTimeAlloted() {
    return this.assessments
      .filter((a) => a.newPercentage !== undefined)
      .map((a) => ({
        kfasAssessmentId: a.kfasAssessmentId,
        assessmentId: a.assessmentId,
        candidateId: this.participant.participantId,
        timePercentage: a.newPercentage,
      }));
  }

  refreshAssessments() {
    this.participantService
      .getAssessments(this.participant.participantId, this.selectedProjectId)
      .subscribe((result: any[]) => {
        this.assessments = result.map((a) => {
          a.completedDate = this.getCompletedDate(a.assessmentId);
          a.availableLocales.unshift(null); // 'Not selected' Value
          return a;
        });
      });
  }

  get projectSummary() {
    return this.participant.projects.find(
      (p) => p.projectId === this.selectedProjectId
    );
  }

  getDemographicsCompletedDate() {
    const date = !this.projectSummary
      ? null
      : this.projectSummary.demographicsCompletedDate;
    return date ? new Date(date) : '-';
  }

  getAllocationDate() {
    const date = !this.projectSummary ? null : this.projectSummary.addedDate;
    return date ? new Date(date) : '-';
  }

  getCompletedDate(assessmentId) {
    let date: Date = null;

    if (this.projectSummary) {
      const assessment = this.projectSummary.assessments
        .find(a => a.assessmentId === assessmentId);
      date = assessment && assessment.completed ? new Date(assessment.completed) : null;
    }

    return date ? date : '-';
  }

  apply() {
    const pid = this.selectedProjectId;
    const cid = this.participant.participantId;
    const queue = [];

    if (this.readyToReenable.length) {
      queue.push(
        this.participantService.reenableAssessments(
          cid,
          pid,
          this.readyToReenable
        )
      );
    }

    if (this.readyToReenableSjts.length) {
      queue.push(
        this.participantService.reenableSjtAssessments(
          cid,
          pid,
          this.readyToReenableSjts
        )
      );
    }

    if (this.readyToReset.length) {
      queue.push(
        this.participantService.resetAssessments(cid, pid, this.readyToReset)
      );
    }

    if (this.readyToResetSjts.length) {
      queue.push(
        this.participantService.resetSjtAssessments(
          cid,
          pid,
          this.readyToResetSjts
        )
      );
    }

    this.readyToUpdateLanguage.forEach((model) => {
      queue.push(this.participantService.updateLanguage(model));
    });

    this.readyToUpdateTimeAlloted.forEach((model) => {
      queue.push(this.participantService.updateTime(model));
    });

    forkJoin(queue).subscribe((...results) => {
      this.refreshAssessments();
    });
  }
}

export interface AssessmentAction {
  kfasAssessmentId: string;
  status: string;
  requiresReenable: boolean;
  canBeReset: boolean;
}

export interface Assessment {
  kfasAssessmentId: string;
  type: string;
  timeAllotedMinutes: number;
  status: string;
  timeTakenAverage: string;
  totalTimeTakenSeconds: number;
  readyToReenable: boolean;
  readyToReset: boolean;
  requiresReenable: boolean;
  canBeReset: boolean;
}
