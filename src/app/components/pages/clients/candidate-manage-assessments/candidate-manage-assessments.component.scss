@import "../../../../../styles/colors.scss";
@import "../../../../../styles/theme.scss";

$container-padding: 24px;
$label-width: 150px;
$label-width-long: 250px;
$label-width-short: 100px;
$value-padding: 10px;
$content-width: 1600px;
$side-margin: 36px 64px;

:host {
  flex: 1;
}

.content {
  padding: 0 $container-padding;
  background: white;
}

.row {
  display: flex;
  .col {
    flex: 50%;
  }
}

.group {
  display: flex;
}

.label {
  width: $label-width;
  padding: $value-padding;
  &.short {
    width: $label-width-short;
  }
  &.long {
    width: $label-width-long;
  }
}

.value {
  font-weight: bold;
  padding: $value-padding;
}

h3 {
  padding: $value-padding;
}

button.apply {
  cursor: pointer;
  margin-left: auto;
  display: inline-block;
  padding: 6px 24px;
  margin-right: 12px;

  white-space: nowrap;
  vertical-align: middle;

  font-weight: bold;
  letter-spacing: 0.5px;
  text-transform: uppercase;

  background: white;
  color: $primary--blue-medium;
  border: 1px solid $primary--blue-medium;

  &:hover {
    background: $primary--blue-medium;
    color: white;
  }
}

table {
  width: 100%;

  tr {
    border-bottom: 1px solid $primary--grey-light;

    th,
    td {
      height: 60px;
      vertical-align: middle;
      padding: $value-padding;
    }

    td {
      font-size: 14px;
    }

    th {
      color: $primary--grey-dark;
      font-weight: bold;
      text-transform: uppercase;
    }

    td.level {
      font-weight: bold;
    }
  }
}

.key {
  padding: $container-padding 0;
  display: flex;
  justify-content: stretch;
  align-items: center;

  span {
    margin-right: 6px;
  }
}

mat-icon {
  vertical-align: bottom;
}

.checkbox-span {
  text-align: center;
  float: left;
  display: block;
  line-height: 2;
  font-size: 12px;
}

.menu-button {
  width: 150px;
  color: $primary--blue-medium;
  .mat-select-arrow {
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid $primary--blue-medium;
    margin: 0 6px;
    display: inline-block;
    vertical-align: middle;
  }
}

::ng-deep .mat-menu-content {
  max-height: 300px;
  overflow: auto;

  /* width */
  &::-webkit-scrollbar {
    width: 8px;
  }

  /* Track */
  &::-webkit-scrollbar-track {
    background: $primary--grey-light;
  }

  /* Handle */
  &::-webkit-scrollbar-thumb {
    background: $primary--grey-medium;
  }

  /* Handle on hover */
  &::-webkit-scrollbar-thumb:hover {
    background: $primary--grey-dark;
  }
}

.bold {
  color: $primary--blue-medium;
  font-weight: bold;
  .mat-select-arrow {
    border-top: 5px solid $primary--blue-medium;
  }
}

.flex {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  padding: $container-padding;
}

.demographics-date {
  line-height: 1.5;
}

.sp-menu-button {
  margin-left: 24px;
  min-width: 270px;
  width: auto;
  background: white;
  margin-right: 48px;
  padding: 16px;
  border: thin solid rgba(145, 145, 145, 0.3);
  letter-spacing: 0.4px;
  color: $dark-primary-text;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .mat-select-arrow {
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid $dark-primary-text;
    margin: 0 6px;
    display: inline-block;
    vertical-align: middle;
  }
}
