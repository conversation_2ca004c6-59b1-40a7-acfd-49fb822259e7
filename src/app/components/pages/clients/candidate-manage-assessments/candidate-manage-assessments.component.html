<div class="page-content glass">
  <div class="flex">
    <ng-container *ngIf="project.successProfiles && project.successProfiles.length > 1">
      <p>Success Profile:</p>

      <mat-menu #spMenu="matMenu" class="menu">
        <button
          mat-menu-item
          *ngFor="let sp of project.successProfiles"
          (click)="onSpChange(sp)"
        >
          {{ sp.name }}
        </button>
      </mat-menu>

      <button class="sp-menu-button white-glass" [matMenuTriggerFor]="spMenu">
        {{ selectedSp && selectedSp.name }}
        <span class="mat-select-arrow"></span>
      </button>
    </ng-container>
    <div class="demographics-date" *ngIf="projectSummary">
      <div>
        Assessments allocated:
        <strong>{{ getAllocationDate() | date: "dd MMM yyyy, HH:mm:ss" }}</strong>
      </div>
      <div>
        Demographics completed:
        <strong>{{
          getDemographicsCompletedDate() | date: "dd MMM yyyy, HH:mm:ss"
        }}</strong>
      </div>
    </div>
  </div>

  <div class="content glass">
    <table>
      <thead>
        <tr>
          <th>Level Required</th>
          <th>Language</th>
          <th>Time Allotted</th>
          <th>Time Taken</th>
          <th>STATUS</th>
          <th>Completed Date</th>
          <th>Unlock</th>
          <th>Reset</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let assessment of assessments; let i = index">
          <td class="level">{{ firstCapital(assessment.type) }}</td>
          <td *ngIf="assessment.status === AssessmentStatusEnum.NOT_STARTED">
            <ng-container *ngIf="project.isKFAssess2">{{ "en-US" | language }}</ng-container>
            <ng-container *ngIf="!project.isKFAssess2">
              <mat-menu #languageMenu="matMenu" class="menu">
                <button mat-menu-item *ngFor="let locale of assessment.availableLocales"
                  (click)="onLanguageChange(assessment, locale)">
                  {{ locale || "Not selected" | language }}
                </button>
              </mat-menu>
            
              <a class="menu-button" [matMenuTriggerFor]="languageMenu" [class.bold]="assessment.selectedLocale !== undefined">
                {{
                assessment.selectedLocale === null
                ? "Not selected"
                : (assessment.selectedLocale ||
                assessment.locale ||
                "Not selected" | language)
                }}
                <span class="mat-select-arrow"></span>
              </a>
            </ng-container>
          </td>
          <td *ngIf="assessment.status !== AssessmentStatusEnum.NOT_STARTED">
            {{ assessment.locale || "Not selected" | language }}
          </td>
          <td *ngIf="assessment.isTimed && assessment.status !== AssessmentStatusEnum.COMPLETED">
            <mat-menu #percentMenu="matMenu" class="menu">
              <button
                mat-menu-item
                *ngFor="let item of availablePercentages"
                (click)="onPercentChange(assessment, item)"
              >
                (+{{ item }}%)
                {{ getTimeAllotedWithPercentage(assessment, item) }}
              </button>
            </mat-menu>

            <a
              class="menu-button"
              [matMenuTriggerFor]="percentMenu"
              [class.bold]="assessment.newPercentage !== undefined"
            >
              {{
                getTimeAllotedWithPercentage(
                  assessment,
                  assessment.newPercentage === undefined
                    ? assessment.additionalTimePercent
                    : assessment.newPercentage
                )
              }}
              <span *ngIf="assessment.isTimed">
                (+{{
                  assessment.newPercentage === undefined
                    ? assessment.additionalTimePercent
                    : assessment.newPercentage
                }}%)
              </span>

              <span class="mat-select-arrow"></span>
            </a>
          </td>
          <td *ngIf="!assessment.isTimed || assessment.status === AssessmentStatusEnum.COMPLETED">
            {{ getTime(assessment.timeAllotedMinutes * 60) }}
            <span *ngIf="assessment.isTimed"
              >(+{{ assessment.additionalTimePercent }}%)</span
            >
          </td>
          <td>
            {{
              assessment.status === AssessmentStatusEnum.COMPLETED
                ? getTime(assessment.totalTimeTakenSeconds)
                : ""
            }}
            <span
              *ngIf="
                assessment.timeTakenAverage &&
                assessment.timeTakenAverage !== 'AVERAGE'
              "
            >
              <mat-icon
                [ngClass]="getIconStyle(assessment.timeTakenAverage)[1]"
                >{{ getIconStyle(assessment.timeTakenAverage)[0] }}</mat-icon
              >
            </span>
          </td>
          <td>
            <mat-icon [ngClass]="getIconStyle(assessment.status)[1]">
              {{ getIconStyle(assessment.status)[0] }}
            </mat-icon>
            {{ firstCapital(assessment.status) }}
          </td>
          <td>
            {{
              assessment.status === AssessmentStatusEnum.COMPLETED
                ? (assessment.completedDate | date: "dd MMM yyyy, HH:mm:ss")
                : ""
            }}
          </td>
          <td>
            <span class="checkbox-span">
              <mat-checkbox
                *ngIf="assessment.requiresReenable"
                color="primary"
                [(ngModel)]="assessment.readyToReenable"
              ></mat-checkbox>
              <br />
              <span class="grey">Unlocks: {{ assessment.reenables }}</span>
            </span>
          </td>
          <td>
            <span class="checkbox-span">
              <mat-checkbox
                *ngIf="assessment.canBeReset"
                color="primary"
                [(ngModel)]="assessment.readyToReset"
              ></mat-checkbox>
              <br />
              <span class="grey">Resets: {{ assessment.resets }}</span>
            </span>
          </td>
        </tr>
      </tbody>
    </table>
    <div class="key">
      <span>
        <mat-icon class="color-secondary--green-dark"
          >keyboard_arrow_up</mat-icon
        >
        Above Average
      </span>
      <span>
        <mat-icon class="color-secondary--red">keyboard_arrow_down</mat-icon>
        Below Average
      </span>
      <button mat-button class="apply" (click)="apply()">Apply</button>
    </div>
  </div>
</div>
