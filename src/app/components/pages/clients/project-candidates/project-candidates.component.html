<div class="page-content">
  <div class="table-actions-panel">
    <mat-menu #bulkActionsAllMenu="matMenu" class="menu">
      <button
        mat-menu-item
        (click)="showReportsModal(false)"
        [disabled]="!hasCompletedParticipants()"
      >
        (Re)generate Reports
      </button>
      <button
        mat-menu-item
        (click)="showExportsModal(false)"
        [disabled]="!hasCompletedParticipants()"
      >
        Export Reports
      </button>
    </mat-menu>

    <button class="menu-button glass" [matMenuTriggerFor]="bulkActionsAllMenu">
      Bulk actions (all)
      <span class="mat-select-arrow"></span>
    </button>

    <mat-menu #bulkActionsSelectedMenu="matMenu" class="menu">
      <button mat-menu-item (click)="showReportsModal(true)">
        (Re)generate Reports
      </button>
      <button mat-menu-item (click)="showExportsModal(true)">
        Export Reports
      </button>
      <button mat-menu-item 
        *ngIf="project.productType === ProductType.SELECT" 
        (click)="updateHiredStatus(2)">
        <span>Mark as 'Hired'</span>
      </button>
      <button mat-menu-item 
        *ngIf="project.productType === ProductType.SELECT" 
        (click)="updateHiredStatus(1)">
        <span>Mark as 'Not Hired'</span>
      </button>
      <button mat-menu-item 
        *ngIf="project.productType === ProductType.SELECT && canClearHiredStatus()" 
        (click)="updateHiredStatus(0)">
        Clear Hiring Status
      </button>
    </mat-menu>

    <button
      *ngIf="selection && selection.length"
      class="menu-button glass"
      [matMenuTriggerFor]="bulkActionsSelectedMenu"
    >
      Bulk actions (selected)
      <span class="mat-select-arrow"></span>
    </button>

    <mat-icon class="input-icon">search</mat-icon>
    <input
      type="search"
      class="glass with-icon"
      [(ngModel)]="searchKey"
      placeholder="Search participants"
      (input)="onSearch($event.target.value)"
    />

    <kf-select
      class="glass"
      *ngFor="let fb of filterByList"
      [placeholder]="fb.name"
      [options]="fb.options"
      [selection]="fb.name"
      (selectionChange)="onFilterChange(fb, $event)"
    ></kf-select>

    <div
      class="right-align"
      *ngIf="project.successProfiles && project.successProfiles.length > 1"
    >
      <mat-menu #spMenu="matMenu" class="menu">
        <button
          mat-menu-item
          *ngFor="let sp of project.successProfiles"
          (click)="changeSuccessProfile(sp)"
        >
          {{ sp.name }}
        </button>
      </mat-menu>

      <button class="glass menu-button" [matMenuTriggerFor]="spMenu">
        <strong> {{ selectedSp.name }} </strong>
        <span class="mat-select-arrow"></span>
      </button>
    </div>
  </div>

  <div class="table-container">
    <kf-table
      [data]="participants || []"
      [columns]="columns"
      [customPaging]="customPaging"
      [actions]="actions"
      (paginatorChanged)="onPageChange($event)"
    ></kf-table>
  </div>
</div>

<ng-template #PARTICIPANT_STATUS_TPL let-cell>
  <div class="participant-status" [ngSwitch]="cell.element.displayStatus">
    <div *ngSwitchCase="'HIRED'">
      <mat-icon class="green">thumb_up_off_alt</mat-icon>
      <span>Hired</span>
    </div>
    <div *ngSwitchCase="'NOT_HIRED'">
      <mat-icon class="red">thumb_down_off_alt</mat-icon>
      <span>Not Hired</span>
    </div>
    <div *ngSwitchCase="'COMPLETED'">
      <mat-icon class="green">check_circle_outline</mat-icon>
      <span>Completed</span>
    </div>
    <div *ngSwitchCase="'NOT_STARTED'">
      <mat-icon class="red">block</mat-icon>
      <span>Not Started</span>
    </div>
    <div *ngSwitchCase="'IN_PROGRESS'">
      <mat-icon class="orange">schedule</mat-icon>
      <span>In Progress</span>
    </div>
    <div *ngSwitchCase="'NOT_INVITED'">
      <mat-icon class="green">add</mat-icon>
      <span>Not Invited</span>
    </div>
    <div *ngSwitchCase="'LOCKED'">
      <mat-icon class="red">lock</mat-icon>
      <span>Locked</span>
    </div>
    <div *ngSwitchCase="'TERMINATED_FACE_VERIFICATION_FAILED'">
      <mat-icon class="red">block</mat-icon>
      <span>Terminated – Face Verification Failed</span>
    </div>
    <div *ngSwitchCase="'IMAGE_NOT_AVAILABLE'">
      <mat-icon class="red">block</mat-icon>
      <span>Image Not Available</span>
    </div>
    <div *ngSwitchDefault>
      <span>
        {{ cell.element.displayStatus }}
      </span>
    </div>
  </div>
</ng-template>

<ng-template #PARTICIPANT_PROCTORING_TPL let-cell>
  <div class="proctoring-score" [ngSwitch]="cell.element.proctoringOverallScore">
    <div *ngSwitchCase="'Undefined'" class="pill pill-default">
      <span>-</span>
    </div>
    <div *ngSwitchCase="'Low'" class="pill pill-low">
      <span>Low</span>
    </div>
    <div *ngSwitchCase="'Medium'" class="pill pill-medium">
      <span>Medium</span>
    </div>
    <div *ngSwitchCase="'High'" class="pill pill-high">
      <span>High</span>
    </div>
  </div>
</ng-template>