@import "../../../../../styles/colors.scss";
@import "../../../../../styles/theme.scss";
@import "../../../../../styles/typography.scss";

:host {
  flex: 1;

  .mat-table {
    .mat-column-select {
      min-width: 75px;
      max-width: 75px;
    }

    .mat-row {
      &.expanded {
        background: $primary--grey-light;
      }
      transition: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    }

    .detail-row {
      background: $primary--grey-light;
      display: flex;
      align-items: stretch;
      justify-content: space-around;

      div {
        flex: 1 1 auto;

        &:not(:last-child) {
          border-right: solid thin $primary--grey-medium;
        }

        display: flex;
        flex-direction: column;
        justify-content: stretch;
        align-items: center;

        h5 {
          height: 24px;
          margin-bottom: 24px;
          font-weight: 600;
          color: $primary--blue;
          text-transform: uppercase;
        }

        li:not(:last-child) {
          padding-bottom: 12px;
        }
      }
    }

    .mat-header-row {
      z-index: 0;
    }
  }

  .table-actions-panel {
    display: flex;
    flex-direction: row;
    justify-content: stretch;
    align-items: center;
    user-select: none;
    margin-bottom: 16px;

    .menu-button {
      min-width: 200px;
      background: white;
      padding: 12px 16px;
      border: thin solid rgba(145, 145, 145, 0.3);
      letter-spacing: 0.4px;
      color: $dark-primary-text;
      font: unquote($proxima-font);
      display: flex;
      justify-content: space-between;
      align-items: center;

      &:not(:last-child) {
        margin-right: 12px;
      }
    }

    input.with-icon {
      flex-grow: 1;
      padding: 13px;
      padding-left: 48px;
      margin-right: 12px;
    }

    .input-icon {
      z-index: 1;
      width: 0;
      transform: translateX(12px);
      color: #007da4;
    }
  }
}

.mat-checkbox-layout {
  width: 100%;

  .mat-checkbox-label {
    display: inline-block;
    width: 100%;
  }
}

.right-align {
  margin-left: auto;

  display: inline-flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;

  > span {
    margin-right: 12px;
    margin-left: 12px;
  }
}

::ng-deep .menu {
  width: 250px;
}

.participant-status {
  .green {
    color: $secondary--green;
  }
  .red {
    color: $secondary--red;
  }
  .orange {
    color: $secondary--orange;
  }
  .mat-icon {
    font-size: 18px;
  }
}

.proctoring-score {
  .pill {
    display: inline-block;
    padding: 2px 10px;
    border-radius: 6px;
    color: #000;
    font-weight: 500;
    text-align: center;
    min-width: 40px;
    box-sizing: border-box;
  }

  .pill-default {
    background-color: none;
    color: #7a4f1d;
  }

  .pill-low {
    background-color: #FF9675;
    color: #7a4f1d;
  }

  .pill-medium {
    background-color: #FEE0B6;
    color: #7a4f1d;
  }

  .pill-high {
    background-color: #78D363;
    color: #1b4d1b;
  }
}
