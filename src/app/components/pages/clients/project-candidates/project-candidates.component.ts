import { environment } from "@/shared/environments/environment";
import {
  KFTableAction,
  KFTableColumn,
} from '@/components/controls/kf-table/kf-table.component';
import { ReportLanguagesDialogComponent, ReportDownloadLimitComponent, ParticipantProctoringInfoComponent } from '@/components/dialogs';
import {
  AlertService,
  LanguagesService,
  ProjectService,
  SpinnerService,
} from '@/services';
import { ParticipantService } from '@/services/participant.service';
import {
  Client,
  FilterOption,
  SearchOptionData,
} from '@/shared/models';
import {
  ParticipantInfo,
  SearchProjectParticipantsData,
  ParticipantStatusType,
  UpdateHiredStatus,
  ParticipantHiredType,
  ProctoringResult
} from '@/shared/models/projects/participants';
import {
  animate,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import { SelectionModel } from '@angular/cdk/collections';
import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material';
import { ActivatedRoute, Router } from '@angular/router';
import saveAs = require('file-saver');
import * as JSZip from 'jszip';
import { EMPTY, Observable, Subject, forkJoin, of } from 'rxjs';
import { catchError, concatMap, debounceTime, distinctUntilChanged, filter, finalize } from 'rxjs/operators';
import { ProjectProductType } from '@/shared/models/projects/project';

/**
 * Project participants component.
 */
@Component({
  selector: 'app-project-candidates',
  templateUrl: './project-candidates.component.html',
  styleUrls: ['./project-candidates.component.scss'],
  animations: [
    trigger('detailExpand', [
      state(
        'void',
        style({ height: '0px', minHeight: '0', visibility: 'hidden' })
      ),
      state('*', style({ height: '*', visibility: 'visible' })),
      transition('void <=> *', animate('150ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
    ]),
  ],
})

export class ProjectCandidatesComponent implements OnInit {
  @ViewChild('PARTICIPANT_STATUS_TPL') PARTICIPANT_STATUS_TPL: TemplateRef<any>;
  @ViewChild('PARTICIPANT_PROCTORING_TPL') PARTICIPANT_PROCTORING_TPL: TemplateRef<any>;
  
  client: Client;
  project: any;
  actionMethod: string;
  customPaging: any;

  filterBy: FilterOption;
  filterByList: FilterOption[] = [];
  filterValues: string[];
  filterValueList: FilterOption[];

  selectedFilters = {};

  loading = false;
  data: SearchProjectParticipantsData;
  participants: ParticipantInfo[];
  selectedParticipants = new SelectionModel<ParticipantInfo>(true, []);
  reportDownloadLimit: number = +environment.REPORT_DOWNLOAD_LIMIT;
  
  actions: KFTableAction<ParticipantInfo>[] = [
    {
      label: 'Report Status',
      icon: 'file_download',
      css: '',
      displayCondition: (t: ParticipantInfo) =>
        t.status.toLowerCase() === 'completed',
      click: (t: ParticipantInfo) => {
        this.showReports(t);
      },
    },
    {
      label: 'Manage Assessments',
      icon: 'assessment',
      css: '',
      click: (t: ParticipantInfo) => {
        this.router.navigate(['/clients/candidate-manage-assessments'], {
          queryParams: {
            projectId: this.project.projectId,
            selectedProjectId: this.selectedProjectId,
            candidateId: t.participantId,
            clientId: this.client.id,
          },
        });
      },
      url: (t: ParticipantInfo) =>
        this.router
          .createUrlTree(['/clients/candidate-manage-assessments'], {
            queryParams: {
              projectId: this.project.projectId,
              selectedProjectId: this.selectedProjectId,
              candidateId: t.participantId,
              clientId: this.client.id,
            },
          })
          .toString(),
    },
    {
      label: 'Proctoring Results',
      icon: 'format_list_bulleted',
      css: '',
      click: (t: ParticipantInfo) => {
        this.proctoringResult(t);
      },
    },
  ];

  columns: KFTableColumn<ParticipantInfo>[] = [
    {
      type: 'textCapitalized',
      name: 'displayName',
      label: 'Name',
      click: (t: ParticipantInfo) => {
        this.router.navigate(['/clients/candidate-details'], {
          queryParams: {
            candidateId: t.participantId,
            clientId: this.client.id,
          },
        });
      },
      url: (t: ParticipantInfo) =>
        this.router
          .createUrlTree(['/clients/candidate-details'], {
            queryParams: {
              candidateId: t.participantId,
              clientId: this.client.id,
            },
          })
          .toString(),
    },
    { type: 'text', name: 'email', label: 'email' },
    { type: 'text', name: 'displayStatus', label: 'status', template: null },
    { type: 'date', name: 'completed', label: 'completed', dateFormat:'local' },
    { type: 'text', name: 'iC2EmployeeId', label: 'IC2 Employee Id' },
    { type: 'text', name: 'proctoringOverallScore', label: 'Overall Proctoring Score', template: null, width: '250px' },
    { type: 'rowOptions', name: 'actions', label: 'Actions' },
  ]
  
  searchOptions: SearchOptionData = {
    filterBy: 'STATUS',
    filterValues: '',
    searchColumn: 'NAME',
    searchString: '',
    sortBy: 'asc',
    sortColumn: 'STATUS',
    pageIndex: 1,
    pageSize: 10
  };
  filter = {};
  searchKey = '';
  private searchTerms = new Subject<string>();
  selection: ParticipantInfo[] = [];
  _debounced: any;
  selectedSp: any;
  readonly ProductType = ProjectProductType;

  constructor(
    private alertService: AlertService,
    private languageService: LanguagesService,
    private participantService: ParticipantService,
    private projectService: ProjectService,
    private route: ActivatedRoute,
    private router: Router,
    private spinnerService: SpinnerService,
    public dialog: MatDialog
  ) {}

  ngOnInit() {
    this.route.data.subscribe((resolved) => {
      // Assigning template condidtionally
      this.columns.forEach((column) => {
        if (column.name === 'displayStatus') {
          column.template = this.PARTICIPANT_STATUS_TPL;
        }
        if (column.name === 'proctoringOverallScore') {
          column.template = this.PARTICIPANT_PROCTORING_TPL;
        }
      });

      this.client = resolved.client;
      this.project = resolved.project;
      this.selectedSp =
        this.project.successProfiles && this.project.successProfiles.length
          ? this.route.snapshot.queryParams.selectedProjectId
            ? this.project.successProfiles.find(
                (sp) =>
                  sp && sp.projectId.toString() ===
                  this.route.snapshot.queryParams.selectedProjectId
              )
            : this.project.successProfiles[0]
          : null;

      const allFilters = resolved.searchMetadata.data.metadata[0].searchOn;

      // Setting up filters
      this.filterByList = this.setupFilters(allFilters);
      
      this.refreshTable();
    });

    this.customPaging = {
      pageIndex: 1,
      pageSize: 10,
      totalPages: null,
      totalResultRecords: null,
    };

    // filter project with keyword
    this.searchTerms.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      filter(term => (term.length > 2 || term.length === 0))
    ).subscribe(term => {
      this.searchOptions.searchString = term;
      this.refreshTable();
    });
  }

  setupFilters(availableFilters: FilterOption[]): FilterOption[] {
    const filters = [];

    // Adding Status filter options
    filters.push(
      availableFilters.find((filter: FilterOption) => filter.name === 'STATUS')
    );

    // Adding Hired filter for Select product type only
    if (this.project.productType === this.ProductType.SELECT) {
      const hiredFilter: FilterOption = availableFilters.find(
        (filter: FilterOption) => filter.name === 'HIRED'
      );
      
      if (hiredFilter) {
        filters.push(this.resolveHiredFilterValueText(hiredFilter));
      }
    }

    return filters;
  }

  resolveHiredFilterValueText(filter: FilterOption): FilterOption {
    if (filter.options) {
      filter.options.map(option => {
        switch (option.value) {
          case 'Hired':
            option.value = 'Hired';
            break;
          case 'NotHired':
            option.value = 'Not Hired';
            break;
          case 'Undefined':
            option.value = 'Not Set';
            break;
        }
      });
    }

    return filter;
  }

  selectFilterBy(selectedFilterBy: FilterOption) {
    this.filterBy = selectedFilterBy;
    this.searchOptions.filterBy = '';
    this.searchOptions.filterValues = '';
    this.refreshTable();
  }

  onSearch(term: string): void {
    this.searchTerms.next(term);
  }

  onPageChange(event) {
    if (
      this.customPaging.pageSize !== event.pageSize ||
      this.customPaging.pageIndex !== event.pageIndex + 1
    ) {
      this.customPaging.pageSize = event.pageSize;
      this.customPaging.pageIndex = event.pageIndex + 1;
      this.refreshTable(this.customPaging.pageIndex, this.customPaging.pageSize)
    }
  }

  refreshTable(pageIndex?: number, pageSize?: number) {
    const resolvedPageIndex = pageIndex === undefined ? 1 : pageIndex;
    const resolvedPageSize = pageSize === undefined ? 10 : pageSize;
    this.searchOptions.pageIndex = resolvedPageIndex;
    this.searchOptions.pageSize = resolvedPageSize;
    this.spinnerService.activate();
    this.selectedParticipants.clear();
    this.projectService
      .getParticipants(this.selectedProjectId, this.searchOptions)
      .subscribe((result) => {
        this.data = result;
        this.participants = this.evaluateParticipants(result.data, this.project);
        this.customPaging = result.paging;
        this.selection = [];
        this.spinnerService.deactivate();
      });
  }

  showExportsModal(forSelected: boolean) {
  this.spinnerService.activate(); // Activate the spinner
  const reportStatuses = forSelected
    ? this.selection
    : this.participants.filter((p) => p.status.toLowerCase() === 'completed');

  forkJoin(
    reportStatuses.map((p) => {
      return this.projectService.getReportStatus(
        p.participantId,
        this.selectedProjectId
      ).pipe(
        catchError((error) => {
          console.error(`Error getting report status for participant ${p.participantId}: ${error}`);
          return EMPTY; // Continue with the next item on error
        })
      );
    })
  ).pipe(
    concatMap((statuses) => {
      this.spinnerService.deactivate();
      return this.handleExportStatuses(statuses);
    }),
    finalize(() => {
      this.spinnerService.deactivate(); 
    })
  ).subscribe(() => {
  });
}

handleExportStatuses(statuses: any[]): Observable<void> {
  const downloadData = statuses.reduce((prev, curr: any) => {
    const readyToDownload = curr.reduce((_prev, _curr) => {
      const showsLink = _curr.reportInfos
        .filter((r) => r.showDownLoadLink)
        .map((r) => ({
          downloadLink: r.downloadLink,
          languageCode: r.languageCode,
          fileName: r.fileName,
          candidate: this.participants.find(
            (p) =>
              p.participantId.toString() === r.downloadLink.split('/')[0]
          ),
        }));
      return _prev.concat(...showsLink);
    }, []);
    return prev.concat(...readyToDownload);
  }, []);
  const distinctLocales = downloadData.filter(
    (x, index) =>
      index ===
      downloadData.findIndex((d) => d.languageCode === x.languageCode)
  );
  this.dialog
    .open(ReportLanguagesDialogComponent, {
      width: '650px',
      data: {
        action: 'download',
        languages: distinctLocales,
      },
    })
    .afterClosed()
    .subscribe((response: any = []) => {
      const reportInfos = downloadData.filter((d) =>
        response.find((r) => {
          return (
            this.languageService.getLanguageByLocale(r.locale).id ===
            this.languageService.getLanguageByLocale(d.languageCode).id
          );
        })
      );
      const reportsToDownload = reportInfos.map((r) => ({
        request: this.projectService.downloadReport(r.downloadLink, r.fileName),
        downloadLink: r.downloadLink,
        candidateName: r.candidate.displayName,
        fileName: r.fileName
      }));

      const reportCount = reportsToDownload.length;

      if (reportCount > this.reportDownloadLimit) {
        this.showReportDownloadLimitModal(reportCount)
        return;
      }
      this.downloadReports(reportsToDownload);
    });

  // Return an observable to satisfy concatMap
  return of(void 0);
}

  showReportDownloadLimitModal(reportCount: number) {
    this.dialog.open(ReportDownloadLimitComponent, {
      width: '650px',
      data: {
        reportDownloadLimit: this.reportDownloadLimit,
        reportCount: reportCount
      }
    })  
  }

  showReportsModal(forSelected: boolean) {
    this.projectService
      .getProjectReports(this.selectedProjectId)
      .subscribe((reports: any) => {
        const distinctLocales = reports
          .reduce((prev, curr) => prev.concat(curr.languages), [])
          .filter((item, index, arr) => arr.indexOf(item) === index)
          .map((locale) => ({
            languageCode: locale,
            languageName: this.languageService.getLanguageNameByLocale(locale),
          }));

        this.dialog
          .open(ReportLanguagesDialogComponent, {
            width: '650px',
            data: {
              action: 'download',
              languages: distinctLocales,
            },
          })
          .afterClosed()
          .subscribe((langs: any) => {
            if (langs && langs.length) {
              const langIds = langs.filter((l) => l).map((l) => l.id);
              if (forSelected) {
                this.regenerateSelected(langIds);
              } else {
                this.regenerateAll(langIds);
              }
            }
          });
      });
  }

  downloadReports(reportRequests) {
    this.spinnerService.activate();
    forkJoin(
      reportRequests.map((rr) =>
        rr.request.pipe(
          catchError((err) => {
            return of(err.status);
          })
        )
      )
    ).subscribe(
      (reports: (string | Blob)[]) => {
        const zip = new JSZip();
        const name = `bulkExport ${this.project.name}`;

        reports.forEach((report, i) => {
          const fileName = reportRequests[i].fileName;
          zip.file(
            fileName,
            report
          );
        });

        zip
          .generateAsync({ type: 'blob' })
          .then((content) => {
            this.spinnerService.deactivate();
            if (content) {
              saveAs(content, name.replace(/[^a-zA-Z0-9 \[\]\(\)_-]/g, ''));
            }
          })
          .catch((error) => {
            this.spinnerService.deactivate();
            this.alertService.error(
              'An error occurred during the building of zip archive'
            );
          });
      },
      (error) => {
        this.spinnerService.deactivate();
        this.alertService.error(
          'An error occurred during the downloading reports'
        );
      }
    );
  }

  hasCompletedParticipants() {
    return (this.participants || []).find(
      (pi) => pi.status.toLowerCase() === 'completed'
    );
  }

  selectableOption(pi: ParticipantInfo) {
    return pi.status.toLowerCase() === 'completed';
  }

  public showReports(participant: ParticipantInfo) {
    this.router.navigate(['clients/candidate-report-status'], {
      queryParams: {
        clientId: this.client.id,
        selectedProjectId: this.selectedProjectId,
        projectId: this.project.projectId,
        candidateId: participant.participantId,
      },
    });
  }
  
  proctoringResult(participant: ParticipantInfo) {
    this.spinnerService.activate();
    this.participantService
      .getParticipantProctoringResults(
        participant.participantId,
        this.project.projectId
      )
      .subscribe({
        next: (pResult: ProctoringResult) => {
          this.spinnerService.deactivate();
          const dialogRef = this.dialog.open(ParticipantProctoringInfoComponent, {
            width: "65%",
            data: { proctoringResult: pResult, participant: participant, projectId: this.project.projectId },
          });
        },
        error: (err) => {
          this.spinnerService.deactivate();
          console.error("Error fetching proctoring results:", err);
          this.alertService.error("Error fetching proctoring results");
        },
      });
  }

  selectionChanged(selection: ParticipantInfo[]) {
    this.selection = selection;
  }

  sortData(sortEvent) {
    this.searchOptions.sortColumn = sortEvent.active;
    this.searchOptions.sortBy = sortEvent.direction || 'asc';

    this.refreshTable();
  }

  debouncedFilterParticipants(searchKey: string, ms?: number) {
    clearTimeout(this._debounced);
    this._debounced = setTimeout(() => {
      this.filterParticipants(searchKey);
    }, ms || 500);
  }

  filterParticipants(searchKey: string) {
    this.searchOptions.searchString = searchKey;
    this.refreshTable();
  }

  toggleFilterValues(filterValue: FilterOption) {
    const existingIndex = this.filterValues.indexOf(filterValue.name);

    if (existingIndex < 0) {
      this.filterValues.push(filterValue.name);
    } else {
      this.filterValues.splice(existingIndex, 1);
    }

    this.searchOptions.filterValues = this.filterValues.join(',');
    this.refreshTable();
  }

  pageChanged($event) {
    this.searchOptions.pageIndex = $event.pageIndex + 1;
    this.searchOptions.pageSize = $event.pageSize;

    this.refreshTable();
  }

  isOptionChecked(filterValue: FilterOption) {
    const existingIndex = this.filterValues.indexOf(filterValue.name);
    return existingIndex >= 0;
  }

  regenerateSelected(langIds) {
    this.projectService
      .regenerateAllReports(
        this.selectedProjectId,
        this.selection.map((p) => p.participantId),
        langIds,
        false
      )
      .subscribe(
        (result) => {
          this.alertService.success(result);
          this.refreshTable();
        },
        (error) => {
          this.alertService.error(error.message);
        }
      );
  }

  regenerateAll(langIds) {
    this.projectService
      .regenerateAllReports(this.selectedProjectId, [], langIds, true)
      .subscribe(
        (result) => {
          this.alertService.success(result);
          this.refreshTable();
        },
        (error) => {
          this.alertService.error(error.message);
        }
      );
  }

  /** Updates this.filter with a certain value */
  onFilterChange(option, values) {
    this.selectedFilters[option.name] = values;

    const filterKeys = Object.keys(this.selectedFilters).filter(
      (key) => this.selectedFilters[key] && this.selectedFilters[key].length
    );

    this.searchOptions.filterBy = filterKeys.join('|');
    this.searchOptions.filterValues = filterKeys
      .map((key) => this.selectedFilters[key].join(';'))
      .join('|');

    this.refreshTable();
  }

  changeSuccessProfile(sp) {
    this.selectedSp = sp;
    this.refreshTable();
  }

  get selectedProjectId() {
    return this.selectedSp ? this.selectedSp.projectId : this.project.projectId;
  }

  determineParticipantStatus(participant: ParticipantInfo, project: any): string {
    const { status, hired } = participant;

    const participantHired = (hired && hired !== ParticipantStatusType.UNDEFINED);
    const participantCompleted = status === ParticipantStatusType.COMPLETED;
    const isSelectProject = project.productType === ProjectProductType.SELECT;

    return participantHired && participantCompleted && isSelectProject ? hired : status;
  };

  evaluateParticipants(participants: ParticipantInfo[], project: any): ParticipantInfo[] {
    participants.map(participant => {
      participant.displayStatus = this.determineParticipantStatus(participant, project);
    });

    return participants;
  }

  canClearHiredStatus(): boolean {
    return this.selection
          .map((participant) => participant.hired !== ParticipantStatusType.UNDEFINED)
          .some((isHiredStatusAvailable) => isHiredStatusAvailable);
  }

  updateHiredStatus(hired: ParticipantHiredType) {
    const participantIds = this.selection.map(item => item.participantId);
    const successProfileId = this.selectedSp.id;

    const data: UpdateHiredStatus = { 
      projectId: this.selectedProjectId,
      successProfileId, 
      participantIds, 
      hired, 
    }

    this.participantService
      .updateHiredStatus(data, this.client.id)
      .subscribe(
        () => {
          this.refreshTable();
          this.alertService.success("Hired Status was successfully updated.");
        },
        (error) => {
          this.alertService.error(error.message);
        }
      );
  }

}
