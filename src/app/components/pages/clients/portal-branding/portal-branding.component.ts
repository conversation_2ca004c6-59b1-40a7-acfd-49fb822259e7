import { SpinnerService } from '@/services';
import { BrandingService } from '@/services/branding.service';
import {
  Client,
  PortalBrandingSettings,
  PortalBrandingSettingsData,
} from '@/shared/models';
import { ReportAsset } from '@/shared/models/configurable-reports/ReportAsset';
import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import * as _ from 'lodash';

@Component({
  selector: 'app-portal-branding',
  styleUrls: ['./portal-branding.component.scss'],
  templateUrl: './portal-branding.component.html',
})
export class PortalBrandingComponent implements OnInit {
  get currentState(): PortalBrandingSettingsData {
    return _.cloneDeep(this.branding.settings);
  }

  set currentState(settings: PortalBrandingSettingsData) {
    this.branding.settings = _.cloneDeep(settings);
    this.dataChange.emit(this.branding);
  }

  @Output() public dataChange = new EventEmitter();
  @Output() public assetsChange = new EventEmitter();

  public isMobilePreview = false;

  public loading: boolean;
  public views: {
    type: 'general' | 'signin' | 'dashboard' | 'manifests';
    name: string;
  }[] = [
    { type: 'general', name: 'General' },
    { type: 'signin', name: 'Participant Sign-In' },
    { type: 'dashboard', name: 'Participant Dashboard' },
    { type: 'manifests', name: 'Assessment Customizations (advanced)' },
  ];
  public selectedView: any;

  public savedForm: PortalBrandingSettingsData;
  public branding: PortalBrandingSettings;
  public assets: ReportAsset[];
  client: Client;
  private initialVersion: PortalBrandingSettingsData;
  private stateIndex = -1;
  private history = [];

  constructor(
    private brandingService: BrandingService,
    private spinnerService: SpinnerService,
    private route: ActivatedRoute
  ) {}

  public ngOnInit() {
    this.route.data.subscribe((resolved) => {
      Object.assign(this, resolved);
      this.branding = resolved.branding;
      this.client = resolved.client;
      this.assets = resolved.assets;

      if (this.branding.interfaceBrandingId === 0) {
        this.currentState = this.getDefaultForm();
      }
      this.initialVersion = this.currentState;
      this.selectedView = this.views[0];
      this.save();
    });
  }

  public save() {
    this.history[++this.stateIndex] = {
      state: _.cloneDeep(this.currentState),
      view: _.cloneDeep(this.selectedView),
    };
    this.history.splice(this.stateIndex + 1);
  }

  public undo() {
    this.stateIndex = Math.max(this.stateIndex - 1, 0);
    this.currentState = this.history[this.stateIndex].state;
    this.selectedView = this.history[this.stateIndex].view;
  }

  public redo() {
    this.stateIndex = Math.min(this.stateIndex + 1, this.history.length - 1);
    this.currentState = this.history[this.stateIndex].state;
    this.selectedView = this.history[this.stateIndex].view;
  }

  public reset() {
    this.currentState = this.initialVersion;
    this.save();
  }

  public getDefaultForm() {
    const settings = _.cloneDeep(this.currentState);
    Object.assign(settings, {
      general: {
        logoImageUrl: '',
        headerBarColour: '#ffffff',
        headerBarColourLeft: '',
        headerBarColourRight: '',
        headerBarUseGradient: false,
        headerBrandBarColour: '#f2f2f2',
        headerBrandBarColourLeft: '',
        headerBrandBarColourRight: '',
        headerBrandBarUseGradient: false,
        footerBrandBarColour: '#106550',
        buttonBackgroundColour: '#007da4',
        buttonHoverColour: '#005184',
        buttonTextColour: '#ffffff',
      },
      signIn: {
        desktopImageUrl: '',
        mobileImageUrl: '',
        customManifest: null,
      },
      dashboard: {
        heroImageUrl: '',
        hamburgerTextColour: '#3a3a3a',
        progressBarColour: '#007da4',
        customManifest: null,
        assessments: settings.dashboard.assessments.map((item) => {
          item.url = '';
          return item;
        }),
      },
      assessmentManifests: settings.assessmentManifests.map((item) => {
        item.manifest = null;
        return item;
      }),
    });
    return settings;
  }

  public clean() {
    this.currentState = this.getDefaultForm();
    this.save();
  }

  public confirmSettings() {
    this.spinnerService.activate();
    this.brandingService
      .updatePortalSettings(this.branding)
      .subscribe(() => {
        this.spinnerService.deactivate();
        this.initialVersion = this.currentState;
      });
  }

  public preview() {
    this.brandingService
      .updatePortalSettings(this.branding)
      .subscribe((model) => {
        this.branding.interfaceBrandingId = model.id;
        this.branding.name = model.name;
        this.branding.isNewBranding = false;

        const url =
          (this.selectedView.type === 'general' || this.selectedView.type === 'signin')
            ? this.brandingService.getSigninPreviewPortalUrl(model)
            : this.brandingService.getDashboardPreviewPortalUrl(model);

        window.open(url, '_blank').focus();
      });
  }

  public onAssetsChange($event) {
    this.assets = $event;
    this.assetsChange.emit(this.assets);
  }
}
