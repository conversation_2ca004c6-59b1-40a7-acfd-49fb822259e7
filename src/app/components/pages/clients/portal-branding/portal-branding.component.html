<div class="branding" *ngIf="!loading">
  <div class="branding-settings-panel">
    <div class="history-actions">
      <a (click)="undo()">
        <mat-icon>undo</mat-icon>
        <span>Undo</span>
      </a>
      <a (click)="redo()">
        <mat-icon>redo</mat-icon>
        <span>Redo</span>
      </a>
      <a (click)="reset()">
        <mat-icon>replay</mat-icon>
        <span>Reset</span>
      </a>
      <a (click)="clean()">
        <mat-icon>clear</mat-icon>
        <span>Clear</span>
      </a>
      <a (click)="preview()">
        <mat-icon>screen_share</mat-icon>
        <span>Preview</span>
      </a>
      <a *ngIf="selectedView.type === 'general' || selectedView.type === 'signin'" (click)="isMobilePreview = !isMobilePreview">
        <mat-icon>{{ isMobilePreview ? "smartphone" : "tv" }}</mat-icon>
        <span>{{ isMobilePreview ? "Mobile" : "Desktop" }}</span>
      </a>
    </div>

    <div class="branding-settings-form">
      <portal-branding-settings-form
        [(settings)]="branding.settings"
        (settingsChange)="save()"
        [(assets)]="assets"
        [views]="views"
        [(selectedView)]="selectedView"
      ></portal-branding-settings-form>

      <branding-settings-form-field
        class="branding-name-row"
        type="text"
        label="Branding Name"
        [(value)]="branding.name"
        (valueChange)="emit()"
      ></branding-settings-form-field>

      <div class="data-actions">
        <div class="state-indicator" [class.primary]="branding.enabled">
          <mat-icon>{{ branding.enabled ? "check" : "warning" }}</mat-icon>
          Branding is {{ !branding.enabled ? "not" : "" }} enabled
        </div>
        <button
          class="btn btn-primary btn-small"
          [matMenuTriggerFor]="saveMenu"
        >
          Save
        </button>
        <mat-menu #saveMenu="matMenu" xPosition="after" yPosition="above">
          <button
            mat-menu-item
            (click)="branding.enabled = false; confirmSettings()"
          >
            <mat-icon>check_box_outline_blank</mat-icon> Save
          </button>
          <button
            mat-menu-item
            (click)="branding.enabled = true; confirmSettings()"
          >
            <mat-icon>check_box</mat-icon> Save & Enable
          </button>
        </mat-menu>
      </div>
    </div>
  </div>
  <!-- Front-cover interactive preview -->
  <div class="front-cover-preview-form">
    <portal-signin-preview
      *ngIf="selectedView.type === 'general' || selectedView.type === 'signin'"
      [isMobileView]="isMobilePreview"
      [(settings)]="branding.settings"
      (settingsChange)="save()"
      [assets]="assets"
    ></portal-signin-preview>
    <portal-dashboard-preview
      *ngIf="selectedView.type === 'dashboard' || selectedView.type === 'manifests'"
      [(settings)]="branding.settings"
      (settingsChange)="save()"
      [assets]="assets"
    ></portal-dashboard-preview>
  </div>

  <div class="upload-assets-form desktop">
    <app-branding-assets
      [compact]="true"
      [client]="client"
      [(assets)]="assets"
      (dataChange)="onAssetsChange($event)"
    ></app-branding-assets>
  </div>
</div>

<div class="upload-assets-form tablets">
  <app-branding-assets
    [compact]="false"
    [client]="client"
    [(assets)]="assets"
    (dataChange)="onAssetsChange($event)"
  ></app-branding-assets>
</div>
