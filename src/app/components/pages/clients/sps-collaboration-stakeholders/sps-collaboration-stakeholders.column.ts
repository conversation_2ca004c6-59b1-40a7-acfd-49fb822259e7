import { KFTableColumn } from "@/components/controls";

export const SPS_COLLABORATION_STAKEHOLDER_COLUMNS: KFTableColumn<any>[] = [
    {
        name: 'name',
        label: 'NAME',
        type: 'text',
        width: '220px',
        sortable: true,
    },
    {
        name: 'email',
        label: 'EMAIL ADDRESS',
        type: 'text',
        sortable: true,
        width: '270px',
    },
    {
        name: 'access',
        label: 'ACCESS',
        type: 'text',
        sortable: true,
        width: '270px', 
    },
    {
        name: 'surveyStatus',
        label: 'SURVEY STATUS',
        type: 'text',
        sortable: true,
        width: '120px',
    },
    {
        name: 'survey',
        label: '',
        type: 'text',
        sortable: false,
        width: '100px',
    },
    {
        name: 'outputStatus',
        label: 'OUTPUT STATUS',
        type: 'text',
        sortable: true,
        width: '200px'

    },
];