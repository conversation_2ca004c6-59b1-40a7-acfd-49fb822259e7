import { Component, OnInit } from '@angular/core';
import { SpsCollaborationStakeholderResponse } from '@/shared/models/sps-client/sps-collaboration-stakeholder';
import { SPS_COLLABORATION_STAKEHOLDER_COLUMNS } from './sps-collaboration-stakeholders.column';
import { KFTableColumn } from '@/components/controls';
import { CollaborationStakeholdersList } from '@/shared/models/sps-client/sps-stakeholder';
import { Subscription } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { PageHeaderService, SpinnerService } from '@/services';
import { CollaboratorStakeholderService } from '@/services/sps-collboration-stakeholder.service';

@Component({
  selector: 'app-sps-collaboration-stakeholders',
  templateUrl: './sps-collaboration-stakeholders.component.html',
  styleUrls: ['./sps-collaboration-stakeholders.component.scss']
})
export class SpsCollaborationStakeholdersComponent implements OnInit {
  tableData: SpsCollaborationStakeholderResponse[];
  columns = SPS_COLLABORATION_STAKEHOLDER_COLUMNS;
  collabId: number;
  currentSubscription: Subscription;

  customPaging = {
    pageIndex: 1,
    pageSize: 10,
    totalPages: null,
    totalResultRecords: null,
  };

  constructor(private activatedRoute: ActivatedRoute, private spsCollaborationService: CollaboratorStakeholderService, 
    private router: Router, private spinnerService: SpinnerService, private pageHeaderService: PageHeaderService) { }

  ngOnInit() {
    this.activatedRoute.queryParams.subscribe(params => {
      this.collabId = params['collabId'];
    })
    this.getData(this.collabId);
    const spsCollabDetails = localStorage.getItem('spsCollabDetails');
    const collabDetails = JSON.parse(spsCollabDetails);
    this.pageHeaderService.setSPSDetails('Collaboration Stakeholders', collabDetails.surveyName)
    this.columns.forEach((column: KFTableColumn<CollaborationStakeholdersList>) => {
      if (column.name === 'name') {
        column.click = (t: any) => {
          this.router.navigate(['']);
        }
        column.url = (t: any) =>
          this.router
            .createUrlTree([''])
            .toString();
      } else if (column.name === 'survey') {
        column.click = (t: any) => {
          // action still not groomed
        }
      }
    });
  }

  getData(collabId: number) {
    this.getPaginationData(collabId);
  }

  onPageClick(event) {
    this.getPaginationData(this.collabId, event.pageIndex + 1, event.pageSize)
  }

  getPaginationData(collabId: number, pageIndex?: any, pageSize?: number) {
    const resolvedPageIndex = pageIndex === undefined ? 1 : pageIndex;
    const resolvedPageSize = pageSize === undefined ? 10 : pageSize;
    this.spinnerService.activate();
    this.currentSubscription = this.spsCollaborationService.getCollaborationStakeholderDetails(collabId, resolvedPageIndex, resolvedPageSize).subscribe((response) => {
      this.customPaging = {
        pageIndex: resolvedPageIndex,
        pageSize: resolvedPageSize,
        totalResultRecords: response.data.totalRecords,
        totalPages: response.data.totalRecords / resolvedPageSize
      }
      this.spinnerService.deactivate();
      response.data.collabStakeholderResponselist.participants.forEach(participant => {

        participant.access = participant.permissions.map(permission => permission.permissionLevel).join(', ');
        participant.name = participant.firstName + ' ' + participant.lastName;

        let completedStages = participant.surveydashboardResponse.filter(response => response.stage === "Complete").length;
        completedStages === 3 ? "3/3" : `${completedStages}/3`;
        if (participant.surveyStatus === "In Progress") {
          participant.survey = "(" +completedStages + "/3)" ;
        }
    });
      this.tableData = response.data.collabStakeholderResponselist.participants;
    }),
      (error: Error) => {
        this.spinnerService.deactivate();
        this.tableData = []; 
      }
  }

  ngOnDestroy() {
    if (this.currentSubscription) {
      this.currentSubscription.unsubscribe();
    }
  }

}
