@import "../../../../../styles/colors.scss";
@import "../../../../../styles/theme.scss";

:host {
  .details-row {
    color: red;
  }

  .table-container {
    box-shadow: 0 0 1px rgba(0, 0, 0, 0.3);
  }
}

.flex {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  padding: 24px;
}

.menu-button {
  margin-left: 24px;
  min-width: 270px;
  background: white;
  margin-right: 48px;
  padding: 16px;
  border: thin solid rgba(145, 145, 145, 0.3);
  letter-spacing: 0.4px;
  color: $dark-primary-text;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .mat-select-arrow {
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid $dark-primary-text;
    margin: 0 6px;
    display: inline-block;
    vertical-align: middle;
  }
}
