import { KFTableAction, KFTableColumn } from '@/components/controls';
import {
  AlertService,
  LanguagesService,
  ProjectService,
  SpinnerService,
} from '@/services';
import { CandidateReportLogsComponent, ReportLanguagesDialogComponent } from '@/components/dialogs';
import { Client } from '@/shared/models';
import { LanguagePipe } from '@/shared/pipes';
import { Location } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material';
import { ActivatedRoute } from '@angular/router';
import { saveAs } from 'file-saver';
import * as JSZip from 'jszip';
import { combineLatest, forkJoin, of, Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';

@Component({
  selector: 'app-candidate-report-status.component',
  styleUrls: ['./candidate-report-status.component.scss'],
  templateUrl: './candidate-report-status.component.html',
})
export class CandidateReportStatusComponent implements OnInit {
  reports: any;
  columns: KFTableColumn<any>[] = [
    {
      type: 'text',
      name: 'reportKey',
      label: 'Name',
    },
    {
      type: 'boolean',
      name: 'successful',
      label: 'Successful',
      width: '150px',
    },
    {
      type: 'date',
      name: 'reportGeneratedDate',
      label: 'Last Regenerated Date',
      dateFormat: 'dd MMM yyyy, HH:mm:ss',
      width: '250px',
    },
    {
      type: 'rowOptions',
      name: 'actions',
      label: 'Actions',
      width: '200px',
    },
  ];
  actions: KFTableAction<ReportStatus>[] = [
    {
      icon: 'cached',
      label: 'Regenerate Report',
      css: 'green',
      stopPropagation: true,
      displayCondition: () => true,
      click: (t: ReportStatus) => this.showLanguagesToRegenerateDialog(t),
    },
    {
      icon: 'save_alt',
      label: 'Download Report',
      css: '',
      displayCondition: (t: ReportStatus) => t.successful,
      click: (t: ReportStatus) => this.showLanguagesToDownloadDialog(t),
    },
    {
      icon: 'assignment',
      label: 'View logs',
      css: '',
      displayCondition: () => true,
      click: (t: ReportStatus) => this.showViewLogsDialog(t),
    },
  ];
  selectedReport: ReportStatus;
  client: Client;
  project: any;
  successProfiles: { projectId: any; id: any; name: any }[];
  selectedSp: { projectId: any; id: any; name: any };
  params: any;
  candidate: any;

  constructor(
    private languageService: LanguagesService,
    private projectService: ProjectService,
    private alertService: AlertService,
    private spinnerService: SpinnerService,
    private languagePipe: LanguagePipe,
    private route: ActivatedRoute,
    private location: Location,
    public dialog: MatDialog
  ) {}

  ngOnInit() {
    this.route.data.subscribe((resolved) => {
      this.client = resolved.client;
      this.project = resolved.project;
      this.candidate = resolved.candidate;
      this.selectedSp =
        this.project.successProfiles && this.project.successProfiles.length
          ? this.route.snapshot.queryParams.selectedProjectId
            ? this.project.successProfiles.find(
                (sp) =>
                  sp && sp.projectId.toString() ===
                  this.route.snapshot.queryParams.selectedProjectId
              )
            : this.project.successProfiles[0]
          : null;
      this.refreshReportStatus();
    });
  }

  onSpChange(sp: any) {
    this.selectedSp = sp;
    this.updateCurrentUrl();
    this.refreshReportStatus();
  }

  updateCurrentUrl() {
    const currentUrl = this.location.path().split('?')[0];
    const params = {
      clientId: this.client.id,
      candidateId: this.candidate.participantId,
      projectId: this.project.projectId,
      selectedProjectId: this.selectedProjectId,
    };

    const queryString = Object.keys(params)
      .filter((k) => params[k])
      .map((k) => [k, params[k]].join('='))
      .join('&');

    this.location.go(`${currentUrl}?${queryString}`);
  }

  get selectedProjectId() {
    return this.selectedSp ? this.selectedSp.projectId : this.project.projectId;
  }

  refreshReportStatus() {
    const allReportsRequest = this.projectService.getProjectReports(
      this.selectedProjectId
    ) as Observable<any>;
    const statusesRequest = this.projectService.getReportStatus(
      this.candidate.participantId,
      this.selectedProjectId
    ) as Observable<any>;
    combineLatest(allReportsRequest, statusesRequest).subscribe(
      ([reports, statuses]) => {
        this.reports = reports.map((r: any) => {
          let report = statuses.find((s) => s.reportKey === r.type);
          if (!report) {
            report = {
              candidateId: this.candidate.participantId,
              projectId: this.selectedProjectId,
              reportKey: r.type,
              reportGeneratedDate: null,
              successful: false,
              reportInfos: r.languages.map((locale) => ({
                downloadLink: '',
                errorMessage: 'Batch log not found',
                languageCode: locale,
                languageName: this.languagePipe.transform(locale),
                reportGenerationAttempted: false,
                showDownLoadLink: false,
              })),
            } as ReportStatus;
          }

          report.successful = report.reportInfos.filter(
            (ri) => ri.showDownLoadLink
          ).length;
          return report;
        });

        this.reports.forEach((x) => {
          const date = new Date(x.reportGeneratedDate);
          if (date.getFullYear() < 2000) {
            x.reportGeneratedDate = null;
          }
        });
      }
    );
  }

  showLanguagesToDownloadDialog(t: ReportStatus) {
    this.dialog
      .open(ReportLanguagesDialogComponent, {
        width: '650px',
        data: {
          action: 'download',
          languages: t.reportInfos,
          disabledIf: (x) => !x.showDownLoadLink,
        },
      })
      .afterClosed()
      .subscribe((response: any = []) => {
        if (!response.length) {
          return;
        }

        const reportInfos = response.map((l) =>
          t.reportInfos.find(
            (x) =>
              this.languageService.getLanguageByLocale(x.languageCode).id ===
              l.id
          )
        );

        const reportsToDownload = reportInfos
          .filter((r) => r.showDownLoadLink)
          .map((r) => ({
            request: this.projectService.downloadReport(r.downloadLink, r.fileName),
            downloadLink: r.downloadLink,
            fileName: r.fileName,
          }));

        // const reportsToRegenerate = reportInfos
        //   .filter((r) => !r.showDownLoadLink)
        //   .map((r) =>
        //     this.projectService.regenerateReports(
        //       this.selectedProjectId,
        //       r.candidateId,
        //       [r.reportKey]
        //     )
        //   );

        this.downloadReports(reportsToDownload);
      });
  }

  showViewLogsDialog(t:ReportStatus){

    this.spinnerService.activate();

    this.projectService.getParticipantReportLogs(t.candidateId, t.projectId, t.reportKey)
      .subscribe((responseData) => {

        this.spinnerService.deactivate();
        this.dialog
          .open(CandidateReportLogsComponent, {
            width: '650px',
            data: responseData,
          });
      });

  }

  downloadReports(reportRequests) {
    this.spinnerService.activate();
    forkJoin(
      reportRequests.map((rr) =>
        rr.request.pipe(
          catchError((err) => {
            return of(err.status);
          })
        )
      )
    ).subscribe(
      (reports: (string | Blob)[]) => {
        const zip = new JSZip();
        const name = `reports ${this.project.name} ${this.candidate.displayName}`;

        reports.forEach((report, i) => {
          const fileName = reportRequests[i].fileName;
          zip.file(
            fileName,
            report
          );
        });

        zip
          .generateAsync({ type: 'blob' })
          .then((content) => {
            this.spinnerService.deactivate();
            if (content) {
              saveAs(content, name.replace(/[^a-zA-Z0-9 \[\]\(\)_-]/g, ''));
            }
          })
          .catch((error) => {
            this.spinnerService.deactivate();
            this.alertService.error(
              'An error occured during the building of zip archive'
            );
          });
      },
      (error) => {
        this.spinnerService.deactivate();
        this.alertService.error(
          'An error occured during the downloading reports'
        );
      }
    );
  }

  showLanguagesToRegenerateDialog(t: ReportStatus) {
    this.dialog
      .open(ReportLanguagesDialogComponent, {
        width: '650px',
        data: {
          action: 'regenerate',
          languages: t.reportInfos,
        },
      })
      .afterClosed()
      .subscribe((langs: any) => {
        if (langs && langs.length) {
          this.projectService
            .regenerateReports(
              this.selectedProjectId,
              t.candidateId,
              [t.reportKey],
              langs.map((lang) => lang.id)
            )
            .subscribe((result) => {
              this.alertService.success(result);
            });
        }
      });
  }
}

export class ReportInfo {
  languageCode: string;
  languageName: string;
  errorMessage: string;
  reportGenerationAttempted: boolean;
  downloadLink: string;
  showDownLoadLink: boolean;
  fileName: string;
}

export class ReportStatus {
  candidateId: number;
  projectId: number;
  reportGeneratedDate: Date;
  reportInfos: ReportInfo[];
  reportKey: string;
  successful: boolean;
}
