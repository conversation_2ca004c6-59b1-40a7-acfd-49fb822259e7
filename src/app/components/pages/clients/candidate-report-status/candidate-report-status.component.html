<div class="page-content glass">
  <div
    class="flex"
    *ngIf="project.successProfiles && project.successProfiles.length > 1"
  >
    <p>Success Profile:</p>

    <mat-menu #spMenu="matMenu" class="menu">
      <button
        mat-menu-item
        *ngFor="let sp of project.successProfiles"
        (click)="onSpChange(sp)"
      >
        {{ sp.name }}
      </button>
    </mat-menu>

    <button class="menu-button" [matMenuTriggerFor]="spMenu">
      {{ selectedSp.name }}
      <span class="mat-select-arrow"></span>
    </button>
  </div>

  <div class="table-container">
    <kf-table [data]="reports" [columns]="columns" [actions]="actions">
    </kf-table>
  </div>
</div>
