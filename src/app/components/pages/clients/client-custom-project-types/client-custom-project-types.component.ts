import { Compo<PERSON>, OnIni<PERSON>, On<PERSON><PERSON>roy, TemplateRef, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CustomProjectTypeService } from '@/services/custom-project-type.service';
import { AlertService } from '@/services/alert.service';
import { SpinnerService } from '@/services/spinner.service';
import { Client, CustomProjectTypeDescription, CPTColumns, Months } from '@/shared/models';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-client-custom-project-types',
  templateUrl: './client-custom-project-types.component.html',
  styleUrls: ['./client-custom-project-types.component.scss']
})
export class ClientCustomProjectTypesComponent implements OnInit, OnDestroy {
  @ViewChild('PRIORITY_TPL') PRIORITY_TPL: TemplateRef<any>;
  currentSubscriptions: Subscription[] = [];

  client: Client;
  filter = '';
  columns: any[];
  customProjectTypeData: CustomProjectTypeDescription[];
  isLoading = false;

  constructor(
    private customProjectTypeService: CustomProjectTypeService,
    private alertService: AlertService,
    private spinnerService: SpinnerService,
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit() {
    this.columns = CPTColumns;
    this.columns[0].click = this.navigateToProject.bind(this);
    this.columns[0].url = this.generateProjectTypeUrl.bind(this);
    this.columns[4].template = this.PRIORITY_TPL;

    this.currentSubscriptions.push(
      this.route.data.subscribe(
      (result) => {
        this.client = result.client;
      },
      (error) => {
        this.alertService.error(error.message);
      })
    );

    this.refreshTable();
  }

  refreshTable() {
    this.isLoading = true;
    this.spinnerService.activate();
  
    this.currentSubscriptions.push(
      this.customProjectTypeService.getCustomProjectTypes(this.client.id).subscribe(
      result => {
        this.customProjectTypeData = [];
        result.map((customProjectType) => {
          const lastAmendedDate = new Date(customProjectType.lastAmendedDate);
          const formattedLastAmended = `${lastAmendedDate.getDate()} ${this.customProjectTypeService.getMonthName(lastAmendedDate.getMonth())} ${lastAmendedDate.getFullYear()}`;
  
          this.customProjectTypeData.push({
            id: customProjectType.customProjectTypeId,
            projectType: customProjectType.name,
            createdBy: `${customProjectType.initiatedBy.firstNameKey} ${customProjectType.initiatedBy.lastNameKey}`,
            lastAmended: formattedLastAmended,
            usedIn: customProjectType.projectCount,
            reportFilterCreated: !!customProjectType.isFilterCreated
          });
        });
        this.isLoading = false;
        this.spinnerService.deactivate();
      },
      error => {
        this.isLoading = false;
        this.spinnerService.deactivate();
        this.alertService.error(error.message);
      },
    ));
  }

  createNewProjectType() {
    this.router.navigate(['../custom-project-type-details'], {
      relativeTo: this.route,
      queryParams: {
        clientId: this.client.id
      },
    });
  }

  navigateToProject(row: CustomProjectTypeDescription) {
    const projectTypeId = row.id;
    this.router.navigate(['../custom-project-type-details'], {
      relativeTo: this.route,
      queryParams: {
        clientId: this.client.id,
        projectTypeId: projectTypeId,
      },
    });
  }
  
  navigateToReports() {
    this.router.navigate(['/clients/report-management'], {
      queryParams: {
        clientId: this.client.id
      }
    });
  }
  generateProjectTypeUrl(row: CustomProjectTypeDescription): string {
    if (row.id) {
      return this.router.createUrlTree(['../custom-project-type-details'], {
        relativeTo: this.route,
        queryParams: {
          clientId: this.client.id,
          projectTypeId: row.id,
        },
      }).toString();
    }
    return '';
  }

  ngOnDestroy() {
    if (this.currentSubscriptions.length > 0) {
      this.currentSubscriptions.forEach(subscription => subscription.unsubscribe());
    }
  }
}
