<div class="page-content">
  <div class="table-actions-panel">
    <a class="btn btn-primary glass" (click)="createNewProjectType()">CREATE PROJECT TYPE</a>
  </div>

  <div class="table-container">
    <kf-table [data]="customProjectTypeData" [columns]="columns">
      <ng-template #PRIORITY_TPL let-cell>
        <div class="status-icon-container" [matTooltip]="cell.element.reportFilterCreated ? '' : 'No report filters have been created for this project type'">
          <div class="warning-icon red" *ngIf="!cell.element.reportFilterCreated">!</div>
          <a (click)="navigateToReports()" class="reports-link" *ngIf="!cell.element.reportFilterCreated">Reports</a>
        </div>
      </ng-template>
    </kf-table>
  </div>
</div>

