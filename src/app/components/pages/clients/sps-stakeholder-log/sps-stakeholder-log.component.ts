import { Component, OnInit } from '@angular/core';
import { STAKEHOLDER_LOG_COLUMNS } from './sps-stakeholder-log-columns';
import { StakeholderLog, StakeholderLogResponse } from '@/shared/models/sps-client/stakeholder-log';
import { Subscription } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { SpsStakeholderLogService } from '@/services/sps-stakeholder-log.service';
import { PageHeaderService, SpinnerService } from '@/services';
import { KFTableColumn } from '@/components/controls';


@Component({
  selector: 'app-sps-stakeholder-log',
  templateUrl: './sps-stakeholder-log.component.html',
  styleUrls: ['./sps-stakeholder-log.component.scss']
})
export class SpsStakeholderLogComponent implements OnInit {

  columns = STAKEHOLDER_LOG_COLUMNS;
  tableData: StakeholderLog[];
  filtering = false;
  clientId: string;
  currentSubscription: Subscription;
  customPaging = {
    pageIndex: 1,
    pageSize: 10,
    totalPages: null,
    totalResultRecords: null,
  };

  constructor(private route: ActivatedRoute,private pageHeaderService: PageHeaderService, private spsStakeholderService: SpsStakeholderLogService, private router: Router,private spinnerService: SpinnerService) { }

  ngOnInit() {
    this.clientId = JSON.parse(localStorage.getItem('selectedClient')).id;
    this.pageHeaderService.setSPSDetails('Stakeholder Log')
    this.getStakeholderLogData(this.clientId);
    this.columns.forEach((column: KFTableColumn<any>) => {
      if (column.name === 'stakeholderName' || column.name === 'collabName') {
        column.click = (t: any) => {
          this.router.navigate(['../sps-stakeholder-details'], {
            relativeTo: this.route,
            queryParams: { 
              emailId: t.emailId,
              pageIndex: 1,
              pageSize: 10
            },
          });
          const stakeholdersDetails = {
            emailId: t.emailId,
            clientId: this.clientId
          }
          localStorage.setItem('stakeholderDetailsData', JSON.stringify(stakeholdersDetails));
        }
        column.url = (t: any) =>
          this.router
            .createUrlTree(['../sps-stakeholder-details'], {
              relativeTo: this.route,
              queryParams: {
                emailId: t.emailId,
                pageIndex: 1,
                pageSize: 10
              },
            })
            .toString();
      }
    })
}

  onPageClick(event) {
    if (
      this.customPaging.pageSize !== event.pageSize ||
      this.customPaging.pageIndex !== event.pageIndex + 1
    ) {
      this.customPaging.pageSize = event.pageSize;
      this.customPaging.pageIndex = event.pageIndex + 1;
      this.getStakeholderLogData(this.clientId, event.pageIndex + 1, event.pageSize);
    }
    
  }
  getStakeholderLogData(clientId:string, pageIndex?:number, pageSize?:number) {
    const resolvedPageIndex = pageIndex === undefined ? 1 : pageIndex;
    const resolvedPageSize = pageSize === undefined ? 10 : pageSize;
    this.spinnerService.activate();
    this.currentSubscription = this.spsStakeholderService.getStakeholderLogData(clientId, resolvedPageIndex, resolvedPageSize).subscribe((response:StakeholderLogResponse) => {
      this.customPaging = {
        pageIndex: resolvedPageIndex,
        pageSize: resolvedPageSize,
        totalResultRecords: response.data.totalRecords,
        totalPages: response.data.totalRecords / resolvedPageSize
      }
      this.spinnerService.deactivate();
      this.tableData = response.data.collabParticipants;
    }),
      (error:Error) => {
        console.error(error);
        this.spinnerService.deactivate();
        this.tableData = [];
      }
  }
 
  ngOnDestroy() {
    if (this.currentSubscription) {
      this.currentSubscription.unsubscribe();
    }
  }
}
