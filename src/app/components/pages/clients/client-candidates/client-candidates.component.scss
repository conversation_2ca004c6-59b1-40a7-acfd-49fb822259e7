@import '../../../../../styles/colors.scss';
@import '../../../../../styles/typography.scss';

:host {
  flex: 1;
}

.table-actions-panel {
  margin-bottom: 16px;

  display: flex;
  flex-direction: row;
  justify-content: stretch;
  align-items: center;
  user-select: none;

  input.with-icon {
    flex-grow: 1;
    padding-left: 48px;
    margin-right: 12px;
  }

  .input-icon {
    z-index: 1;
    width: 0;
    transform: translateX(12px);
    color: #007da4;
  }
}

.table-container {
  box-shadow: 0 0 1px rgba(0, 0, 0, 0.3);
}

button.search {
  cursor: pointer;
  display: inline-block;
  padding: 12px 48px;
  margin-right: 0;

  white-space: nowrap;
  vertical-align: middle;

  font: unquote($proxima-font);
  font-weight: bold;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.smaller {
  font-size: smaller;
}

.initSearchText {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;

  span {
    font-weight: bold;
  }
}
