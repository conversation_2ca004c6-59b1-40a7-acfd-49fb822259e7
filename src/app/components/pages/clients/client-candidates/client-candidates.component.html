<div class="page-content">
  <div class="table-actions-panel">
    <mat-icon class="input-icon">search</mat-icon>
    <input
      type="search"
      class="with-icon strong-glass"
      [(ngModel)]="searchkey"
      (keydown.enter)="search()"
      placeholder="Search participants"
    />

    <kf-select
      class="strong-glass"
      placeholder="Participant Status"
      [options]="filterOptions.status"
      [selection]="filter.STATUS"
      (selectionChange)="onFilterChange('STATUS', $event)"
    ></kf-select>

    <kf-select
      class="strong-glass"
      placeholder="Project Type"
      [options]="filterOptions.type"
      [selection]="filter.PROJECTTYPES"
      (selectionChange)="onFilterChange('PROJECTTYPES', $event)"
    ></kf-select>

    <button class="search btn btn-primary" (click)="search()">Search</button>
  </div>

  <div *ngIf="tableData" class="table-container">
    <kf-table
      [data]="tableData || []"
      [columns]="columns"
      [customPaging]="customPaging"
      (paginatorChanged)="onPageChange($event)"
      (serverSortChanged)="sortData($event)"
      [useServerSorting]="true"
    ></kf-table>
  </div>

  <div *ngIf="!tableData" class="initSearchText weak-glass">
    <span> Use the filters to search client participants </span>
  </div>
</div>

<ng-template #PROJECTS_TPL let-context>
  <ng-container *ngIf="context.element.projects && context.element.projects.length > 0">
    <a (click)="goToProjectDetails(context.element.projects[0].projectId)">
      {{ context.element.projects[0].name }}
    </a>
    <span class="grey" *ngIf="context.element.projects.length > 1"
      >(+{{ context.element.projects.length - 1 }})</span
    >
    <br />
    <span class="smaller grey"
      >[{{ context.element.projects[0].type }} -
      {{ context.element.projects[0].participantStatus }}]</span
    >
  </ng-container>
</ng-template>
