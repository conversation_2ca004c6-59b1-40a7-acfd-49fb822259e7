import { KFTableColumn } from '@/components/controls/kf-table/kf-table.component';
import { ParticipantService, SpinnerService } from '@/services';
import { Client } from '@/shared/models';
import { Location } from '@angular/common';
import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Router } from '@angular/router';
import { Sort } from '@angular/material';

/** Component to display client projects. */
@Component({
  selector: 'app-client-candidates',
  templateUrl: './client-candidates.component.html',
  styleUrls: ['./client-candidates.component.scss'],
})
export class ClientCandidatesComponent implements OnInit {
  columns: KFTableColumn<any>[] = [
    {
      name: 'displayName',
      label: 'Name',
      type: 'textCapitalized',
      sortable: true,
      click: item => this.goToParticipantDetails(item.participantId),
    },
    {
      name: 'email',
      label: 'Email',
      type: 'text',
      sortable: true,
    },
    {
      name: 'projects',
      label: 'Project(s)',
      type: 'text',
      sortable: true,
      compare: (a, b, asc) => {
        return (a[0].name.toLowerCase() < b[0].name.toLowerCase() ? -1 : 1) * (asc ? 1 : -1);
      }
    },
    {
      name: 'iC2EmployeeId',
      label: 'IC2 Employee ID',
      type: 'text',
      sortable: false
    },
  ];
  @ViewChild('PROJECTS_TPL') PROJECTS_TPL: TemplateRef<any>;

  client: Client;
  searchkey = '';
  
  customPaging = {
    pageIndex: 1,
    pageSize: 10,
    totalPages: null,
    totalResultRecords: null,
  };
  tableData: any[];
  loading: boolean;

  filter = {
    PROJECTTYPES: [],
    STATUS: [],
  };
  filterOptions: any;

  sortColumn: string = '';
  sortBy: string = '';

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private location: Location,
    private spinnerService: SpinnerService,
    private participantService: ParticipantService,
  ) {}

  ngOnInit() {
    this.spinnerService.activate();
    this.route.data.subscribe(resolved => {
      this.spinnerService.deactivate();
      this.client = resolved.client;
      this.filterOptions = this.mapMetadataToFilterOptions(resolved.metadata.data.metadata[0]);
      this.setFiltersFromUri();
    });

    this.columns.forEach(column => {
      if (column.name === 'projects') {
        column.template = this.PROJECTS_TPL;
      }
    });
  }

  setFiltersFromUri() {
    this.route.queryParams.subscribe(params => {
      if (params.searchKey) {
        this.searchkey = params.searchKey;
      }
      if (params.projects) {
        this.filter.PROJECTTYPES = params.projects.split(',');
      }
      if (params.status) {
        this.filter.STATUS = params.status.split(',');
      }
      if (params.searchKey || params.projects || status) {
        this.search();
      }
    });
  }

  mapMetadataToFilterOptions(metadata) {
    return {
      type: metadata.searchOn.find(m => m.name === 'PROJECTTYPES').options,
      status: metadata.searchOn.find(m => m.name === 'STATUS').options,
    };
  }

  onFilterChange(filterType, values) {
    this.filter[filterType] = values;
    this.search();
  }

  onPageChange(event) {
    if (
      this.customPaging.pageSize !== event.pageSize ||
      this.customPaging.pageIndex !== event.pageIndex + 1
    ) {
      this.customPaging.pageSize = event.pageSize;
      this.customPaging.pageIndex = event.pageIndex + 1;
      this.search(this.customPaging.pageIndex, this.customPaging.pageSize)
    }
  }
  search(pageIndex?: number, pageSize?: number) {
    const resolvedPageIndex = pageIndex === undefined ? 1 : pageIndex;
    const resolvedPageSize = pageSize === undefined ? 10 : pageSize;
    this.spinnerService.activate();
    this.participantService
      .searchClientParticipants(this.client.id, this.searchkey, this.filter, resolvedPageIndex, resolvedPageSize, this.sortColumn, this.sortBy)
      .subscribe((result: any) => {
        this.tableData = result.data;
        this.customPaging = result.paging;
        this.updateCurrentUrl();
        this.spinnerService.deactivate();
      });
  }

  sortData(sort: Sort) {
    if (sort.active === 'displayName') {
      sort.active = 'name';
    }
    if (sort.active === 'projects') {
      sort.active = 'projectName';
    }
    this.sortColumn = sort.active;
    this.sortBy = sort.direction;

    this.customPaging.pageIndex = 1; // Reset to the first page on sorting change
    this.search(this.customPaging.pageIndex, this.customPaging.pageSize);
  }

  updateCurrentUrl() {
    const currentUrl = this.location.path().split('?')[0];
    const params = {
      projects: this.filter.PROJECTTYPES.join(','),
      status: this.filter.STATUS.join(','),
    };

    const queryString = Object.keys(params)
      .filter(k => params[k])
      .map(k => [k, params[k]].join('='))
      .join('&');

    this.location.go(`${currentUrl}?clientId=${this.client.id}&searchKey=${this.searchkey}` + (queryString ? `&${queryString}` : ''));
  }

  goToProjectDetails(projectId) {
    this.router.navigate(['/clients/project-details'], {
      queryParams: { projectId, clientId: this.client.id, selectedProjectId: ''  }
    });
  }

  goToParticipantDetails(candidateId) {
    this.router.navigate(['/clients/candidate-details'], {
      queryParams: { candidateId, clientId: this.client.id }
    });
  }
}
