<div class="details-page-content white-glass flex">
  <div class="form-box">
    <table class="readonly-args">
      <tr *ngFor="let item of readonlyArgs">
        <td>{{ item.label }}:</td>
        <td class="value">
          {{ getRequestFormSetting(item.property) }}
        </td>
      </tr>
    </table>

    <div class="flex">
      <div>
        <p class="desc">KF4D Legacy option:</p>
        <div *ngFor="let item of kf4dLegacy">
          <mat-checkbox
            type="checkbox"
            color="primary"
            [disableRipple]="true"
            [matTooltip]="item.tooltip"
            matTooltipPosition="right"
            (click)="$event.stopPropagation()"
            (change)="
              $event ? toggleKf4dLegacy(item.property, $event.checked) : null
            "
            [checked]="getRequestFormSetting(item.property)"
          >
            {{ item.label }}
          </mat-checkbox>
        </div>
        <p class="desc">
          Extract options: <a (click)="selectAllSettings(true)">Select all</a> |
          <a (click)="selectAllSettings(false)">Clear</a>
        </p>
        <div *ngFor="let item of settingArgs">
          <mat-checkbox
            type="checkbox"
            color="primary"
            [disableRipple]="true"
            [matTooltip]="item.tooltip"
            matTooltipPosition="right"
            [allowedRoles]="item.allowedRoles || allRoles"
            [disabled]="request.replicateKF4DLegacyExtract"
            (click)="$event.stopPropagation()"
            (change)="
              $event
                ? setRequestFormSetting(item.property, $event.checked)
                : null
            "
            [checked]="getRequestFormSetting(item.property)"
          >
            {{ item.label }}
          </mat-checkbox>
        </div>
        <div
          matTooltip="Determine the types of scores to include in the extract, at least one of these is required"
          matTooltipPosition="right"
        >
          <div class="desc">
            Score types: <a (click)="selectAllScoreTypes(true)">Select all</a> |
            <a (click)="selectAllScoreTypes(false)">Clear</a>
          </div>
          <div *ngFor="let item of scoresSettings">
            <mat-checkbox
              type="checkbox"
              color="primary"
              [disableRipple]="true"
              [disabled]="request.replicateKF4DLegacyExtract"
              (click)="$event.stopPropagation()"
              (change)="
                $event
                  ? setRequestFormSetting(item.property, $event.checked)
                  : null
              "
              [checked]="getRequestFormSetting(item.property)"
            >
              {{ item.label }}
            </mat-checkbox>
          </div>
        </div>
      </div>
      <div
        matTooltip="The specific assessments to include in the extract, if none selected then the assessments defined against the project are used"
        matTooltipPosition="below"
      >
        <div class="desc">
          Assessments: <a (click)="selectAllAssessments(true)">Select all</a> |
          <a (click)="selectAllAssessments(false)">Clear</a>
        </div>
        <div *ngFor="let item of blendedAssessments">
          <mat-checkbox
            type="checkbox"
            color="primary"
            [disableRipple]="true"
            [disabled]="request.replicateKF4DLegacyExtract"
            [checked]="isAssesmentSelected(item.id)"
            (click)="$event.stopPropagation()"
            (change)="
              $event ? toggleRequestFormAssesments(item.id, $event) : null
            "
          >
            {{ item.label }}
          </mat-checkbox>
        </div>
      </div>
    </div>

    <div>
      <button
        class="btn btn-primary extract-button"
        (click)="requestExtract()"
        [disabled]="
          !request.assessments ||
          !request.assessments.length ||
          noParticipants ||
          !validateScoreTypes()
        "
      >
        Extract
      </button>
      <span
        class="grey no-participant-label"
        *ngIf="
          !request.assessments ||
          !request.assessments.length ||
          noParticipants ||
          !validateScoreTypes()
        "
        [innerHTML]="validateErrorsLabel()"
      >
      </span>
    </div>
  </div>
  <div class="upload-file-box">
    <p class="desc">Upload extract request file in .xlsx format.</p>
    <p class="desc">
      Once file is uploaded, extract will be processed and downloaded to you
      computer.
    </p>
    <div>
      <file-upload
        (complete)="onFileComplete($event)"
        target="{{ actionMethod }}"
        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        hint="(Project Id will be taken from project selected on previous page)"
      ></file-upload>
    </div>
  </div>
</div>
