@import "../../../../../styles/colors.scss";
@import "../../../../../styles/theme.scss";
@import "../../../../../styles/typography.scss";

$container-padding: 48px;
$content-width: 1200px;
$side-margin: 48px 64px;

:host {
  flex: 1;

  .flex {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-start;

    & > * {
      margin-right: 64px;
    }
  }

  .readonly-args {
    td.value {
      font-weight: bold;
      letter-spacing: 0.5px;
      padding-left: 12px;
    }
  }

  .form-box {
    flex: 1 1 auto;
    border-right: solid thin $primary--grey-medium;
  }

  .desc {
    margin: 24px 0 12px 0;

    a {
      margin: 0 12px;
    }
  }

  .upload-file-box {
    width: 300px;
    padding: 0 24px;
    text-align: center;
    align-self: center;
    file-upload {
      margin-top: 48px;
    }
  }

  .extract-button {
    margin-top: 24px;
  }

  .no-participant-label {
    font-size: 13px;
    margin-left: 24px;
    display: inline-block;
    vertical-align: middle;
    line-height: 1.15;
    user-select: none;
  }
}
