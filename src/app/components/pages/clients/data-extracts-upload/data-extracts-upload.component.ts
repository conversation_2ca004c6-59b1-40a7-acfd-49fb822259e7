import {
  AlertService,
  AuthenticationService,
  ProjectService,
} from '@/services';
import { SpinnerService } from '@/services/spinner.service';
import { AuthRole, AuthRolesList, ParticipantMetadata, SearchOptionData } from '@/shared/models';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import * as FileSaver from 'file-saver';
import { combineLatest, Subscription } from 'rxjs';

/**
 * Project extracts component.
 */
@Component({
  selector: 'app-data-extracts-upload',
  templateUrl: './data-extracts-upload.component.html',
  styleUrls: ['./data-extracts-upload.component.scss'],
})
export class DataExtractsUploadComponent implements OnInit {
  currentSubscription: Subscription;
  projectIdMissing = false;
  projectId: number;
  project: any;
  client: any;
  actionMethod: string;
  readonlyArgs = [
    { property: 'clientName', label: 'Client Name' },
    { property: 'projectId', label: 'Project Id' },
    { property: 'projectName', label: 'Project Name' },
    { property: 'personRequestingExtract', label: 'Person requesting extract' },
  ];
  kf4dLegacy = [
    {
      property: 'replicateKF4DLegacyExtract',
      label: 'Replicate KF4D Legacy Extract',
      tooltip:
        'A preset configuration of scores and assessments, includes blanks columns to match the legacy KF4D extract',
    },
  ];
  settingArgs = [
    {
      property: 'includeSuccessProfile',
      label: 'Include Success Profile Details',
      tooltip:
        'Include a separate worksheet with the Best in class scores for the success profile',
    },
    {
      property: 'includePersonalData',
      label: 'Include PII',
      tooltip: 'Include participants forename and surname',
      allowedRoles: ['admin', 'productDelivery', 'dataScientist'] as AuthRole[]
    },
    {
      property: 'includeEmailAddress',
      label: 'Include Email Address',
      tooltip: 'Include participants email address',
    },
    {
      property: 'includeDemographics',
      label: 'Include Demographics',
      tooltip: 'Include the unified demographic responses',
    },
    {
      property: 'includeStatus',
      label: 'Include Status',
      tooltip:
        'Include the status for each assessment, a participant might have only completed half of their assessments',
    },
    {
      property: 'includeCompletedDates',
      label: 'Include Completed dates',
      tooltip: 'Include the date each assessment was completed',
    },
  ];
  scoresSettings = [
    {
      property: 'includeRawScores',
      label: 'Include Raw scores',
      tooltip:
        'Determine the types of scores to include in the extract, at least one of these is required',
    },
    {
      property: 'includeNormedScores',
      label: 'Include Normed scores',
      tooltip:
        'Determine the types of scores to include in the extract, at least one of these is required',
    },
    {
      property: 'includePercentileScores',
      label: 'Include Percentile scores',
      tooltip:
        'Determine the types of scores to include in the extract, at least one of these is required',
    },
    {
      property: 'includeStenScores',
      label: 'Include Sten scores',
      tooltip:
        'Determine the types of scores to include in the extract, at least one of these is required',
    },
    {
      property: 'includeFitScores',
      label: 'Include Fit Scores',
      tooltip:
        'Determine the types of scores to include in the extract, at least one of these is required',
    },
  ];
  assessments = [
    { label: 'Numerical', id: 1 },
    { label: 'Verbal', id: 2 },
    { label: 'Logical', id: 3 },
    { label: 'Checking', id: 4 },
    { label: 'Competencies', alt: 'behavioural', id: 5 },
    { label: 'Traits', id: 6 },
    { label: 'Drivers', id: 7 },
    { label: 'Experiences', id: 8 },
    { label: 'Preferences', id: 9 },
    { label: 'SJT', id: 10 },
    { label: 'Abstract', id: 11 },
    { label: 'TechnicalSkillsInventory', id: 12 },
    { label: 'InclusiveLeaderSjt', id: 13 },
  ];
  request: any = {};
  noParticipants: any;
  requestBackup: any;
  assessmentKeys: string[];
  allRoles = AuthRolesList;

  constructor(
    private route: ActivatedRoute,
    private alertService: AlertService,
    private spinnerService: SpinnerService,
    private authenticationService: AuthenticationService,
    private projectService: ProjectService
  ) {}

  ngOnInit() {
    this.route.data.subscribe(
      (resolved) => {
        this.client = resolved.client;
        this.project = resolved.project;

        const statusOptions = (resolved.searchMetadata as ParticipantMetadata).data.metadata[0].searchOn.find(
          (x) => x.name === 'STATUS'
        ).options;

        const statusIds = statusOptions
          .filter((x) => x.value === 'Completed' || x.value === 'In progress')
          .map((x) => x.id);

        const searchOptions = {
          filterBy: 'STATUS',
          filterValues: statusIds.join(';'),
        } as SearchOptionData;

        this.projectService
          .getParticipants(this.project.projectId, searchOptions)
          .subscribe((data) => {
            this.noParticipants = data.paging.totalResultRecords === 0;
          });

        this.assessmentKeys = Object.keys(this.project.assessments)
          .filter(
            (x) =>
              this.project.assessments[x] && this.project.assessments[x].measure
          )
          .map((x) => x.toLowerCase());
        this.actionMethod = `Extracts/${this.project.projectId}?extractType=PARTICIPANT`;
        this.setInitialRequestValues();
      },
      (error) => {
        this.projectIdMissing = true;
        this.alertService.error(error);
      }
    );
  }

  setInitialRequestValues() {
    this.request = {
      clientName: this.client.name,
      projectId: this.project.projectId,
      projectName: this.project.name,
      personRequestingExtract: `${this.authenticationService.currentUser.firstName} ${this.authenticationService.currentUser.lastName}`,
      includeSuccessProfile: false,
      includePersonalData: false,
      includeEmailAddress: false,
      includeDemographics: false,
      includeStatus: false,
      includeCompletedDates: false,
      replicateKF4DLegacyExtract: false,
      includeRawScores: false,
      includeNormedScores: false,
      includePercentileScores: false,
      includeStenScores: false,
      includeFitScores: false,
      assessments: [],
    };
    this.selectAllAssessments(true);
  }

  setKf4dLegacyRequestValues() {
    this.request = {
      clientName: this.client.name,
      projectId: this.project.projectId,
      projectName: this.project.name,
      personRequestingExtract: `${this.authenticationService.currentUser.firstName} ${this.authenticationService.currentUser.lastName}`,
      includeSuccessProfile: true,
      includePersonalData: this.authenticationService.isInAllowedRoles(['admin', 'productDelivery', 'dataScientist'] as AuthRole[]),
      includeEmailAddress: false,
      includeDemographics: false,
      includeStatus: false,
      includeCompletedDates: false,
      replicateKF4DLegacyExtract: true,
      includeRawScores: true,
      includeNormedScores: true,
      includePercentileScores: false,
      includeStenScores: false,
      includeFitScores: false,
      assessments: [5, 6, 7],
    };
  }

  get blendedAssessments() {
    return this.assessments.filter((item) =>
      this.request.replicateKF4DLegacyExtract
        ? this.request.assessments.indexOf(item.id) >= 0
        : this.assessmentKeys.indexOf((item.alt || item.label).toLowerCase()) >= 0
    );
  }

  getRequestFormSetting(key) {
    return this.request[key];
  }

  setRequestFormSetting(key, value) {
    this.request[key] = value;
  }

  toggleKf4dLegacy(key, value) {
    if (value) {
      this.requestBackup = Object.assign({}, this.request);
      this.setKf4dLegacyRequestValues();
    } else {
      this.request = Object.assign({}, this.requestBackup);
    }
  }

  selectAllSettings(value) {
    if (this.request.replicateKF4DLegacyExtract) {
      return;
    }

    Object.assign(this.request, {
      includeSuccessProfile: value,
      includePersonalData: value,
      includeEmailAddress: value,
      includeDemographics: value,
      includeStatus: value,
      includeCompletedDates: value,
    });
  }

  selectAllScoreTypes(value) {
    if (this.request.replicateKF4DLegacyExtract) {
      return;
    }

    Object.assign(this.request, {
      includeRawScores: value,
      includeNormedScores: value,
      includePercentileScores: value,
      includeStenScores: value,
      includeFitScores: value,
    });
  }

  selectAllAssessments(selectAll) {
    if (this.request.replicateKF4DLegacyExtract) {
      return;
    }

    this.request.assessments = selectAll
      ? this.blendedAssessments.map((x) => x.id)
      : [];
  }

  validateScoreTypes() {
    return (
      this.request.includeRawScores ||
      this.request.includeNormedScores ||
      this.request.includePercentileScores ||
      this.request.includeStenScores ||
      this.request.includeFitScores
    );
  }

  validateErrorsLabel() {
    return [
      !this.validateScoreTypes()
        ? '* At least one score type should be selected'
        : null,
      !this.request.assessments || !this.request.assessments.length
        ? '* At least one assessment should be selected'
        : null,
      this.noParticipants
        ? '* There is no any participants started their assessments to extract data'
        : null,
    ]
      .filter((item) => item)
      .join('<br>');
  }

  toggleRequestFormAssesments(id, append) {
    const index = this.request.assessments.indexOf(id);
    if (append.checked && index < 0) {
      this.request.assessments.push(id);
    }
    if (!append.checked && index >= 0) {
      this.request.assessments.splice(index, 1);
    }
  }

  isAssesmentSelected(id) {
    return this.request.assessments.indexOf(id) >= 0;
  }

  requestExtract() {
    this.spinnerService.activate();

    if (!this.authenticationService.canRequestPersonalData) {
      if (this.request.includePersonalData) {
        console.warn("Notice: you are not authorized to see Personal Data in the data extract.");
      }
      this.request.includePersonalData = false;
    }

    this.projectService.sendExtractRequest(this.request).subscribe(
      (result) => {
        this.onFileComplete(result);
        this.spinnerService.deactivate();
      },
      (error) => {
        this.alertService.error(error.message);
        this.spinnerService.deactivate();
      }
    );
  }

  /** Call back method for file upload completed event */
  onFileComplete(data: any) {
    const name = `${this.client.name} - ${this.project.name} - Extracts.xlsx`;
    const blob = new Blob([data], { type: 'application/vnd.ms-excel' });
    const file = new File([blob], name, {
      type: 'application/vnd.ms-excel',
    });

    this.alertService.success(`${name} has been downloaded`);
    FileSaver.saveAs(file);
  }
}
