import { KFTableColumn } from '@/components/controls';
import {
  CandidateEmailLogRecord,
  CandidateJobRecord,
  CandidateLogRecord,
  Client,
} from '@/shared/models';

export type LogRecord = CandidateEmailLogRecord | CandidateJobRecord | CandidateLogRecord;
export type CandidateLogView = 'log' | 'jobs' | 'emails';

export class CandidateLogComponentContext {
  client: Client;
  participant: any;
  currentView: CandidateLogView = 'emails';
  startDateString: string;
  endDateString: string;
  level: number;
  columns: KFTableColumn<LogRecord>[];
  records: LogRecord[];
  levelOptions: { id: string; value: string }[];
  rowClasses: any;
  selectedLevels: any;
  filteredRecords: any;
  selectedJob: any;
}
