@import "../../../../../styles/colors.scss";
@import "../../../../../styles/theme.scss";
@import "../../../../../styles/typography.scss";

:host {
  flex: 1;

  .current-view {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 12px 24px;

    font-size: 12px;
    font-weight: bold;
    letter-spacing: 1px;
    text-transform: uppercase;

    span {
      color: rgba(0, 0, 0, 0.33);
      padding: 6px 0;
    }
    a {
      padding: 6px 0;
      margin-left: 48px;
      position: relative;

      &.active {
        &::after {
          content: "";
          width: 22px;
          height: 2px;
          position: absolute;
          background: $primary--blue;
          bottom: 3px;
          left: 0;
        }
      }
    }
  }

  .table {
    box-shadow: 0 0 1px rgba(0, 0, 0, 0.3);

    .table-filter-panel {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      background: white;
      border-bottom: solid thin rgba(0, 0, 0, 0.1);
      padding: 12px 24px;

      input[type="date"] {
        padding: 10px;
      }

      button {
        background: #007bc7;
        color: white;
        border-width: 0;
      }

      > * {
        margin-right: 16px;
      }
    }
  }

  .status-icon-container {
    width: 60px;
    display: flex;
    justify-content: center;
    .status {
      display: flex;
      align-items: center;

      mat-icon {
        margin-right: 10px;
      }
    }
    .red {
      color: $secondary--red;
    }
    .yellow {
      color: $secondary--orange;
    }
    .green {
      color: $secondary--green-dark;
    }
    .grey {
      color: $primary--grey !important;
    }
    .blue {
      color: $primary--blue;
    }
  }

  .level {
    margin: -2px -4px;
    padding: 2px 4px;
    border-radius: 3px;

    &.trace {
      color: gray;
    }

    &.debug {
      color: blue;
    }

    &.info {
      color: black;
    }

    &.warn {
      background-color: #dddd09;
    }

    &.error {
      color: red;
    }

    &.fatal {
      background-color: red;
      color: white;
    }

    &.processing {
      color: gray;
    }

    &.completed {
      color: green;
    }

    &.failed,
    &.failandretry {
      color: red;
    }

    &.delayandretry {
      color: darkblue;
    }
  }

  :ng-deep.mat-select-trigger {
    padding: 10px;
  }
}
