import { KFTableColumn } from '@/components/controls';
import {
  CandidateJobDetailsComponent,
  CandidateLogDetailsComponent,
} from '@/components/dialogs';
import { AlertService, ProjectService, SpinnerService } from '@/services';
import {
  CandidateEmailLogRecord,
  CandidateJobRecord,
  CandidateLogRecord,
} from '@/shared/models';
import { NotificationType } from '@/shared/models/NotificationHeader';
import { Location } from '@angular/common';
import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material';
import { ActivatedRoute } from '@angular/router';
import { combineLatest, of } from 'rxjs';
import {
  CandidateLogComponentContext,
  CandidateLogView,
  LogRecord,
} from './candidate-log.component.context';

@Component({
  selector: 'app-candidate-log',
  templateUrl: './candidate-log.component.html',
  styleUrls: ['./candidate-log.component.scss'],
})
export class CandidateLogComponent implements OnInit {
  @ViewChild('BCCSENT_TPL') BCCSENT_TPL: TemplateRef<any>;
  @ViewChild('PRIORITY_TPL') PRIORITY_TPL: TemplateRef<any>;
  @ViewChild('STATUS_TPL') STATUS_TPL: TemplateRef<any>;
  @ViewChild('LEVEL_TPL') LEVEL_TPL: TemplateRef<any>;

  context = new CandidateLogComponentContext();
  NotificationType = NotificationType;

  constructor(
    private route: ActivatedRoute,
    private service: ProjectService,
    private location: Location,
    private spinnerService: SpinnerService,
    private alertService: AlertService,
    public dialog: MatDialog
  ) {}

  ngOnInit() {
    this.route.data.subscribe((resolved) => {
      this.context.client = resolved.client;
      this.context.participant = resolved.participant;
      const view = this.route.snapshot.queryParams
        .currentView as CandidateLogView;
      if (view) {
        this.changeCurrentView(view);
      }
      this.refreshData();
    });
  }

  refreshData() {
    this.spinnerService.activate();
    this.currentRecordsRequest.subscribe(
      (result) => {
        if (this.context.currentView === "emails") {
          result.forEach((v) => {
            v.templateType = this.getNotificationTypeNameByValue(v.templateType);
          });
        }
     
        this.context.records = result;
        this.context.filteredRecords = this.filterRecords(result);
        this.context.columns = this.currentColumns;
        this.context.rowClasses = this.currentRowClasses;
        this.context.levelOptions = this.logsLevelOptions;
      },
      (error) => {
        this.alertService.error(error.message);
      },
      () => {
        this.spinnerService.deactivate();
      }
    );
  }

  showJobDetails(record: CandidateJobRecord) {
    combineLatest(
      this.service.getCandidateJobDetails(
        this.context.participant.userId,
        record.jobId
      ),
      this.service.getCandidateJobLogs(
        this.context.participant.userId,
        record.jobId
      )
    ).subscribe(([recordDetails, jobLogs]) => {
      record.logs = jobLogs;
      record.details = recordDetails;
      this.dialog
        .open(CandidateJobDetailsComponent, {
          width: '90%',
          data: record,
        })
        .afterClosed()
        .subscribe((closedWithRetryRequest) => {
          if (closedWithRetryRequest) {
            this.service
              .retryCandidateJob(record.jobId, record.request.requestorUserId)
              .subscribe(
                (result) => {
                  this.alertService.success(result);
                  this.refreshData();
                },
                (error) => {
                  this.alertService.error(error);
                }
              );
          }
        });
    });
  }

  showLogDetails(record: any) {
    this.dialog.open(CandidateLogDetailsComponent, {
      width: '1250px',
      data: record,
    });
  }

  convertStartDate(dateString: string): Date {
    return new Date(dateString || '01/01/1990');
  }

  convertEndDate(dateString: string): Date {
    const end = new Date(dateString || '01/01/2050');
    end.setUTCHours(23);
    end.setUTCMinutes(59);
    end.setUTCSeconds(59);
    return end;
  }

  changeCurrentView(view: CandidateLogView) {
    if (this.context.currentView !== view) {
      this.context.currentView = view;
      this.updateCurrentUrl();
      this.refreshData();
    }
  }

  updateCurrentUrl() {
    const currentUrl = this.location.path().split('?')[0];
    const params = {
      clientId: this.context.client.id,
      candidateId: this.context.participant.participantId,
      currentView: this.context.currentView,
    };

    const queryString = Object.keys(params)
      .filter((k) => params[k])
      .map((k) => [k, params[k]].join('='))
      .join('&');

    this.location.go(`${currentUrl}?${queryString}`);
  }

  changeLevels(levels) {
    this.context.selectedLevels = levels;
  }

  filterRecords(records) {
    switch (this.context.currentView) {
      case 'log':
        return this.context.selectedLevels &&
          this.context.selectedLevels.length > 0
          ? records.filter(
              (item: CandidateLogRecord) =>
                this.context.selectedLevels.indexOf(item.level.toLowerCase()) >=
                0
            )
          : records || [];
      default:
        return records || [];
    }
  }

  get currentRecordsRequest() {
    const startDate = this.convertStartDate(this.context.startDateString);
    const endDate = this.convertEndDate(this.context.endDateString);
    const userId = this.context.participant.userId;

    switch (this.context.currentView) {
      case 'emails':
        return this.service.getEmailLog(userId, startDate, endDate);
      case 'jobs':
        return this.service.getCandidateJobs(userId, startDate, endDate);
      case 'log':
        return this.service.getCandidateLog(userId, startDate, endDate);
      default:
        of(null);
    }
  }

  get currentColumns(): KFTableColumn<LogRecord>[] {
    switch (this.context.currentView) {
      case 'emails':
        return this.emailsColumns;
      case 'jobs':
        return this.jobsColumns;
      case 'log':
        return this.logColumns;
      default:
        return [];
    }
  }

  get currentRowClasses() {
    switch (this.context.currentView) {
      case 'log':
        return this.logsRowClasses;
      default:
        return null;
    }
  }

  get emailsColumns(): KFTableColumn<CandidateEmailLogRecord>[] {
    return [
      {
        type: 'date',
        name: 'sent',
        label: 'Date sent',
        dateFormat: 'dd MMM yyyy, HH:mm:ss',
        sortable: true,
      },
      {
        type: 'textCapitalized',
        name: 'name',
        label: 'from',
        sortable: false,
      },
      { type: 'text', name: 'email', label: 'email', sortable: false },
      {
        type: 'text',
        name: 'bccSent',
        label: 'bcc sent?',
        width: '100px',
        template: this.BCCSENT_TPL,
      },
      {
        type: 'text',
        name: 'templateType',
        label: 'Email Type',
      },
      { type: 'text', name: 'templateName', label: 'template Name' },
      {
        type: 'text',
        name: 'priority',
        label: 'Priority',
        width: '100px',
        template: this.PRIORITY_TPL,
      },
      {
        type: 'text',
        name: 'status',
        label: 'Status',
        width: '100px',
        sortable: true,
        template: this.STATUS_TPL,
      },
      { type: 'text', name: 'errorMessage', label: 'Error Reason' },
    ];
  }

  get jobsColumns(): KFTableColumn<CandidateJobRecord>[] {
    return [
      {
        type: 'text',
        name: 'jobId',
        label: 'Job ID',
        width: '150px',
        sortable: true,
      },
      {
        type: 'date',
        name: 'received',
        label: 'received',
        width: '250px',
        dateFormat: 'dd MMM yyyy, HH:mm:ss',
        sortable: true,
      },
      {
        type: 'text',
        name: 'status',
        label: 'status',
        width: '150px',
        sortable: true,
        template: this.STATUS_TPL,
      },
      {
        type: 'text',
        name: 'handler',
        path: ['request', 'handler'],
        label: 'Handler',
        sortable: true,
        click: (record: CandidateJobRecord) => {
          this.showJobDetails(record);
        },
      },
      {
        type: 'json',
        name: 'content',
        path: ['request', 'content'],
        label: 'Content',
      },
    ];
  }

  get logColumns(): KFTableColumn<CandidateLogRecord>[] {
    return [
      {
        type: 'text',
        name: 'logId',
        label: 'Log ID',
        sortable: true,
        width: '150px',
      },
      {
        type: 'textCapitalized',
        name: 'level',
        label: 'level',
        width: '150px',
        template: this.LEVEL_TPL,
      },
      {
        type: 'date',
        name: 'created',
        label: 'created',
        width: '250px',
        dateFormat: 'dd MMM yyyy, HH:mm:ss',
        sortable: true,
      },
      {
        type: 'text',
        name: 'message',
        label: 'message',
        click: (record: CandidateLogRecord) => {
          this.showLogDetails(record);
        },
      },
    ];
  }

  get logsRowClasses() {
    return {
      default: true,
      error: (row: CandidateLogRecord) => row.level === 'Error',
      fatal: (row: CandidateLogRecord) => row.level === 'Fatal',
      warn: (row: CandidateLogRecord) => row.level === 'Warn',
    };
  }

  get logsLevelOptions() {
    return [
      { value: 'Completed', id: 'completed' },
      { value: 'Debug', id: 'debug' },
      { value: 'Delay&Retry', id: 'delayandretry' },
      { value: 'Error', id: 'error' },
      { value: 'Failed And Retry', id: 'failandretry' },
      { value: 'Falied', id: 'failed' },
      { value: 'Fatal', id: 'fatal' },
      { value: 'Info', id: 'info' },
      { value: 'Processing', id: 'processing' },
      { value: 'Trace', id: 'trace' },
      { value: 'Warn', id: 'warn' },
    ];
  }
  getNotificationTypeNameByValue(value: number): string | undefined {
    const enumKeys = Object.keys(NotificationType).filter((key) => isNaN(Number(key)));
    const key = enumKeys.find((key) => NotificationType[key as keyof typeof NotificationType] === value);
    return key;
  }
}
