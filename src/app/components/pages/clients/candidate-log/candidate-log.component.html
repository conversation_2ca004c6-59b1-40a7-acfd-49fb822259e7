<div class="page-content glass">
  <div class="current-view">
    <span>Current view: </span>
    <a
      [class.active]="context.currentView === 'log'"
      (click)="changeCurrentView('log')"
      >Logs</a
    >
    <a
      [class.active]="context.currentView === 'jobs'"
      (click)="changeCurrentView('jobs')"
      >Jobs</a
    >
    <a
      [class.active]="context.currentView === 'emails'"
      (click)="changeCurrentView('emails')"
      >Emails log</a
    >
  </div>

  <div class="table">
    <div class="table-filter-panel">
      <span>Filter from:</span>
      <input
        [class.grey]="!context.startDateString"
        type="date"
        [(ngModel)]="context.startDateString"
      />
      <span> to: </span>
      <input
        [class.grey]="!context.endDateString"
        type="date"
        [(ngModel)]="context.endDateString"
      />

      <ng-container *ngIf="context.currentView === 'log'">
        <kf-select
          placeholder="Levels"
          [selection]="context.selectedLevels || []"
          (selectionChange)="changeLevels($event)"
          [options]="context.levelOptions"
        ></kf-select>
      </ng-container>

      <button class="btn" (click)="refreshData()">filter</button>
    </div>

    <kf-table
      *ngIf="context.currentView && context.filteredRecords"
      [data]="context.filteredRecords || []"
      [columns]="context.columns"
      [rowClasses]="context.rowClasses"
    ></kf-table>
  </div>
</div>

<ng-template #BCCSENT_TPL let-cell>
  <div
    class="status-icon-container"
    [matTooltip]="cell.element.bccSent ? 'Yes' : 'No'"
  >
    <mat-icon class="grey" *ngIf="cell.element.bccSent">done</mat-icon>
  </div>
</ng-template>

<ng-template #STATUS_TPL let-cell>
  <div class="status-icon-container" [matTooltip]="cell.element.status">
    <span
      class="status"
      *ngIf="
        cell.element.status === 'Successful' ||
        cell.element.status === 'Completed'
      "
    >
      <mat-icon class="green">done</mat-icon>
    </span>
    <span
      class="status"
      *ngIf="
        cell.element.status === 'None' || cell.element.status === 'Processing'
      "
    >
      <mat-icon class="yellow">schedule</mat-icon>
    </span>
    <span
      class="status"
      *ngIf="
        cell.element.status === 'Error' || cell.element.status === 'Failed'
      "
    >
      <mat-icon class="red">close</mat-icon>
    </span>
  </div>
</ng-template>

<ng-template #PRIORITY_TPL let-cell>
  <div class="status-icon-container" [matTooltip]="cell.element.priority">
    <mat-icon class="red" *ngIf="cell.element.priority === 'High'"
      >priority_high</mat-icon
    >
    <mat-icon class="blue" *ngIf="cell.element.priority === 'Low'"
      >arrow_downward</mat-icon
    >
  </div>
</ng-template>

<ng-template #LEVEL_TPL let-cell>
  <span class="level {{ cell.element.level | lowercase }}">
    {{ cell.element.level }}
  </span>
</ng-template>
