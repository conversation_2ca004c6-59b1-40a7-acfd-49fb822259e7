import { KFTableAction } from '@/components/controls';
import { AlertService, NormService, SpinnerService } from '@/services';
import { Client, CustomNormDescription } from '@/shared/models';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

// Component for client norms display
@Component({
  selector: 'app-client-norms',
  templateUrl: './client-norms.component.html',
  styleUrls: ['./client-norms.component.scss'],
  providers: [NormService],
})
export class ClientNormsComponent implements OnInit {
  client: Client;
  filter = '';
  columns = [
    {
        name: 'normNo',
        label: 'ID',
        type: 'text',
        width: '65px'
    },
    {
        name: 'text',
        label: 'NORM NAME',
        type: 'text',
        sortable: 'true'
    }
  ];
  actions: KFTableAction<CustomNormDescription>[] = [];
  tableData: CustomNormDescription[];

  constructor(
    private normService: NormService,
    private spinnerService: SpinnerService,
    private alertService: AlertService,
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit() {
    this.route.data.subscribe(
      (result) => {
        this.client = result.client;
      },
      (error) => {
        this.alertService.error(error.message);
      }
    );

    this.refreshTable();

    this.columns.forEach((column: any) => {
      if (column.name === 'text') {
        column.click = (t: any) =>
          this.router.navigate(['../norm-details'], {
            relativeTo: this.route,
            queryParams: {
              clientId: t.clientId,
              normNo: t.normNo,
            },
          });
        column.url = (t: any) =>
          this.router
            .createUrlTree(['../norm-details'], {
              relativeTo: this.route,
              queryParams: {
                clientId: t.clientId,
                normNo: t.normNo,
              },
            })
            .toString();
      }
      if (this[`${column.name.toUpperCase()}_TPL`]) {
        column.template = this[`${column.name.toUpperCase()}_TPL`];
      }
    });
  }

  refreshTable() {
    this.spinnerService.activate();

    this.normService.getClientCustomNormDescriptions(this.client.id).subscribe(
      result => {
        this.tableData = result.sort((a, b) => a.displayOrder - b.displayOrder).slice();
        this.spinnerService.deactivate();
      },
      error => {
        this.spinnerService.deactivate();
        this.alertService.error(error.message);
      },
    );
  }

  createNewNorm() {
    this.router.navigate(['../norm-details'], {
      relativeTo: this.route,
      queryParams: {
        clientId: this.client.id,
        normNo: -1
      },
    });
  }
}
