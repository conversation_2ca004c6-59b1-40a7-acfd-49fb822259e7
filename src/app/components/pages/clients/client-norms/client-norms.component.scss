@import "../../../../../styles/colors.scss";
@import '../../../../../styles/typography.scss';

.form-title {
  vertical-align: top;
  color: white;
  font-size: 13px;
  margin-bottom: 12px;
  opacity: .85;

  mat-icon {
    font-size: 20px;
    line-height: 20px;
    height: 20px;
  }
}

.table-container {
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
}

.table-actions-panel {
  display: flex;
  flex-direction: row;
  justify-content: stretch;
  align-items: center;
  user-select: none;
  margin-bottom: 16px;

  input[type='search'] {
    width: 250px;
  }

  input.with-icon {
    flex-grow: 1;
    padding-left: 48px;
  }

  .input-icon {
    z-index: 1;
    width: 0;
    transform: translateX(12px);
    color: #007da4;
  }

  a {
    margin: 0 0 0 12px;
    color: white;
    box-sizing: border-box;
    line-height: 22px;
  }
}
