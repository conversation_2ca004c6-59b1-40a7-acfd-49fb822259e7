import { KFTableColumn } from '@/components/controls/kf-table/kf-table.component';
import { ParticipantDetailsInfoComponent } from '@/components/dialogs/participant-details-info/participant-details-info.component';
import { AlertService } from '@/services';
import { ProjectService } from '@/services/project.service';
import { Client, ProjectDetails } from '@/shared/models';
import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material';
import { ActivatedRoute, Router } from '@angular/router';

/**
 * Project participants component.
 */
@Component({
  selector: 'app-candidate-details',
  templateUrl: './candidate-details.component.html',
  styleUrls: ['./candidate-details.component.scss'],
})
export class CandidateDetailsComponent implements OnInit {
  fields = [
    { name: 'forename', label: 'First Name' },
    { name: 'surname', label: 'Last Name' },
    { name: 'username', label: 'Username' },
    { name: 'email', label: 'Email Address' },
    { name: 'dateCreated', label: 'Date Created' },
    { name: 'createdBy', label: 'Created By' },
    { name: 'lastAccessed', label: 'Last Logged In' },
    { name: 'prefferedLanguage', label: 'Preferred Language' },
    { name: 'atsPlatform', label: 'ATS Platform' },
    { name: 'exReference', label: 'External Reference' },
  ];
  techFields = [
    { name: 'userId', label: 'User ID' },
    { name: 'personId', label: 'Person ID' },
    { name: 'participantId', label: 'Participant ID' },
    { name: 'clientOwnerId', label: 'Client Owner ID' },
    { name: 'ssoProviderUserId', label: 'SSO Provider User Id' },
    { name: 'createdViaSelfService', label: 'Created Via Self Service' },
    { name: 'exURLRedirectMode', label: 'External Redirect Mode' },
    { name: 'exURL', label: 'External Redirect' },
    { name: 'iC2EmployeeId', label: 'IC2 Employee ID' },
  ];
  columns: KFTableColumn<any>[] = [
    {
      type: 'text',
      name: 'projectId',
      label: 'ID',
      click: (item) => this.goToProjectDetails(item.projectId),
      width: '100px',
    },
    {
      type: 'text',
      name: 'projectName',
      label: 'project name',
      click: (item) => this.goToProjectDetails(item.projectId),
    },
    { type: 'date', name: 'addedDate', label: 'date' },
    { type: 'text', name: 'type', label: 'type' },
    { type: 'text', name: 'CHILD_PROJECTS', label: 'Child projects' },
    {
      type: 'text',
      name: 'COMPLETED_TOTAL',
      label: 'Assessments',
      click: (t: ProjectDetails) => {
        this.showAssessmentsModal(t);
      },
    },
    { type: 'text', name: 'OPTIONS_MENU', label: 'Actions' },
  ];
  @ViewChild('CHILD_PROJECTS_TPL') CHILD_PROJECTS_TPL: TemplateRef<any>;
  @ViewChild('COMPLETED_TOTAL_TPL') COMPLETED_TOTAL_TPL: TemplateRef<any>;
  @ViewChild('OPTIONS_MENU_TPL') OPTIONS_MENU_TPL: TemplateRef<any>;

  client: Client;
  participant: any;
  projects: any;

  constructor(
    private route: ActivatedRoute,
    public projectService: ProjectService,
    public alertService: AlertService,
    public dialog: MatDialog,
    private router: Router
  ) {}

  ngOnInit() {
    this.route.data.subscribe((resolved) => {
      this.client = resolved.client;
      this.participant = resolved.candidate;

      this.participant.projects.forEach((item) => {
        item.type = `${item.productHubType} - ${item.projectType}`;
        item.completed = item.assessments.filter(
          (a) => a.status === 'Completed'
        ).length;
        item.total = item.assessments.length;
      });

      this.projects = this.participant.projects;
    });

    this.columns.forEach((column) => {
      column.template = this[`${column.name}_TPL`];
    });
  }

  fieldValueOf(field: { label: string; name: string }) {
    const value = this.participant[field.name];
    return value ? value.toString() : '-';
  }

  fieldKeyOf(field: { label: string; name: string }) {
    return field.label;
  }

  showAssessmentsModal(project) {
    const dialogRef = this.dialog.open(ParticipantDetailsInfoComponent, {
      width: '65%',
      data: project,
    });

    dialogRef.afterClosed().subscribe((result: string) => {
      if (result) {
        this.goToProjectDetails(result);
      }
    });
  }

  goToManageAssessments(clientId, projectId, candidateId) {
    this.router.navigate(['/clients/candidate-manage-assessments'], {
      queryParams: {
        projectId,
        candidateId,
        clientId,
      },
    });
  }

  goToProjectDetails(projectId) {
    this.router.navigate(['/clients/project-details'], {
      queryParams: { projectId, clientId: this.client.id, selectedProjectId: '' },
    });
  }

  goToEmailsLog() {
    this.router.navigate(['/clients/candidate-log'], {
      queryParams: {
        clientId: this.client.id,
        candidateId: this.participant.participantId,
      },
    });
  }

  goToLogsAndJobs() {
    this.router.navigate(['/clients/candidate-log'], {
      queryParams: {
        clientId: this.client.id,
        candidateId: this.participant.participantId,
      },
    });
  }

  getCompletedAssessmentsCount(projectItem) {
    return projectItem.assessments.filter((a) => a.status === 'Completed').length;
  }

  getTotalAssessmentsCount(projectItem) {
    return projectItem.assessments.length;
  }

  canBeRegenerated(projectItem) {
    const completed = this.getCompletedAssessmentsCount(projectItem);
    const total = this.getTotalAssessmentsCount(projectItem);
    return (completed === total) && completed > 0;
  }

  regenerateSelected(projectId, candidateId) {
    this.projectService
      .regenerateAllReports(projectId, [candidateId], null, false)
      .subscribe(
        (result) => {
          this.alertService.success(result);
        },
        (error) => {
          this.alertService.error(error.message);
        }
      );
  }

  showReports(clientId, projectId, candidateId) {
    this.router.navigate(['clients/candidate-report-status'], {
      queryParams: {
        projectId,
        clientId,
        candidateId,
      },
    });
  }
}
