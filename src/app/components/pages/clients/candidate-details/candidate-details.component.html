<div class="page-content strong-glass">
  <div class="participant-info">
    <div>
      <table>
        <tr *ngFor="let field of fields">
          <td>{{ fieldKeyOf(field) }}:</td>
          <td>
            <b>{{ fieldValueOf(field) }}</b>
          </td>
        </tr>
        <tr>
          <td>Participant log:</td>
          <td>
            <a (click)="goToLogsAndJobs()"> <b>Show participant Log</b> </a>
          </td>
        </tr>
      </table>
    </div>
    <div>
      <h3>Technical Information</h3>
      <table>
        <tr *ngFor="let field of techFields">
          <td>{{ fieldKeyOf(field) }}:</td>
          <td>
            <b>{{ fieldValueOf(field) }}</b>
          </td>
        </tr>
      </table>
    </div>
  </div>
  <div class="table-container">
    <kf-table
      *ngIf="projects"
      [data]="projects"
      [columns]="columns"
      [actions]="actions"
      [expandable]="true"
    ></kf-table>
  </div>
</div>

<ng-template #COMPLETED_TOTAL_TPL let-cell>
  <span>
    {{ getCompletedAssessmentsCount(cell.element) }}/{{
      getTotalAssessmentsCount(cell.element)
    }}
  </span>
</ng-template>

<ng-template #CHILD_PROJECTS_TPL let-cell>
  <div *ngIf="cell.element.childProjectIds.length">
    <a class="toggle-child-projects" (click)="cell.expand = !cell.expand">
      <span *ngIf="cell.expand">Hide ▲</span>
      <span *ngIf="!cell.expand">Show ▼</span>
    </a>

    <div *ngIf="cell.expand">
      <div *ngFor="let id of cell.element.childProjectIds">
        <a (click)="goToProjectDetails(id)">{{ id }}</a>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #OPTIONS_MENU_TPL let-cell>
  <mat-menu #optionsMenu="matMenu" class="menu">
    <button
      mat-menu-item
      (click)="
        goToManageAssessments(
          client.id,
          cell.element.projectId,
          participant.participantId
        )
      "
    >
      <mat-icon>assessment</mat-icon>
      Manage Assessments
    </button>
    <button
      mat-menu-item
      *ngIf="canBeRegenerated(cell.element)"
      (click)="
        regenerateSelected(cell.element.projectId, participant.participantId)
      "
    >
      <mat-icon>cached</mat-icon>
      Regenerate Reports
    </button>
    <button
      mat-menu-item
      *ngIf="canBeRegenerated(cell.element)"
      (click)="
        showReports(
          client.id,
          cell.element.projectId,
          participant.participantId
        )
      "
    >
      <mat-icon>file_download</mat-icon>
      Reports Status
    </button>
  </mat-menu>

  <button class="options-button" [matMenuTriggerFor]="optionsMenu">
    <mat-icon>more_horiz</mat-icon>
  </button>
</ng-template>
