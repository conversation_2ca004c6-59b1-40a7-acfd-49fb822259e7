@import '../../../../../styles/colors.scss';
@import '../../../../../styles/theme.scss';
@import '../../../../../styles/typography.scss';

:host {
  flex: 1;

  page-header {
    padding-left: 0;
    padding-right: 0;
  }

  .participant-info {
    display: flex;
    flex-direction: row;
    justify-content: stretch;
    align-items: flex-start;
    padding: 48px;

    >div {
      width: 50%;
    }

    h3 {
      margin: 0;
      margin-bottom: 12px;
    }

    table {
      tr {
        td {
          vertical-align: top;
          padding: 3px 24px 3px 0;
        }
      }
    }
  }
}

.toggle-child-projects {
  font-size: 12px;
}

.options-button {
  background: transparent;
  color: $primary--blue-medium;
  padding-left: 0;
  border-width: 0;
  outline: none;
}

.table-container {
  border-top: solid thin $primary--grey-medium;
}

::ng-deep .mat-column-actions>button.flat {
  padding: 6px !important;
  background: transparent !important;
  border-width: 0 !important;

  >mat-icon {
    margin-right: 0 !important;
  }
}

