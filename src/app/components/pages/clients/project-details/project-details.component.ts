import { EditNormComponent } from '@/components/dialogs/edit-norm/edit-norm.component';
import { EmailTemplateComponent } from '@/components/dialogs/email-template/email-template.component';
import { SelectCustomNormComponent } from '@/components/dialogs/selectCustomNorm/select-custom-norm.component';
import { SpDetailsComponent } from '@/components/dialogs/sp-details/sp-details.component';
import { AlertService, AuthenticationService, NormService, ProjectService, SpinnerService } from '@/services';
import { Ability, ClientAssessmentNorms, NormOption, ProjectAssessments } from '@/shared/models';
import { AssessmentByIdPipe, AssessmentType, isCognitiveAbilityAssessment, LanguagePipe, LevelsPipe } from '@/shared/pipes';
import { Component, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material';
import { ActivatedRoute } from '@angular/router';
import { ProjectDetailsComponentContext } from './project-details.component.context';

@Component({
  selector: 'app-project-details',
  templateUrl: './project-details.component.html',
  styleUrls: ['./project-details.component.scss'],
})
export class ProjectDetailsComponent implements OnInit {
  public context = new ProjectDetailsComponentContext();
  private customNorms: ClientAssessmentNorms[];
  private isAdmin: boolean;
  projStatus: string = undefined;

  constructor(
    private route: ActivatedRoute,
    private projectService: ProjectService,
    private spinnerService: SpinnerService,
    private levels: LevelsPipe,
    private languages: LanguagePipe,
    private assessmentsPipe: AssessmentByIdPipe,
    private alertService: AlertService,
    private normsService: NormService,
    private authenticationService: AuthenticationService,
    public dialog: MatDialog
  ) { }

  ngOnInit() {
    this.route.queryParams.subscribe((params) => {
      this.projStatus = params.status ? params.status : "";
    });
    this.route.data.subscribe(
      (resolved) => {
        this.context.client = resolved.client;
        this.context.project = resolved.project;
        this.spinnerService.deactivate();
      },
      (error) => {
        this.alertService.error(error.message);
        this.spinnerService.deactivate();
      }
    );

    if (this.authenticationService.hasRole('admin')) {
      this.isAdmin = true;
      this.normsService.getClientAssessmentNorms(this.context.client.id).subscribe(
        (resolved) => {
          this.customNorms = resolved.norms;
        },
        (error) => {
          this.alertService.error(error.message);
          this.spinnerService.deactivate();
        }
      );
    }
  }

  get normDisplay() {
    return this.context.project.norm
      ? `${this.context.project.norm.normCountry} ${this.context.project.norm.normVersion}`
      : '';
  }

  get generalInfo() {
    return [
      { label: 'Project ID', value: this.context.project.projectId },
      { label: 'Project Name', value: this.context.project.name },
      { label: 'Product Type', value: this.context.project.productType },
      { label: 'Project Type', value: this.context.project.projectType },
      {
        label: 'Owner Name',
        value: this.toTitleCase(this.context.project.ownerDisplayName),
      },
      {
        label: 'Location of',
        value: this.context.project.country || 'United States of America',
        isEditable: this.isLocationEditable(),
      },
      {
        label: 'Default Language',
        value: this.languages.transform(this.context.project.locale),
      },
      {
        label: 'Norm Group',
        value: this.normDisplay,
        isEditable: this.isNormEditable(),
      },
      { label: 'User Group', value: this.context.project.userGroup },
      { label: 'SSO Setting', value: this.context.project.ssoSetting },
    ];
  }

  get settings() {
    return [
      {
        label: 'Current Level',
        value: this.levels.transform(this.context.project.currentLevel),
        type: 'text',
        display: this.context.project.currentLevel,
      },
      {
        label: 'Target Level',
        value: this.levels.transform(this.context.project.targetLevel),
        type: 'text',
        display: this.context.project.targetLevel,
      },
      {
        label: 'Created Date',
        value: this.context.project.createdDateTime,
        type: 'date',
        display: true,
      },
      {
        label: 'End Date',
        value: this.context.project.endDateTime,
        type: 'date',
        display: true,
      },
      {
        label: 'Include Fit Score?',
        value: this.context.project.includeFitScore,
        type: 'bool',
        display: true,
      },
      {
        label: 'Score Display',
        value: this.context.project.scoreDisplay,
        type: 'text',
        display: this.showFitScoreDisplayType,
      },
      {
        label: 'Include Derailers?',
        value: this.context.project.includeDerailers,
        type: 'bool',
        display:
          this.context.project.currentLevel || this.context.project.targetLevel,
      },
      {
        label: 'Allow Participants to See Feedback?',
        value: this.context.project.candidatesAccessScores,
        type: 'bool',
        display: true,
      },
      {
        label: 'Send Completion Notifications?',
        value: this.context.project.completionNotifications,
        type: 'bool',
        display: true,
      },
      {
        label: 'Override Data Protection Notice?',
        value: this.context.project.overrideDataProtectionNotice,
        type: 'bool',
        display: true,
      },
      {
        label: 'Allow participants to reuse existing assessment results?',
        value: this.context.project.allowAssessmentReuse,
        type: 'bool',
        display: true,
      },
      {
        label: 'Allow eLearning Behavioral Competency Access',
        value: this.context.project.candidatesAccessLearningContent,
        type: 'bool',
        display: this.context.project.projectType === 'PROFESSIONAL_DEVELOPMENT',
      },
      {
        label: 'Include Learning Agility Coaching?',
        value: this.context.project.includeLearningAgility,
        type: 'bool',
        display: this.context.project.productType === 'ASSESS',
      },
      {
        label: 'Version',
        value: this.context.project.isKFAssess2,
        type: 'bool',
        display: true,
      },
    ].filter((item) => item.display);
  }

  get emailTemplates() {
    return [
      {
        typeLabel: 'Default Invitation',
        header: this.context.project.defaultNotificationTemplateHeader,
      },
      {
        typeLabel: 'Default Reminder',
        header: this.context.project.reminderNotificationTemplateHeader,
      },
      {
        typeLabel: 'Default SSO Invitation',
        header: this.context.project.invitationSsoTemplateHeader,
      },
    ];
  }

  // see https://wiki-jirait.kornferry.com/display/~<EMAIL>/June+2020+Support+Portal+Enhancements
  get showSucessProfile() {
    return true; // this.projectType !== 'POTENTIAL';
  }

  get showLevelsAndDerailers() {
    // TODO don't show it for leadership without potential
    return (
      this.context.project.projectType === 'POTENTIAL' ||
      this.context.project.projectType === 'LEADERSHIP' ||
      (this.context.project.currentLevel && this.context.project.targetLevel)
    );
  }

  get showFitScoreDisplayType() {
    return this.context.project.projectType === 'SJT';
  }

  get showPlatform() {
    // only for ASSESS
    return true; // ['LEADERSHIP', 'POTENTIAL', 'DEVELOPMENT'].find(x => x === this.projectType);
  }

  get measuredAssessments() {
    return this.context.project.assessments
      ? Object.keys(this.context.project.assessments)
        .filter(
          (x) =>
            this.context.project.assessments[x] &&
            this.context.project.assessments[x].measure
        )
        .map((key) =>
          key === "sjt"
            ? key
            : parseInt(this.context.project.assessments[key].assessmentId, 10)
        )
        .map(id => ({ id, name: this.assessmentsPipe.transform(id) }))
        .sort((a, b) => a.name < b.name ? -1 : a.name > b.name ? 1 : 0)
      : [];
  }

  getCustomNormDescriptionForAssessment(assessmentType: AssessmentType): NormOption {

    const assessment = this.getAbilty(this.context.project.assessments, assessmentType);

    if (assessment == null || assessment.customNormNo == null || this.customNorms === undefined || this.customNorms.length == 0) {
      return null;
    }

    let normDescriptions = this.customNorms.find(c => c.kfasAssessmentId == assessmentType);
    let normDescription = normDescriptions.norms.find(n => n.normNo == assessment.customNormNo);
    return normDescription;
  }

  getAbilty(assessments: ProjectAssessments, assessmentType: AssessmentType): Ability {

    var keys = Object.keys(assessments);
    let key = keys.find(k => assessments[k] != null && assessments[k].assessmentId == assessmentType && assessments[k] as Ability != null);

    return key != null ? (assessments[key] as Ability) : null;
  }

  getAssessmentCustomNormTitle(assessmentType: AssessmentType): string {
    if (!isCognitiveAbilityAssessment(assessmentType)) {
      return '';
    }

    var customNormDescription = this.getCustomNormDescriptionForAssessment(assessmentType);

    if (customNormDescription != null) {
      return customNormDescription.text;
    }

    return 'Korn Ferry Norms';
  }

  selectCustomNorm(assessmentType: AssessmentType) {
    var customNormDescription = this.getCustomNormDescriptionForAssessment(assessmentType);
    var norms = this.customNorms.find(c => c.kfasAssessmentId == assessmentType);

    if (norms === undefined || norms === null) {
      return;
    }

    this.dialog.open(SelectCustomNormComponent, {
      width: '50%',
      data: {
        contextProject: this.context.project,
        customNorms: norms.norms,
        assessmentType: assessmentType,
        initialNormNoValue: customNormDescription != null ? customNormDescription.normNo : null
      }
    });
  }

  toTitleCase(str) {
    return str.replace(/\w\S*/g, function (txt) {
      return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
    });
  }

  openSuccessProfileDetails(spDetails) {
    this.dialog.open(SpDetailsComponent, {
      width: '1250px',
      data: spDetails,
    });
  }

  openEmailTemplate(template) {
    this.projectService
      .getNotificationTemplate(template.id, this.context.project.clientId)
      .subscribe((result) => {
        this.dialog.open(EmailTemplateComponent, {
          width: '66%',
          data: result, // TODO use actual template
        });
      });
  }

  isCustomNormDisabled() {
    return this.customNorms === undefined || this.customNorms.length === 0;
  }

  private isLocationEditable() {
    return !this.context.project.isChildProject && this.isAdmin;
  }

  private isNormEditable() {
    return (
      !this.context.project.isChildProject &&
      this.context.project.successProfileId !== null &&
      this.projectAllowsEditNorm() && this.isAdmin
    );
  }

  private projectAllowsEditNorm() {
    const projectType = this.context.project.projectType.toUpperCase();

    if (projectType === 'SJT') return false; // Select SJT projects should not allow changes of norms
    if (projectType === 'ENTRY') return false; // Select Entry Level projects should only allow the Global 2020 norm
    if (projectType === 'POTENTIAL') return false; // Assess Potential projects do not use Norms

    return true;
  }

  editNorm() {
    this.dialog.open(EditNormComponent, {
      width: '50%',
      data: {
        projStatus: this.projStatus,
        contextProject: this.context.project,
        isNormEditable: this.isNormEditable(),
      },
    });
  }
}
