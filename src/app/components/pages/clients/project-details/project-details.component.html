<div class="details-page-content white-glass">
  <div class="row">
    <div class="col">
      <div class="group-title">General Information</div>
      <div class="group" *ngFor="let item of generalInfo">

        <div class="label">{{ item.label }}</div>

        <div class="value">
          <ng-container *ngIf="item.isEditable">
            <a class="flat" [allowedRoles]="['admin', 'productDelivery', 'projectParticipantManagement']" (click)="editNorm()">
              <mat-icon>edit</mat-icon>
              {{ item.value || "-" }}
            </a>
          </ng-container>
          <ng-container *ngIf="!item.isEditable">
              {{ item.value || "-" }}
          </ng-container>
        </div>

      </div>
      <div class="group" *ngIf="context.project.platform">
        <div class="label">Platform</div>
        <div class="value">{{ context.project.platform }}</div>
      </div>
    </div>
    <div class="col">
      <div class="group-title">Settings</div>
      <div class="group" *ngFor="let item of settings">
        <div class="label long">{{ item.label }}</div>
        <div class="value">
          <span *ngIf="item.type === 'text'">
            {{ item.value }}
          </span>
          <span *ngIf="item.type === 'date'">
            {{ item.value ? (item.value | date: "d MMM yyyy") : "No end date" }}
          </span>
          <span *ngIf="item.type === 'bool' && item.label === 'Version'">
            {{ item.value ? "2.0" : "1.0" }}
          </span>
          <span *ngIf="item.type === 'bool'  && item.label !== 'Version'">
            {{ item.value ? "Yes" : "No" }}
          </span>
          

        </div>
      </div>
      <div class="group assessments">
        <div class="label long group-title">Assessments</div>
        <div class="group-title">Custom Norm</div>
      </div>
      <div
        class="group assessments"
        *ngFor="let assessment of measuredAssessments"
      >
        <div class="label long">{{ assessment.name }}</div>
        <div
          class="value assessmentCellAssessment"
          [allowedRoles]="['admin', 'productDelivery', 'projectParticipantManagement']"
        >
          <a
            class="assessmentLinkAssessment"
            [class.disabled]="isCustomNormDisabled()"
            (click)="selectCustomNorm(assessment.id)"
            *ngIf="getAssessmentCustomNormTitle(assessment.id)"
          >
            <mat-icon *ngIf="!isCustomNormDisabled()">edit</mat-icon>
            {{ getAssessmentCustomNormTitle(assessment.id) }}
          </a>

          <span class="gray" *ngIf="!getAssessmentCustomNormTitle(assessment.id)">
            N/A
          </span>
        </div>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col">
      <div class="group-title">Email Schedules</div>
      <div class="email-template">
        <div class="email-template-label">
          <a
            class="preview"
            [routerLink]="['/clients', 'project-email-schedules']"
            [queryParams]="{
              clientId: context.client.id,
              projectId: context.project.projectId
            }"
          >
            Go to Email schedules
          </a>
        </div>
      </div>
    </div>
    <div class="col" *ngIf="
        context.project.successProfiles && context.project.successProfiles[0]
      ">
      <table>
        <tr>
          <th>
            <div class="group-title">Success Profiles</div>
          </th>
          <th class="customized">
            <div class="group-title">Customised?</div>
          </th>
        </tr>
        <tr *ngFor="let sp of context.project.successProfiles">
          <td>
            <div class="sp-name">
              <a class="preview" (click)="openSuccessProfileDetails(sp)">
                {{ sp.name }}
              </a>

              <span style="font-weight: normal"> (ID {{ sp.id }}) </span>
            </div>
          </td>
          <td class="customized">
            <b>{{ sp.successProfileCustomized ? "Yes" : "No" }}</b>
          </td>
        </tr>
      </table>
    </div>
  </div>
</div>
