<div class="page-content">
  <div class="glass">
    <div class="table-actions-panel">
      <div class="left-side">
        <a 
          [class.active]="context.currentTab === ClientDetailsTab.GENERAL_INFO"
          (click)="setCurrentTab(ClientDetailsTab.GENERAL_INFO)"
        >General Info</a>
        <a 
          [allowedRoles]="['admin']"
          [class.active]="context.currentTab === ClientDetailsTab.PROJECT_DEFAULT_SETTINGS"
          (click)="setCurrentTab(ClientDetailsTab.PROJECT_DEFAULT_SETTINGS)"
        >Project Default Settings</a>
        <a 
          [allowedRoles]="['admin']"
          [class.active]="context.currentTab === ClientDetailsTab.ASSESSMENT_VALIDITY_PERIODS"
          (click)="setCurrentTab(ClientDetailsTab.ASSESSMENT_VALIDITY_PERIODS)"
        >Assessment Validity Periods</a>
        <input 
          class="glass" 
          type="search" 
          [(ngModel)]="context.assessmentValidityPeriodFilter"
          placeholder="Search period" 
          *ngIf="context.currentTab === ClientDetailsTab.ASSESSMENT_VALIDITY_PERIODS"
          />
      </div>
      <div class="right-side" *ngIf="context.currentTab === ClientDetailsTab.ASSESSMENT_VALIDITY_PERIODS">
        <a
          *ngIf="!context.periodsInheritedFromParent"
          (click)="addValidityPeriod()"
         >
          <mat-icon>add</mat-icon>
          <b>Add new period</b>
        </a>
        <a
          *ngIf="context.periodsInheritedFromParent"
          (click)="overrideInheritedValidityPeriods()"
        >
          <mat-icon>settings_backup_restore</mat-icon>
          <b>Override parent filters</b>
        </a>
      </div>
    </div>

    <div class="white-glass">
      <div class="content" *ngIf="context.currentTab === ClientDetailsTab.GENERAL_INFO">
        <app-client-general-info
          [client]="context.client"
        ></app-client-general-info>
      </div>
      <div class="content" *ngIf="context.currentTab === ClientDetailsTab.PROJECT_DEFAULT_SETTINGS">
        <app-client-default-settings
          [client]="context.client"
          [notifications]="context.notifications"
          (clientUpdateSubmitted)="updateClient($event)"
        ></app-client-default-settings>
      </div>
      <div *ngIf="context.currentTab === ClientDetailsTab.ASSESSMENT_VALIDITY_PERIODS">
        <app-assessment-validity-period
          [filter]="context.assessmentValidityPeriodFilter"
          [validityPeriods]="context.validityPeriods"
          (onEditPeriodClicked)="editValidityPeriod($event)"
          (onDeletePeriodClicked)="deleteValidityPeriod($event)"
        ></app-assessment-validity-period>
      </div>
    </div>
  </div>
</div>