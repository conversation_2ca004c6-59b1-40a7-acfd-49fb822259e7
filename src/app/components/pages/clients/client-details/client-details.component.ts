import { ActivatedRoute } from '@angular/router';
import { AlertService, AuthenticationService, ClientSearchService, SpinnerService } from '@/services';
import { AssessmentType } from "@/shared/pipes";
import { AssessmentValidityPeriod, AssessmentValidityPeriodDataWrapper, AssessmentValidityPeriodType, } from "@/shared/models/assessment-validity-period/assessment-validity-period";
import { AssessmentValidityPeriodService } from "@/services/assessment-validity-period.service";
import { ClientDetailsComponentContext, ClientDetailsTab } from './client-details.component.context';
import { Component, OnInit } from '@angular/core';
import { ConfirmationDialogComponent } from '@/components/controls/confirmation-dialog/confirmation-dialog.component';
import { MatDialog } from "@angular/material";
import { ValidityPeriodDialogComponent } from "@/components/dialogs/validity-period-dialog/validity-period-dialog.component";

/**
 * Project participants component.
 */
@Component({
  selector: 'app-client-details',
  templateUrl: './client-details.component.html',
  styleUrls: ['./client-details.component.scss'],
})
export class ClientDetailsComponent implements OnInit {
  context = new ClientDetailsComponentContext();

  // enum collection
  ClientDetailsTab = ClientDetailsTab;

  constructor(
    private alertService: AlertService,
    private assessmentValidityPeriodService: AssessmentValidityPeriodService,
    private authenticationService: AuthenticationService,
    private clientService: ClientSearchService,
    private dialog: MatDialog,
    private route: ActivatedRoute,
    private spinnerService: SpinnerService
  ) {
  }

  ngOnInit() {
    this.route.data.subscribe((resolved) => {
      this.context.client = resolved.client;
      this.context.notifications = resolved.notifications;
    });

    if (this.authenticationService.hasRole('admin')) {
      this.refreshValidityPeriod();
    }
  }

  getAssessmentTypes(excludedAssessmentTypes: AssessmentType[]): AssessmentType[] {
    return Object.keys(AssessmentType).filter(k => excludedAssessmentTypes == null || excludedAssessmentTypes.indexOf(AssessmentType[k]) < 0).map(k => AssessmentType[k]);
  }

  setCurrentTab(tab) {
    this.context.currentTab = tab;
  }

  updateClient(updatedClient) {
    this.clientService.updateClient(updatedClient).subscribe(result => {
      this.alertService.success(result);
      this.context.client = Object.assign({}, updatedClient);
    },
    error => {
      this.alertService.error(error.message || error);
    });
  }

  openDialog(data: AssessmentValidityPeriodDataWrapper) {
    const dialogRef = this.dialog.open(ValidityPeriodDialogComponent, data);
    dialogRef.afterClosed()
      .subscribe(period => this.savePeriod(period));
  }

  savePeriod(period: AssessmentValidityPeriod) {
    if (!period) {
      return;
    }

    this.assessmentValidityPeriodService.saveAssessmentValidityPeriod(period).subscribe(
      () => {
        this.alertService.success('Assessment validity period saved');
        this.refreshValidityPeriod();
      },
      error => {
        this.alertService.error(error.message);
      }
    );
  }

  refreshValidityPeriod() {
    this.spinnerService.activate();

    this.assessmentValidityPeriodService.getAssessmentsValidityPeriods(this.context.client.id).subscribe(
      result => {
        this.context.periodsInheritedFromParent = result.periodsInhertitedFromParent;
        this.context.validityPeriods = result.periods;
        this.spinnerService.deactivate();
      },
      error => {
        this.spinnerService.deactivate();
        this.alertService.error(error.message);
      },
    );
  }

  /**
   * Shows dialog for a new assessment validity period control.
   */
   addValidityPeriod() {
    const newPeriod: AssessmentValidityPeriod = {
      assessmentType: null,
      assessmentTypeAsText: null,
      periodType: AssessmentValidityPeriodType.Day,
      periodTypeAsText: null,
      periodDuration: 1,
      clientId: this.context.client.id,
      clientAssessmentValidityPeriodId: -1
    }

    this.openDialog({
      data: {
        type: 'Add',
        selectedPeriod: newPeriod,
        assessmentTypes: this.getAssessmentTypes(this.context.validityPeriods.map(i => i.assessmentType))
      }
    });
  }

  /**
   * Shows dialog for a editing assessment validity period.
   */
  editValidityPeriod(period: AssessmentValidityPeriod) {
    const newPeriod = { ...period };

    this.openDialog({
      data: {
        type: 'Edit',
        selectedPeriod: newPeriod,
        assessmentTypes: this.getAssessmentTypes(this.context.validityPeriods.filter(i => i.assessmentType != newPeriod.assessmentType).map(i => i.assessmentType))
      }
    });
  }

  /**
   * Deletes assessment validity period.
   */
  deleteValidityPeriod(period: AssessmentValidityPeriod) {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: '450px',
      data: { action: `delete period with id = ${period.clientAssessmentValidityPeriodId}` },
    });

    dialogRef.afterClosed().subscribe(confirm => {
      if (confirm) {
        this.assessmentValidityPeriodService.deleteAssessmentValidityPeriod(period).subscribe(
          () => {
            this.alertService.success('Assessment validity period deleted');
            this.refreshValidityPeriod();
          },
          error => {
            this.alertService.error(error.message);
          }
        );
      }
    });
  }

  overrideInheritedValidityPeriods() {
    this.assessmentValidityPeriodService.overrideInheritedValidityPeriods(this.context.client.id).subscribe(
      () => {
        this.alertService.success('Assessment validity periods overrided');
        this.refreshValidityPeriod();
      },
      error => {
        this.alertService.error(error.message);
      }
    );
  }
}
