import { AssessmentValidityPeriod } from "@/shared/models/assessment-validity-period/assessment-validity-period";
import { ClientDetails } from '@/shared/models';
import { NotificationHeader } from '@/shared/models/NotificationHeader';

export enum ClientDetailsTab {
  GENERAL_INFO = 'GENERAL_INFO',
  PROJECT_DEFAULT_SETTINGS = 'PROJECT_DEFAULT_SETTINGS',
  ASSESSMENT_VALIDITY_PERIODS = 'ASSESSMENT_VALIDITY_PERIODS'
}

export class ClientDetailsComponentContext {
  assessmentValidityPeriodFilter = '';
  currentTab = ClientDetailsTab.GENERAL_INFO;
  periodsInheritedFromParent = false;
  validityPeriods: AssessmentValidityPeriod[];

  client: ClientDetails;
  notifications: NotificationHeader[];
}