@import "../../../../../styles/theme.scss";
@import "../../../../../styles/colors.scss";

$container-padding: 48px;
$content-width: 1200px;
$side-margin: 48px 64px;

:host {
  flex: 1;
}

.content {
  padding: $container-padding;
}

.table-actions-panel {
  display: flex;
  height: 48px;
  padding: 12px 24px;
  flex-direction: row;
  justify-content: stretch;
  align-items: center;
  user-select: none;

  input[type="search"] {
    width: 250px;
  }

  a {
    display: inline-block;
    font-weight: bold;
    padding: 3px 6px;
    white-space: nowrap;
    vertical-align: middle;
    color: $primary--blue;
    font-size: 0.9em;

    .mat-icon {
      margin-right: 12px;
      vertical-align: middle;
      color: inherit;
    }
  }

  .right-side {
    margin-left: auto;

    a {
      margin-left: 6px;
      font-size: 1em;
    }
  }

  .left-side {
    a {
      font-size: 12px;
      font-weight: bold;
      font-style: normal;
      font-stretch: normal;
      letter-spacing: 1px;
      text-transform: uppercase;
      position: relative;
      padding: 6px 0;
      vertical-align: middle;
      margin-right: 48px;

      &.active {
        &::after {
          content: "";
          width: 22px;
          height: 2px;
          position: absolute;
          background: $primary--blue;
          bottom: 3px;
          left: 0;
        }
      }
    }
  }
}

h4.group-title {
  color: rgba(0, 0, 0, 0.33);
  margin-bottom: 24px;
  text-transform: uppercase;
  letter-spacing: 1.5px;
  font-weight: 400;
}

.row {
  display: flex;
  justify-content: stretch;
  padding: 6px 0;
  width: 100%;

  &.changed {
    background: rgba(0, 0, 0, 0.05);
    font-weight: bold;
    margin: 0 -12px;
    padding: 6px 12px;
  }

  .col {
    min-width: 50%;
    width: auto;
    box-sizing: border-box;

    &.key {
      max-width: 150px;
      min-width: 150px;

      &.v-center {
        align-self: center;
      }
    }

    &.value {
      font-weight: bold;
      margin-left: 24px;
      max-height: 350px;
      overflow: auto;
    }
  }

  &.settings {
    .key {
      max-width: 210px;
      min-width: 210px;
    }
    .value {
      overflow: hidden;
    }
  }
}

::ng-deep .mat-menu-panel {
  .mat-menu-content {
    max-height: 350px;
    overflow-y: auto;
    box-sizing: border-box;
  }
}

.group {
  display: flex;
}

.muted {
  color: $primary--grey;
}
