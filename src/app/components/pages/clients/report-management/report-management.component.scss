@import "../../../../../styles/theme.scss";
@import "../../../../../styles/colors.scss";

:host {
  flex: 1 1 auto;

  display: flex;
  flex-direction: column;
  justify-content: stretch;

  .container {
    padding: 64px 0;
    display: flex;
    justify-content: center;

    input[type="text"],
    select,
    .label,
    .validation {
      padding: 4px 4px;
    }

    input[type="text"],
    select {
      width: 100%;
    }

    .form-control {
      margin: 1em 0;
      display: flex;
      justify-content: flex-start;
      .label {
        width: 200px;
      }
      .input {
        width: 300px;
        input[type="text"],
        select {
          &:not(:focus) {
            &.ng-touched.ng-invalid {
              box-shadow: 0px 0px 3px 0px $secondary--red;
            }
            &.ng-untouched.ng-invalid {
              box-shadow: 0px 0px 3px 0px $primary--blue-light;
            }
          }
        }
        label {
          &:not(:focus) {
            &.ng-touched.ng-invalid {
              input {
                box-shadow: 0px 0px 3px 0px $secondary--red;
              }
            }
            &.ng-untouched.ng-invalid {
              input {
                box-shadow: 0px 0px 3px 0px $primary--blue-light;
              }
            }
          }
        }
      }
      .validation {
        width: 300px;
      }
    }

    .error-container {
      padding: 17px 10px;
      width: 300px;
      .message {
        background-color: rgba(255, 255, 255, 0.3);
        padding: 8px;
        overflow-y: scroll;
        max-height: 400px;
      }
    }
    .spacer {
      width: 300px;
    }
  }
}
