import { ClientReportsService, ClientSearchService } from '@/services';
import { Client, ClientReportActions } from '@/shared/models';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, ParamMap } from '@angular/router';
import { Subscription } from 'rxjs';
import { switchMap } from 'rxjs/operators';

// Component to for client reports display and configurations.
@Component({
  selector: 'app-report-management',
  templateUrl: './report-management.component.html',
  styleUrls: ['./report-management.component.scss'],
  providers: [ClientReportsService],
})
export class ReportManagementComponent implements OnInit, OnDestroy {
  currentSubscription: Subscription;
  clientSubscription: Subscription;
  selectedClientId: number;
  showFilters = false;
  showReports = false;
  showDisplayOrder = false;
  client: Client;

  constructor(
    private route: ActivatedRoute,
    private clientReportService: ClientReportsService
  ) {}

  ngOnInit() {
    // Client reports.
    this.currentSubscription = this.route.data
      .pipe(
        switchMap((resolved) => {
          this.client = resolved.client;
          this.clientReportService.loadClientReports(this.client.id);
          return this.clientReportService.reportConfigurations;
        })
      )
      .subscribe();

    // set initial state to display reports.
    this.clientReportService.state(ClientReportActions.showReports);
    this.clientReportService.showReports.subscribe((result) => {
      this.showReports = result;
    });
    this.clientReportService.showFilters.subscribe((result) => {
      this.showFilters = result;
    });
    this.clientReportService.showReports.subscribe((result) => {
      this.showReports = result;
    });
    this.clientReportService.showDisplayOrder.subscribe((result) => {
      this.showDisplayOrder = result;
    });
  }

  ngOnDestroy() {
    // unsubscribe to ensure no memory leaks
    if (this.currentSubscription) {
      this.currentSubscription.unsubscribe();
    }
  }
}
