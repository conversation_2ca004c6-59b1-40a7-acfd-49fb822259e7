import { ClientTilesService } from '@/services/client-tiles.service';
import { AuthRole } from '@/shared/models';
import { Component, OnInit } from '@angular/core';

interface Tile {
  title: string;
  routerLink: string[];
  logoUrl: string;
  allowedRoles?: AuthRole[];
  bgColor: string;
  childRoutes?: Tile[];
}

@Component({
  selector: 'app-client-tiles',
  templateUrl: './client-tiles.component.html',
  styleUrls: ['./client-tiles.component.scss']
})
export class ClientTilesComponent implements OnInit {
  tiles: Tile[] = [
    {
      title: "KF ASSESS AND SELECT",
      routerLink: ['/clients'],
      logoUrl: "assets/images/data-extracts.svg",
      bgColor: "blue",

    },
    {
      title: "SUCCESS PROFILE SURVEY",
      routerLink: ['/clients'],
      logoUrl: "assets/images/configurable.svg",
      bgColor: "orange",

    },
  ];
  constructor(private clientTilesService:ClientTilesService) { }

  ngOnInit() {
    localStorage.removeItem("selectedClient");
    localStorage.removeItem("stakeholderDetailsData");
    localStorage.removeItem("spsCollabDetails");
  }

  onTileClick(event:Event){
    this.clientTilesService.sendTileName(event['title'])
  }
}