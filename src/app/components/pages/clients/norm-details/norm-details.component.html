<div class="details-page-fit-content white-glass">
  <div class="form-title">
    <mat-icon>help_outline</mat-icon>
    These norms are available for Cognitive Ability assessments only
  </div>
  <form [formGroup]="form">
    <table>
      <tr>
        <td class="nameCol titleCell">Name:</td>
        <td class="titleCell" colspan="2">
          <input type="text" class="nameField" formControlName="normName" required>
          <div *ngIf="form.controls.normName.invalid && (form.controls.normName.dirty || form.controls.normName.touched)" class="alert alert-danger nameField">
            <div *ngIf="form.controls.normName.errors?.required">
              Name is required.
            </div>
          </div>
        </td>
      </tr>
      <tr *ngIf="!allowEdit">
        <td class="nameCol titleCell">Norm No:</td>
        <td><div class="descriptionCell">{{this.context.normDetails.normNo}}</div></td>
        <td></td>
      </tr>
      <tr *ngIf="!allowEdit">
        <td class="nameCol titleCell">Text Id:</td>
        <td><div class="descriptionCell">{{this.context.normDetails.textId}}</div></td>
        <td></td>
      </tr>
      <tr>
        <td class="titleCell">Elements</td>
        <td class="titleCellWithCenterAlign">Mean</td>
        <td class="titleCellWithCenterAlign">Standard Deviation</td>
      </tr>
      <tr>
        <td>Logical</td>
        <td>
          <input type="numeric" id = "elementsLogicalMean" formControlName="elementsLogicalMean" [attr.disabled]="!allowEdit || null" (blur) = "checkDecimalValue($event)" class="numericField"/>
          <div *ngIf="form.controls.elementsLogicalMean.invalid && (form.controls.elementsLogicalMean.dirty || form.controls.elementsLogicalMean.touched)" class="alert alert-danger">
            <div *ngIf="form.invalid">
              {{ meanFieldValidationMessage }}
            </div>
          </div>
        </td>
        <td>
          <input type="numeric" formControlName="elementsLogicalSD" [attr.disabled]="!allowEdit || null" (blur) = "checkDecimalValue($event)" class="numericField"/>
          <div *ngIf="form.controls.elementsLogicalSD.invalid && (form.controls.elementsLogicalSD.dirty || form.controls.elementsLogicalSD.touched)" class="alert alert-danger">
            <div *ngIf="form.invalid">
              {{ sdFieldValidationMessage }}
            </div>
          </div>
        </td>
      </tr>
      <tr>
        <td>Numerical</td>
        <td>
          <input type="numeric" formControlName="elementsNumericalMean" [attr.disabled]="!allowEdit || null" (blur) = "checkDecimalValue($event)" class="numericField"/>
          <div *ngIf="form.controls.elementsNumericalMean.invalid && (form.controls.elementsNumericalMean.dirty || form.controls.elementsNumericalMean.touched)" class="alert alert-danger">
            <div *ngIf="form.invalid">
              {{ meanFieldValidationMessage }}
            </div>
          </div>
        </td>
        <td>
          <input type="numeric" formControlName="elementsNumericalSD" [attr.disabled]="!allowEdit || null" (blur) = "checkDecimalValue($event)"  class="numericField"/>
          <div *ngIf="form.controls.elementsNumericalSD.invalid && (form.controls.elementsNumericalSD.dirty || form.controls.elementsNumericalSD.touched)" class="alert alert-danger">
            <div *ngIf="form.invalid">
              {{ sdFieldValidationMessage }}
            </div>
          </div>
        </td>
      </tr>
      <tr>
        <td>Verbal</td>
        <td>
          <input type="numeric" formControlName="elementsVerbalMean" [attr.disabled]="!allowEdit || null" (blur) = "checkDecimalValue($event)" class="numericField"/>
          <div *ngIf="form.controls.elementsVerbalMean.invalid && (form.controls.elementsVerbalMean.dirty || form.controls.elementsVerbalMean.touched)" class="alert alert-danger">
            <div *ngIf="form.invalid">
              {{ meanFieldValidationMessage }}
            </div>
          </div>
        </td>
        <td>
          <input type="numeric" formControlName="elementsVerbalSD" [attr.disabled]="!allowEdit || null" (blur) = "checkDecimalValue($event)" class="numericField"/>
          <div *ngIf="form.controls.elementsVerbalSD.invalid && (form.controls.elementsVerbalSD.dirty || form.controls.elementsVerbalSD.touched)" class="alert alert-danger">
            <div *ngIf="form.invalid">
              {{ sdFieldValidationMessage }}
            </div>
          </div>
        </td>
      </tr>
      <tr>
        <td class="titleCell">Aspects</td>
        <td></td>
        <td></td>
      </tr>
      <tr>
        <td>Checking</td>
        <td>
          <input type="numeric" formControlName="aspectsCheckingMean" [attr.disabled]="!allowEdit || null" (blur) = "checkDecimalValue($event)" class="numericField"/>
          <div *ngIf="form.controls.aspectsCheckingMean.invalid && (form.controls.aspectsCheckingMean.dirty || form.controls.aspectsCheckingMean.touched)" class="alert alert-danger">
            <div *ngIf="form.invalid">
              {{ meanFieldValidationMessage }}
            </div>
          </div>
        </td>
        <td>
          <input type="numeric" formControlName="aspectsCheckingSD" [attr.disabled]="!allowEdit || null" (blur) = "checkDecimalValue($event)" class="numericField"/>
          <div *ngIf="form.controls.aspectsCheckingSD.invalid && (form.controls.aspectsCheckingSD.dirty || form.controls.aspectsCheckingSD.touched)" class="alert alert-danger">
            <div *ngIf="form.invalid">
              {{ sdFieldValidationMessage }}
            </div>
          </div>
        </td>
      </tr>
      <tr>
        <td>Numerical</td>
        <td>
          <input type="numeric" formControlName="aspectsNumericalMean" [attr.disabled]="!allowEdit || null" (blur) = "checkDecimalValue($event)" class="numericField"/>
          <div *ngIf="form.controls.aspectsNumericalMean.invalid && (form.controls.aspectsNumericalMean.dirty || form.controls.aspectsNumericalMean.touched)" class="alert alert-danger">
            <div *ngIf="form.invalid">
              {{ meanFieldValidationMessage }}
            </div>
          </div>
        </td>
        <td>
          <input type="numeric" formControlName="aspectsNumericalSD" [attr.disabled]="!allowEdit || null" (blur) = "checkDecimalValue($event)" class="numericField"/>
          <div *ngIf="form.controls.aspectsNumericalSD.invalid && (form.controls.aspectsNumericalSD.dirty || form.controls.aspectsNumericalSD.touched)" class="alert alert-danger">
            <div *ngIf="form.invalid">
              {{ sdFieldValidationMessage }}
            </div>
          </div>
        </td>
      </tr>
      <tr>
        <td>Verbal</td>
        <td>
          <input type="numeric" formControlName="aspectsVerbalMean" [attr.disabled]="!allowEdit || null" (blur) = "checkDecimalValue($event)" class="numericField"/>
          <div *ngIf="form.controls.aspectsVerbalMean.invalid && (form.controls.aspectsVerbalMean.dirty || form.controls.aspectsVerbalMean.touched)" class="alert alert-danger">
            <div *ngIf="form.invalid">
              {{ meanFieldValidationMessage }}
            </div>
          </div>
        </td>
        <td>
          <input type="numeric" formControlName="aspectsVerbalSD" [attr.disabled]="!allowEdit || null" (blur) = "checkDecimalValue($event)" class="numericField"/>
          <div *ngIf="form.controls.aspectsVerbalSD.invalid && (form.controls.aspectsVerbalSD.dirty || form.controls.aspectsVerbalSD.touched)" class="alert alert-danger">
            <div *ngIf="form.invalid">
              {{ sdFieldValidationMessage }}
            </div>
          </div>
        </td>
      </tr>
      <tr>
        <td></td>
        <td>
          <button (click)="exit()" type="button" class="btn btn-secondary">
            Cancel
          </button>
        </td>
        <td>
          <button (click)="saveNorm()" type="button" class="btn btn-primary" [attr.disabled]="form.invalid || null">
            Save
          </button>
        </td>
      </tr>
    </table>
  </form>
</div>
