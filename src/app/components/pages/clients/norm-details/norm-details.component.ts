import { AlertService, NormService, SpinnerService } from '@/services';
import { Norm } from '@/shared/models';
import { AssessmentType } from '@/shared/pipes';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NormDetailsComponentContext } from './norm-details.component.context';

@Component({
  selector: 'app-norm-details',
  templateUrl: './norm-details.component.html',
  styleUrls: ['./norm-details.component.scss'],
})
export class NormDetailsComponent implements OnInit {
  public context = new NormDetailsComponentContext();
  form: FormGroup;
  allowEdit = false;
  meanFieldValidationMessage = 'Range: 0 - 999.99';
  meanFieldMinValue = 0;
  sdFieldValidationMessage = 'Range: 0 - 999.99';
  sdFieldMinValue = 0;
  maxValue = 999.99; // decimal(7,4) in MS SQL Database (dbo.Norms)

  formControlLabels = {
    elementsLogical: AssessmentType['Elements Logical'],
    elementsNumerical: AssessmentType['Elements Numerical'],
    elementsVerbal: AssessmentType['Elements Verbal'],
    aspectsChecking: AssessmentType['Aspects Checking'],
    aspectsNumerical: AssessmentType['Aspects Numerical'],
    aspectsVerbal: AssessmentType['Aspects Verbal']
  }

  constructor(private fb: FormBuilder,
    private route: ActivatedRoute,
    private normService: NormService,
    private spinnerService: SpinnerService,
    private alertService: AlertService,
    private router: Router
  ) {}

  ngOnInit() {
    this.buildForm(false);
  }

  buildForm(refresh: boolean) {
    if (!refresh) {
      this.route.data.subscribe(
        (resolved) => {
          this.context.client = resolved.client;
          this.context.normDetails = resolved.norm;
          this.spinnerService.deactivate();
        },
        (error) => {
          this.alertService.error(error.message);
          this.spinnerService.deactivate();
        }
      );

      this.allowEdit = this.context.normDetails.normNo < 0;
    }

    const meanValidators = [Validators.required, Validators.min(this.meanFieldMinValue), Validators.max(this.maxValue)];
    const sdValidators = [Validators.required, Validators.min(this.sdFieldMinValue), Validators.max(this.maxValue)];

    this.form = this.fb.group({
      normName: ['', Validators.required],
      elementsLogicalMean: ['', meanValidators],
      elementsLogicalSD: ['', sdValidators],
      elementsNumericalMean: ['', meanValidators],
      elementsNumericalSD: ['', sdValidators],
      elementsVerbalMean: ['', meanValidators],
      elementsVerbalSD: ['', sdValidators],
      aspectsCheckingMean: ['', meanValidators],
      aspectsCheckingSD: ['', sdValidators],
      aspectsNumericalMean: ['', meanValidators],
      aspectsNumericalSD: ['', sdValidators],
      aspectsVerbalMean: ['', meanValidators],
      aspectsVerbalSD: ['', sdValidators]
    });

    this.form.get('normName').setValue(this.context.normDetails.text);
    Object.keys(this.formControlLabels).map((controlName) => { 
      let norm = this.context.normDetails.norms.find(n => n.testId == this.formControlLabels[controlName]);
      this.form.get(controlName + 'Mean').setValue(norm ? norm.mean : this.meanFieldMinValue);
      this.form.get(controlName + 'SD').setValue(norm ? norm.sd : this.sdFieldMinValue);
    });
  }

  checkDecimalValue(event: any) {
    event.target.value = parseFloat(event.target.value).toFixed(2);
  }

  saveNorm() {
    if (this.form.invalid) {
      return;
    }

    let newNorm = {...this.context.normDetails};
    newNorm.text = this.form.get('normName').value;

    Object.keys(this.formControlLabels).map((controlName) => { 
      let assessmentType = this.formControlLabels[controlName];
      let existsNorm = newNorm.norms.find(n => n.testId == assessmentType);
      if (existsNorm == null) {
        let norm: Norm = {
          Id: -1,
          normNo: newNorm.normNo,
          testId: assessmentType,
          versionId: null,
          mean: this.form.get(controlName + 'Mean').value,
          sd: this.form.get(controlName + 'SD').value
        };

        if (norm.mean > 0 || norm.sd > 0) {
          newNorm.norms.push(norm);
        }
      }
      else {
        existsNorm.mean = this.form.get(controlName + 'Mean').value;
        existsNorm.sd = this.form.get(controlName + 'SD').value;
      }
    });

    this.normService.saveCustomNormWithDetails(newNorm).subscribe(
      result => {
        this.normService.getCustomNormWithDetails(this.context.client.id, result.normNo).subscribe(
          result => {
            this.context.normDetails = result;

            this.router.navigate(['../norm-details'], {
              relativeTo: this.route,
              queryParams: {
                clientId: this.context.normDetails.clientId,
                normNo: this.context.normDetails.normNo
              }
            })
            .then(() => {
               this.buildForm(true);
               this.alertService.success('Custom norm was successfully saved');
            });
          }
        );
    },
      error => {
        this.spinnerService.deactivate();
        this.alertService.error(error.message);
      });
  }

  exit() {
    this.router.navigate(['../client-norms'], {
      relativeTo: this.route,
      queryParams: {
        clientId: this.context.client.id
      },
    });
  }
}
