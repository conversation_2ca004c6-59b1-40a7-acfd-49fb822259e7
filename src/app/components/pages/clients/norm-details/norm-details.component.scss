@import "../../../../../styles/theme.scss";
@import "../../../../../styles/colors.scss";

$container-padding: 24px;
$label-width: 200px;
$label-width-long: 250px;
$label-width-short: 100px;
$value-padding: 6px;
$value-width: 200px;
$content-width: 1200px;
$side-margin: 18px 64px;

.form-title {
  vertical-align: top;
  color: $primary--grey;
  font-size: 13px;
  margin-bottom: 12px;
  opacity: .85;

  mat-icon {
    font-size: 20px;
    line-height: 20px;
    height: 20px;
  }
}

tr {
  height: 30px;
}

.nameCol {
  width: 120px;

  &.titleCell {
    padding: 4px 0;
  }
}

.nameField {
  width: 100%;
}

.titleCellWithCenterAlign,
.titleCell {
  font-weight: bold;
  vertical-align: middle;
  padding: 18px 0 12px 0;
}

.titleCellWithCenterAlign {
  text-align: center;
}

.description<PERSON>ell,
input[type='text'],
input[type='numeric'] {
  padding: 4px;
  margin: 2px;
}

button {
  margin: 18px 3px 3px 3px;
  width: calc(100% - 6px);
}

.numericField {
  text-align: right;
}

.ng-invalid:not(form)  {
  border-left: 5px solid #a94442; /* red */
}

.alert div {
  background-color: #fed3d3;
  color: #820000;
  padding: 1rem;
  margin-bottom: 1rem;
}
