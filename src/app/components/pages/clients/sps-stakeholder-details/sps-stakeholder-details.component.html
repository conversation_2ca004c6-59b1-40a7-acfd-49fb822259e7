<div class="details-page-content white-glass">
  <div class="row">
    <div class="col">
      <div class="group" *ngFor="let item of generalInfo">
        <div class="label">{{ item.label }}</div>
        <div class="value">
          <ng-container *ngIf="item.isEditable">
            <a class="flat" [allowedRoles]="['admin', 'productDelivery', 'projectParticipantManagement']"
              (click)="editNorm()">
              {{ item.value || "-" }}
            </a>
          </ng-container>
          <ng-container *ngIf="!item.isEditable">
            {{ item.value || "-" }}
          </ng-container>
        </div>
      </div>
      <div class="stakeholder-info">
        Stakeholder Information
        <span class="show-stakeholder">Show Stakeholder List</span>
      </div>
    </div>
  </div>
</div>

<div class="page-content">
  <div class="client-reports">
    <kf-table [data]="tableData" [columns]="columns"  (paginatorChanged)="onPageClick($event)" [customPaging]="customPaging"></kf-table>
  </div>
</div>
