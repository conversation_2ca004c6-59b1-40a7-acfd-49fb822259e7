@import "../../../../../styles/theme.scss";
@import "../../../../../styles/colors.scss";

$container-padding: 24px;
$label-width: 200px;
$label-width-long: 250px;
$label-width-short: 100px;
$value-padding: 6px;
$value-width: 200px;
$content-width: 1200px;
$side-margin: 48px 64px;

:host {
  flex: 1;
}

.content {
  max-width: $content-width;
  margin: $side-margin;
  padding: $container-padding;
  background: white;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
}

.details-page-content{
  background: $primary--grey-light;
  margin: 0 64px;
  max-width: 1400px;
  opacity: 0.9;
  border-radius: 0;
}

.group-title {
  color: $primary--grey-medium;
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 1.5px;
  font-weight: 400;
}

.row {
  display: flex;

  &:not(:last-child) {
    margin-bottom: 12px;
  }

  .col {
    flex: 50%;
  }
}

.group {
  display: flex;

  a {
    .mat-icon {
      vertical-align: middle;
      line-height: inherit;
      color: inherit;

      font-size: 1.1em;
      margin-right: 0;
      width: 16px;
    }
  }
}

.label {
  min-width: $label-width-long;
  padding: $value-padding 0;

  &.short {
    width: $label-width-short;
  }

  &.long {
    width: $label-width-long;
  }
}

.value {
  font-weight: bold;
  padding: $value-padding 0;
  min-width: $value-width;
}

.preview {
  font-weight: bold;
}

.success-profile {
  display: flex;

  .sp-name {
    padding: $value-padding;
    width: 430px;
    font-weight: bold;
  }

  .sp-customized-label {
    width: 100px;
    padding: $value-padding;
  }

  .sp-customized {
    padding: $value-padding;
    font-weight: bold;
  }

  .sp-view-details {
    padding: $value-padding;
  }
}

.email-template {
  display: flex;

  &-label {
    width: $label-width-long - 50px;
    padding: $value-padding 0;
  }

  &-value {
    padding: $value-padding 0;
  }
}

ul.assessments {
  li {
    text-transform: capitalize;
  }
}

td,
th {
  &.customized {
    padding: 0 $container-padding 2 * $value-padding $container-padding;
    text-align: center;
  }
}

.group {
  &.modules {

    .label,
    .value {
      padding: 0;
      line-height: 24px;
    }

    .group-title {
      font-weight: 400;
      margin-top: 12px;
    }

    .gray {
      color: $primary--grey-medium;
      pointer-events: none;
      font-weight: 400;
      cursor: auto;
    }

    .assessmentLinkAssessment {
      &.disabled {
        color: black;
        pointer-events: none;
        cursor: auto;
      }
    }

    &:last-of-type {
      margin-bottom: 12px;
    }
  }
}

.group {
  display: flex;

  .modules-title {
    padding-bottom: 2%;
  }
}

.stakeholder-info{
  padding-top: 10px;
}

.show-stakeholder {
  padding-left: 7%;
  color: #007DA4;
  font-size: 14px;
  font-weight: 600;
}

.group-modules {
  display: flex;
  flex-direction: column;

  .modules-title {
    padding-bottom: 12px;
  }
}

.client-reports{
  max-width: 1496px;
}