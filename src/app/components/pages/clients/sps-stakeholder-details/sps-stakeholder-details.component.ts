import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { AlertService, PageHeaderService, SpinnerService } from '@/services';
import { StakeholderDetailsComponentContext } from './sps-stakeholder-details.component.context';
import { StakeholderDetails, SurveyStatuses } from '@/shared/models/sps-client/sps-stakeholder';
import { STAKEHOLDER_DETAILS_TABLE_COLUMNS } from './stakeholder-details.columns';
import { KFTableColumn } from '@/components/controls/kf-table/kf-table.component';
import { StakeholderDetailsService } from '@/services/sps-stakeholder-details.service';
import { Subscription } from 'rxjs';
import { SharedService } from '@/shared/services/shared.service';


@Component({
  selector: 'app-stakeholder-details',
  templateUrl: './sps-stakeholder-details.component.html',
  styleUrls: ['./sps-stakeholder-details.component.scss']
})
export class SpsStakeholderDetailsComponent implements OnInit {
  public context = new StakeholderDetailsComponentContext();
  columns = STAKEHOLDER_DETAILS_TABLE_COLUMNS;
  tableData: StakeholderDetails[];
  clientId;
  currentSubscription: Subscription;
  emailId: string;
  customPaging = {
    pageIndex: 1,
    pageSize: 10,
    totalPages: null,
    totalResultRecords: null,
  };
  MAX_SURVEY_STAGES: number = 3;

  constructor(private route: ActivatedRoute, private spinnerService: SpinnerService, private alertService: AlertService, private router: Router,
    private stakeholderDetailsService: StakeholderDetailsService, private shared: SharedService, private pageHeaderService: PageHeaderService) { }

  ngOnInit() {
    this.route.data.subscribe(
      (resolved) => {
        this.context.client = resolved.client;
        this.context.stakeholders = resolved.stakeholders;
        this.pageHeaderService.setSPSDetails('Stakeholder Details', resolved.stakeholders.data.stakeholderDetailsResponse.stakeholderName)
        this.spinnerService.deactivate();
      },
      (error) => {
        this.alertService.error(error.message);
        this.spinnerService.deactivate();
      }
    );

    this.clientId = JSON.parse(localStorage.getItem('selectedClient')).id;
    this.emailId = this.context.stakeholders.data.stakeholderDetailsResponse.collabDetails[0].participants[0].email;
    this.getStakeholderDetailsData(this.clientId, this.emailId);

    this.columns.forEach((column: KFTableColumn<any>) => {
      if (column.name === 'collabId' || column.name === 'spTitle') {
        column.click = (t: any) => {
          this.router.navigate(['../sps-collaboration-details'], {
            relativeTo: this.route,
            queryParams: {
              collabId: t.collabId
            },
          });
        }
        column.url = (t: any) =>
          this.router
            .createUrlTree(['../sps-collaboration-details'], {
              relativeTo: this.route,
              queryParams: {
                collabId: t.collabId
              },
            })
            .toString();
      }
      else if (column.name === 'survey') {
        column.click = (t: any) => {
          //pop up to be shown here
        }
      }
    })
  }

  get generalInfo() {
    return [
      { label: 'First Name', value: this.context.stakeholders.data.stakeholderDetailsResponse.collabDetails[0].participants[0].firstName },
      { label: 'Last Name', value: this.context.stakeholders.data.stakeholderDetailsResponse.collabDetails[0].participants[0].lastName },
      { label: 'Email Address', value: this.context.stakeholders.data.stakeholderDetailsResponse.collabDetails[0].participants[0].email },
    ];
  }

  toTitleCase(str) {
    return str.replace(/\w\S*/g, function (txt) {
      return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
    });
  }

  onPageClick(event) {
    this.getStakeholderDetailsData(this.clientId, this.emailId, event.pageIndex + 1, event.pageSize)
  }

  getStakeholderDetailsData(clientId: string, emailId?: string, pageIndex?: number, pageSize?: number) {
    const resolvedPageIndex = pageIndex === undefined ? 1 : pageIndex;
    const resolvedPageSize = pageSize === undefined ? 10 : pageSize;
    this.spinnerService.activate();
    this.currentSubscription = this.stakeholderDetailsService.getStakeholderDetails(clientId, emailId, resolvedPageIndex, resolvedPageSize).subscribe((response) => {
      this.customPaging = {
        pageIndex: resolvedPageIndex,
        pageSize: resolvedPageSize,
        totalResultRecords: response.data.totalRecords,
        totalPages: response.data.totalRecords / resolvedPageSize
      }
      this.spinnerService.deactivate();
      response.data.stakeholderDetailsResponse.collabDetails.forEach(obj => {
        obj.access = obj.participants.map(participant => participant.permissions.map(permission => permission.permissionLevel).join(", ")).join(", ");
      });
      response.data.stakeholderDetailsResponse.collabDetails.forEach(obj => {
        let completedStages = obj.participants[0].surveydashboardResponse.filter(response => response.stage === SurveyStatuses.COMPLETED).length;
        if (obj.participants[0].surveyStatus === SurveyStatuses.NOT_INVITED || obj.participants[0].surveyStatus === SurveyStatuses.NO_ACCESS) {
          let surveyStatus = obj.participants[0].surveyStatus;
          obj.survey = surveyStatus =  surveyStatus === SurveyStatuses.NOT_INVITED ? SurveyStatuses.NOT_INVITED : SurveyStatuses.NO_ACCESS;
        } else {
          obj.survey = completedStages === this.MAX_SURVEY_STAGES ?  `${this.MAX_SURVEY_STAGES}/${this.MAX_SURVEY_STAGES}` : `${completedStages}/${this.MAX_SURVEY_STAGES}`;
        }
      });
      this.tableData = response.data.stakeholderDetailsResponse.collabDetails;
    }),
      (error: Error) => {
        this.spinnerService.deactivate();
        this.tableData = [];
      }
  }

  ngOnDestroy() {
    if (this.currentSubscription) {
      this.currentSubscription.unsubscribe();
    }
  }

}
