import { KFTableColumn } from '@/components/controls/kf-table/kf-table.component';

export const STAKEHOLDER_DETAILS_TABLE_COLUMNS: KFTableColumn<any>[] = [
    {
        name: 'collabId',
        label: 'ID',
        type: 'text',
        width: '135px',
        sortable: true,
    },
    {
        name: 'spTitle',
        label: 'Collaboration Name',
        type: 'text',
        sortable: true,
        width: '350px',
    },
    {
        name: 'createdDate',
        label: 'Date',
        type: 'text',
        sortable: true,
        width: '220px',
    },
    {
        name: 'sourceSystemName',
        label: 'Source System',
        type: 'text',
        sortable: true,
        width: '160px',
    },
    {
        name: 'access',
        label: 'Access',
        type: 'text',
        sortable: true,
        width: '345px',
    },
    {
        name: 'survey',
        label: 'Survey',
        type: 'text',
        sortable: true,
        width: '118px',
    }
];