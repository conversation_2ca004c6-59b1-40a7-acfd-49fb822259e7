@import "../../../../../styles/theme.scss";
@import "../../../../../styles/colors.scss";

:host {
  display: flex;
  flex-direction: column;
  justify-content: stretch;
  margin-bottom: 40px;

  .file-preview-popup-container {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10;

    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .file-preview-content {
      padding: 12px;
      background: $light-background;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
      position: relative;

      a {
        cursor: pointer;
        color: $primary--blue;
        display: inline-block;
        vertical-align: middle;

        &.copy {
          font-size: 0.7em;
          color: white;
          background: $light-bg-darker-10;
          border-radius: 5px;
          padding: 2.5px 5px;
          margin: 0 9px;
          &.touched {
            color: white;
            background: $primary--blue;
          }
        }
      }

      h4 {
        margin-bottom: 16px;
      }

      .image {
        text-align: center;
        img {
          max-width: 900px;
          max-height: 600px;
          filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.15));
        }
      }
    }
  }

  .files-container {
    @extend .glass;

    margin: 0 64px;
    padding: 36px;
    display: flex;
    flex-direction: column;

    h4 {
      margin-bottom: 24px;
      flex: 0 0 auto;
      white-space: nowrap;
    }

    .file-list {
      display: flex;
      flex-direction: row;
      align-items: center;
      flex-wrap: wrap;

      &.viewMode-thumbs {
        .file {
          min-height: 50px;
        }
      }

      .file {
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        justify-content: stretch;
        align-items: center;
        width: 280px;
        padding: 6px;
        cursor: pointer;
        font-size: 14px;
        line-height: 20px;
        letter-spacing: -0.2px;
        margin-bottom: 12px;
        margin-right: 24px;

        .file-thumb {
          min-width: 50px;
          max-width: 50px;
          margin-right: 10px;
          filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.15));
        }

        .file-icon {
          color: rgba(0, 0, 0, 0.75);
          vertical-align: middle;
        }

        &:hover {
          @extend .glass;
        }

        .filename {
          flex-grow: 1;
          max-width: 100%;
          min-width: fit-content;
          font-weight: 600;
          overflow-x: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          span {
            margin-left: 6px;
            vertical-align: middle;
          }
        }

        a.close {
          width: 24px;
          height: 24px;
          display: block;

          color: transparent;
        }

        &:hover {
          a.close {
            color: $secondary--red;
            opacity: 0.3;

            &:hover {
              opacity: 1;
            }
          }
        }
      }
    }

    &.compact {
      margin: 0;
      padding: 24px;

      .file-list {
        display: block;
        max-height: 65vh;
        overflow-y: auto;

        .file {
          margin: 0;

          .filename {
            font-weight: 400;
          }
        }
      }
    }
  }

  .upload-form {
    flex: 0 0 auto;
    padding: 0;
    margin-bottom: 24px;
    white-space: nowrap;

    .tooltip-backdrop {
      display: none;
      position: fixed;
      background: transparent;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
      cursor: default;

      &.active {
        display: block;
      }
    }

    a {
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.05));
      position: relative;

      &.tooltip-icon {
        position: absolute;
        z-index: 1;
        transform: translate(10px, 10px);
        .mat-icon {
          color: $primary--grey;
          color: gray;
        }
      }

      .tooltip {
        position: absolute;
        top: -15px;
        left: 0;
        transform: translateY(-100%);

        background: white;
        color: $dark-primary-text;

        width: 320px;
        padding: 12px;
        font-size: 12px;
        line-height: 16px;

        display: none;

        & > * {
          padding-bottom: 12px;
          white-space: normal;
        }

        ul {
          li {
            list-style: square inside !important;
          }
        }

        &::after {
          content: "";
          display: block;
          position: absolute;
          width: 0;
          height: 0;
          bottom: -12px;
          border-right: solid 18px transparent;
          border-top: solid 12px white;
          left: 12px;
        }

        &.active {
          display: block;
        }
      }

      .mat-icon {
        color: $primary--blue;
        margin-right: 12px;
        vertical-align: middle;
      }

      &.disabled {
        cursor: default;

        .mat-icon {
          color: $disabled-color;
        }
      }

      &:not(.disabled):hover {
        .mat-icon {
          color: $primary--blue;
        }
      }

      + a {
        .mat-icon {
          margin-left: 0;
        }
      }
    }

    input[type="file"] {
      display: none;
    }

    input[type="text"] {
      width: 250px;
      pointer-events: none;
      vertical-align: middle;
      color: black;
      margin-right: 12px;
      padding: 12px;
      padding-left: 40px;

      @extend .glass;
    }
  }
}
