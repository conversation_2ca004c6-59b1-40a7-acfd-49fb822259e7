import { AlertService, SpinnerService } from '@/services';
import { UploadAssetsService } from '@/services/upload-assets.service';
import { Client } from '@/shared/models';
import { ReportAsset } from '@/shared/models/configurable-reports/ReportAsset';
import { HttpResponse } from '@angular/common/http';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-branding-assets',
  templateUrl: './branding-assets.component.html',
  styleUrls: ['./branding-assets.component.scss'],
})
export class BrandingAssetsComponent implements OnInit {
  readonly IMAGE_WIDTH_PX_LIMIT = 3000;
  readonly IMAGE_HEIGHT_PX_LIMIT = 2000;
  readonly IMAGE_SIZE_MB_LIMIT = 5;

  @Input() assets: ReportAsset[];
  @Input() client: Client;
  @Input() compact = false;
  @Input() showThumbs = false;

  @Output() dataChange = new EventEmitter();

  previewedFile: any;
  loading: boolean;
  file2upload: File;

  constructor(
    private uploadService: UploadAssetsService,
    private spinnerService: SpinnerService,
    private alertService: AlertService,
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
    this.route.data.subscribe((resolved) => {
      this.client = resolved.client;
      this.assets = resolved.assets;
    });
  }

  previewFile(file) {
    this.previewedFile = file;
  }

  cancelPreview() {
    this.previewedFile = null;
  }

  uploadFileConfirmed(event) {
    const context = this;
    const selectedFile = event.target.files.item(0);
    if (selectedFile) {
      const img = new Image();
      img.src = window.URL.createObjectURL(selectedFile);
      img.onload = () => {
        const w = img.naturalWidth;
        const h = img.naturalHeight;

        const wLimit = context.IMAGE_WIDTH_PX_LIMIT;
        const hLimit = context.IMAGE_HEIGHT_PX_LIMIT;
        const sizeLimitInBytes = context.IMAGE_SIZE_MB_LIMIT * 1024 * 1024;

        // rect size validation
        if (w > wLimit || h > hLimit) {
          context.alertService.error(
            `The image size should be not over ${wLimit}x${hLimit} pixels.`
          );
          return;
        }

        // file size validation
        if (selectedFile.size > sizeLimitInBytes) {
          context.alertService.error(
            `The upload file should be less than ${context.IMAGE_SIZE_MB_LIMIT} MB.`
          );
          return;
        }

        context.file2upload = selectedFile;
      };
    }
  }

  uploadFile() {
    if (!this.file2upload) {
      return;
    }

    this.spinnerService.activate();
    this.uploadService
      .uploadNewAsset(this.client.id, this.file2upload)
      .subscribe((data) => {
        if (data instanceof HttpResponse) {
          this.assets = data.body.clientAssets;
          this.dataChange.emit(this.assets);
          this.file2upload = null;
          this.spinnerService.deactivate();
        }
      });
  }

  toggleTooltip(tooltip, tooltipBackdrop) {
    tooltip.classList.toggle('active');
    tooltipBackdrop.classList.toggle('active');
  }

  copyToClipboard(value, btn: Element) {
    const el = document.createElement('textarea');
    el.value = value;
    el.setAttribute('readonly', '');
    el.style.position = 'absolute';
    el.style.left = '-9999px';
    document.body.appendChild(el);
    el.select();
    document.execCommand('copy');
    document.body.removeChild(el);

    btn.classList.add('touched');
    btn.innerHTML = 'Copied!';
  }
}
