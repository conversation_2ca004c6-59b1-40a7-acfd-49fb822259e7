<div class="files-container" [class.compact]="compact">
  <div class="upload-form" [allowedRoles]="['admin', 'reportManagement', 'productDelivery']">
    <div
      #tooltipBackdrop
      class="tooltip-backdrop"
      (click)="toggleTooltip(tooltip, tooltipBackdrop)"
    ></div>
    <a class="tooltip-icon" (click)="toggleTooltip(tooltip, tooltipBackdrop)">
      <div #tooltip class="tooltip">
        <h4>Guideline for uploading assets</h4>
        <p>
          Please make sure that file you going to upload match the following
          requirements:
        </p>
        <ul>
          <li>Image extension is <b>JPG, JPEG, PNG or SVG</b></li>
          <li>
            Image size
            <b
              >not over {{ IMAGE_WIDTH_PX_LIMIT }}x{{
                IMAGE_HEIGHT_PX_LIMIT
              }}
              pixels</b
            >
          </li>
          <li>
            File size <b>less than {{ IMAGE_SIZE_MB_LIMIT }}MB</b>
          </li>
        </ul>
      </div>
      <mat-icon>help_outline</mat-icon>
    </a>

    <!-- this input is hidden in component.scss -->
    <input
      #upload
      type="file"
      (change)="uploadFileConfirmed($event)"
      accept=".jpg,.jpeg,.png,.svg"
    />

    <!-- visible UI for hidden "file" input -->
    <a (click)="upload.click()">
      <input
        type="text"
        placeholder="Choose file to upload..."
        [value]="file2upload ? file2upload.name : null"
      />
      <mat-icon>folder_open</mat-icon>
    </a>
    <a (click)="uploadFile()" [ngClass]="{ disabled: !file2upload }">
      <mat-icon>cloud_upload</mat-icon>
    </a>
  </div>

  <h4>
    Available assets:
    <a (click)="showThumbs = !showThumbs">
      <mat-icon>{{ showThumbs ? "visibility_off" : "visibility" }}</mat-icon>
    </a>
  </h4>
  <div class="file-list">
    <p *ngIf="!assets || !assets.length">No assets found</p>
    <div
      class="file"
      *ngFor="let file of assets; let i = index"
      (click)="previewFile(file)"
    >
      <img class="file-thumb" *ngIf="showThumbs" [src]="file.fileUrl" alt="" />
      <div class="filename">
        <mat-icon class="file-icon" *ngIf="!showThumbs">image</mat-icon>
        <span>{{ file.fileName }}</span>
      </div>
    </div>
  </div>
</div>

<div class="file-preview-popup-container" *ngIf="previewedFile">
  <div class="file-preview-content">
    <h4>
      {{ previewedFile.fileName }}
      <a class="close float-right" (click)="cancelPreview()">
        <mat-icon>close</mat-icon>
      </a>
      <a
        class="copy float-right"
        (click)="copyToClipboard(previewedFile.fileUrl, $event.target)"
      >
        Copy path to clipboard
      </a>
    </h4>
    <div class="image">
      <img src="{{ previewedFile.fileUrl }}" alt="Asset preview" />
    </div>
  </div>
</div>
