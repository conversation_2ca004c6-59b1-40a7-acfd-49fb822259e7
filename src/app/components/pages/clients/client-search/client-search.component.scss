@import "../../../../../styles/theme.scss";
@import "../../../../../styles/colors.scss";
@import "../../../../../styles/toolbar.scss";

:host {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;

  .search {
    padding: 40px;
    color: white;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    > * {
      width: 100%;
      max-width: 700px;
    }
  }
  .mat-row.active {
    background: $primary--blue;
    .mat-cell {
      color: white;
      a {
        color: white;
      }
    }
  }
}

.search,
h2 {
  box-sizing: border-box;
  margin: 0 80px;
}

h2 {
  text-align: center;
  padding: 48px 0;
  font-size: 30px;
  line-height: 36px;
  text-transform: uppercase;
  color: white;
}

mat-table.mat-table {
  margin: 0;
  max-height: 450px;
}

mat-header-cell,
mat-cell {
  &.fixed-size {
    flex: 0 0 150px;
  }
}

a.view-client-details-link {
  display: block;
  font-size: 16px;
  margin-top: 12px;
  color: #d8d8d8;
  text-shadow: 0px 0px 3px rgba(0, 0, 0, 0.32);
}

.success-profile-survey {
  padding: 64px 80px 0 64px;
  color:#FFFFFF;
  .sps-heading {
    font-size: 36px;
    font-weight: 700;
    padding-bottom: 14px;
  }

  .client {
    color: rgba(255, 255, 255, 0.70);
    font-size: 15px;
    font-weight: 400;
  }

  .sps-client-name {
    font-size: 15px;
    font-weight: 600;
    padding-left: 10px;
  }
}
