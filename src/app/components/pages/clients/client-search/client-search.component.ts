import { KFTableAction, KFTableColumn } from '@/components/controls';
import { KfTile } from '@/components/controls/kf-tiles/kf-tile-model';
import { AuthenticationService, ClientSearchService, SpinnerService, ApiService } from '@/services';
import { ClientTilesService } from '@/services/client-tiles.service';
import { AuthRole, Client, ClientTypes } from '@/shared/models';
import { Location } from '@angular/common';
import {
  Component,
  HostBinding,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { MatPaginator, MatSort, MatTableDataSource } from '@angular/material';
import { ActivatedRoute, Router } from '@angular/router';
import { combineLatest, Subscription } from 'rxjs';
import { map, tap } from 'rxjs/operators';

/**
 * Component to search and display clients.
 */
@Component({
  selector: 'app-client-search',
  templateUrl: './client-search.component.html',
  styleUrls: ['./client-search.component.scss'],
})
export class ClientSearchComponent implements OnInit, OnDestroy {
  currentSubscription: Subscription;
  searchResult: MatTableDataSource<Client> = new MatTableDataSource<Client>();
  columns: KFTableColumn<Client>[];
  kfAssesscolumns: KFTableColumn<Client>[] = [
    {
      name: 'id',
      label: 'id',
      type: 'text',
      width: '150px',
      sortable: true,
    },
    {
      name: 'name',
      label: 'name',
      type: 'text',
      sortable: true,
      click: (client: Client) => {
        this.selectClient(client);
      },
    },
    {
      name: 'dateCreated',
      label: 'Date created',
      type: 'date',
      dateFormat: 'MMM dd, yyyy',
      sortable: true,
      width: '150px',
    },
    {
      name: 'actions',
      label: 'actions',
      type: 'rowOptions',
      width: '150px',
    },
  ];
  actions: KFTableAction<Client>[] = [
    {
      label: 'Client details',
      icon: 'list_alt',
      click: (client: Client) => {
        this.selectClient(client);
        this.router.navigate(['/clients/client-details'], {
          queryParams: {
            clientId: client.id,
          },
        });
      },
    },
  ];
  loading = false;
  filtering = false;
  filteredClients: Client[];
  spsColumns: KFTableColumn<Client>[] = [
    {
      name: 'id',
      label: 'id',
      type: 'text',
      width: '244px',
      sortable: true,
    },
    {
      name: 'name',
      label: 'name',
      type: 'text',
      width: '570px',
      sortable: true,
      click: (client: Client) => {
        this.selectSPSClient(client);
      },
    }
  ];
  selectedClient: Client;
  outletActive = false;
  tiles: any;
  kfAssessTiles = [
    {
      title: 'Report Management',
      routerLink: ['/clients/report-management'],
      logoUrl: 'assets/images/users.svg',
      allowedRoles: ['admin', 'reportManagement'] as AuthRole[]
    },
    {
      title: this.authenticationService.projectsAllowedOnlyForExtract ? 'Project Search' : 'Project Management',
      routerLink: ['/clients/client-projects'],
      logoUrl: 'assets/images/data-extracts.svg',
      allowedRoles: ['admin', 'productDelivery', 'projectParticipantManagement', 'dataScientist', 'participantExtract'] as AuthRole[]
    },
    {
      title: 'Participant search',
      routerLink: ['/clients/client-candidates'],
      logoUrl: 'assets/images/users.svg',
      allowedRoles: ['admin', 'productDelivery', 'projectParticipantManagement'] as AuthRole[]
    },
    {
      title: 'Client Customisations',
      routerLink: ['/clients/client-customisations'],
      logoUrl: 'assets/images/configurable.svg',
      allowedRoles: ['admin', 'reportManagement', 'usageReporting', 'productDelivery', 'projectParticipantManagement', 'dataScientist', 'participantExtract'] as AuthRole[]
    },
    {
      title: 'Usage Report',
      routerLink: ['/clients/usage-report'],
      logoUrl: 'assets/images/configurable.svg',
      allowedRoles: ['admin', 'usageReporting'] as AuthRole[]
    },
  ];
  spsTiles = [
    {
      title: 'StakeHolder Log',
      routerLink: ['/clients/sps-stakeholder-log'],
      logoUrl: 'assets/images/users.svg',
    },
    {
      title: 'Collaboration',
      routerLink: ['/clients/sps-collaboration-list'],
      logoUrl: 'assets/images/data-extracts.svg',
    }
  ];

  brandingRoutes = [
    {
      title: 'Assets',
      routerLink: ['/clients/branding-assets'],
      queryParams: {},
      allowedRoles: ['admin', 'reportManagement', 'productDelivery'] as AuthRole[]
    },
    {
      title: 'Branding',
      routerLink: ['/clients/report-branding'],
      queryParams: {},
      allowedRoles: ['admin', 'reportManagement', 'productDelivery'] as AuthRole[]
    },
  ];
  brandingRoute: boolean;
  tableData: Client[];
  snapshot: any;

  @HostBinding('class') get themeClass() {
    return this.outletActive ? '' : 'bg';
  }

  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;
  searchString = '';
  timer: any;
  isSuccessProfileSurvey = false;

  constructor(
    private clientSearchService: ClientSearchService,
    private authenticationService: AuthenticationService,
    private spinnerService: SpinnerService,
    private route: ActivatedRoute,
    private location: Location,
    private router: Router,
    private clientTilesService: ClientTilesService
  ) { }

  ngOnInit() {
    combineLatest(
      this.route.data,
      this.route.queryParams.pipe(
        map((data) => data.search || ''),
        tap((query) => (this.searchString = query))
      )
    ).subscribe(([resolved, query]) => {
      if (resolved.client || localStorage.getItem('selectedClient')) {
        if (!resolved.client) {
          this.isSuccessProfileSurvey = true;
        }
        this.selectedClient = this.selectedClient || resolved.client || JSON.parse(localStorage.getItem('selectedClient'));
        this.searchString = query || this.selectedClient.name;
        this.brandingRoutes.forEach(
          (route) => (route.queryParams = { clientId: this.selectedClient.id })
        );
      } else {
        if (query) {
          this.confirmSearch(query);
        }
      }
    });
  }

  /**
   * Method to search for clients based on name search string.
   * @param  searchString search parameter.
   */
  searchClients() {
    if (this.searchString.length <= 2) {
      this.searchResult = new MatTableDataSource<Client>();
      this.searchResult.data = null;
      return;
    }
    this.filtering = true;
    this.spinnerService.activate();
    this.location.go(`clients?search=${this.searchString}`);
    this.clientTilesService.tilesName.subscribe((data: string) => {
      if (data == ClientTypes.KFAS) {
        this.isSuccessProfileSurvey = false;
        this.columns = this.kfAssesscolumns;
        this.currentSubscription = this.clientSearchService
          .getClients(this.searchString)
          .subscribe(
            (result) => {
              this.tiles = this.kfAssessTiles;
              this.spinnerService.deactivate();
              this.filtering = false;
              this.tableData = result.clients.filter(
                (client) =>
                  client.name
                    .toLowerCase()
                    .indexOf(this.searchString.toLowerCase()) >= 0
              );
            },
            () => {
              this.spinnerService.deactivate();
              this.filtering = false;
              this.tableData = [];
            }
          );
      }
      else if (data == ClientTypes.SPS) {
        this.isSuccessProfileSurvey = true;
        this.columns = this.spsColumns;
        this.currentSubscription = this.clientSearchService.getSPSClient(this.searchString)
          .subscribe(
            (res) => {
              this.tiles = this.spsTiles;
              this.spinnerService.deactivate();
              this.filtering = false;
              if (res.data.clients != null) {
                this.spinnerService.deactivate();
                this.filtering = false;
                this.tableData = res.data.clients.filter(
                  (client: Client) =>
                    client.name
                      .toLowerCase()
                      .indexOf(this.searchString.toLowerCase()) >= 0);
              }
              else {
                this.spinnerService.deactivate();
                this.filtering = false;
                this.tableData = [];
              }
            },
            (error) => {
              console.error('Error in getClientSPS:', error);
              this.spinnerService.deactivate();
              this.filtering = false;
              this.tableData = [];
            }
          );
      }
    })
  }
     
  confirmSearch(searchKey: string) {
    this.filtering = true;
    this.searchString = searchKey.trim();
    this.searchClients();
  }

  debouncedSearchInput(msDuration: number, searchKey: string) {
    this.selectedClient = null;
    clearTimeout(this.timer);
    this.searchString = searchKey.trim();
    this.timer = setTimeout(() => this.searchClients.call(this), msDuration);
  }

  selectClient(client: Client) {
    this.selectedClient = client;
    this.brandingRoutes.forEach(
      (route) => (route.queryParams = { clientId: this.selectedClient.id })
    );
    this.location.go(
      `clients?search=${this.searchString}&clientId=${client.id}`
    );
  }

  selectSPSClient(client: Client) {
    this.selectedClient = client;
    const spsClient = {
      id: client.id,
      name: client.name
    };
    localStorage.setItem('selectedClient', JSON.stringify(spsClient));
    this.location.go(`clients?search=${this.searchString}`);
  }

  returnToClientSearch() {
    this.router.navigate(['/clients'], {
      queryParams: {
        search: this.searchString,
        clientId: this.selectedClient.id,
      },
    });
  }

  get isEmpty() {
    return !this.searchResult.data || !this.searchResult.data.length;
  }

  get flattiles() {
    return this.tiles
      .map((route) => [route])
      .reduce((prev, curr) => {
        prev.push(...curr);
        return prev;
      }, []);
  }

  get queryParams() {
    return { clientId: this.selectedClient.id };
  }

  ngOnDestroy() {
    // unsubscribe to ensure no memory leaks
    if (this.currentSubscription) {
      this.currentSubscription.unsubscribe();
    }
  }
}
