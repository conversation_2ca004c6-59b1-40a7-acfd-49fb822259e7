<div *ngIf="!outlet.isActivated">
  <div class="success-profile-survey" *ngIf="isSuccessProfileSurvey && selectedClient">
    <div class="sps-heading">SUCCESS PROFILE SURVEY</div>
    <span class="client">Client: </span>
    <span class="sps-client-name">{{ selectedClient.name }}</span>
  </div>

  <h2>Clients</h2>
  <div class="page-content weak-glass">
    <div class="search">
      <input
        type="search"
        class="glass"
        #searchInput
        placeholder="Search for clients"
        [value]="searchString"
        (input)="debouncedSearchInput(250, searchInput.value)"
        (keydown.enter)="
          $event.target.blur(); confirmSearch(searchInput.value); (false)
        "
        autofocus
      />
    </div>
    <div
      class="searchResults"
      [ngClass]="filtering ? 'loading' : null"
      *ngIf="!selectedClient && searchString.length > 2"
    >
      <kf-table
        *ngIf="tableData"
        [data]="tableData"
        [columns]="columns"
        [actions]="actions"
      ></kf-table>
    </div>
  </div>

  <div *ngIf="selectedClient">
    <a *ngIf="!isSuccessProfileSurvey"
      [routerLink]="['/clients/client-details']"
      [queryParams]="{ clientId: selectedClient.id }"
    >
      <h2>
        {{ selectedClient.name }}
      </h2>
    </a>
    <a *ngIf="isSuccessProfileSurvey">
      <h2>
        {{ selectedClient.name }}
      </h2>
    </a>
    <kf-tiles
      [(tiles)]="tiles" 
      [queryParams]="{ clientId: selectedClient.id }"
    ></kf-tiles>
  </div>
</div>

<page-header *ngIf="!isSuccessProfileSurvey && outlet.isActivated"></page-header>
<sps-page-header *ngIf="isSuccessProfileSurvey && outlet.isActivated"></sps-page-header>

<router-outlet
  #outlet="outlet"
  (activate)="outletActive = true"
  (deactivate)="outletActive = false"
></router-outlet>
  