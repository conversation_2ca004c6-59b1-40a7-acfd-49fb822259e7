import { KFTableAction, KFTableColumn } from '@/components/controls';
import { AuthenticationService, ClientSearchService, SpinnerService } from '@/services';
import { AuthRole, Client } from '@/shared/models';
import { Location } from '@angular/common';
import {
  Component,
  HostBinding,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { MatPaginator, MatSort, MatTableDataSource } from '@angular/material';
import { ActivatedRoute, Router } from '@angular/router';
import { combineLatest, Subscription } from 'rxjs';
import { map, tap } from 'rxjs/operators';

@Component({
  selector: 'app-client-customisations',
  templateUrl: './client-customisations.component.html',
  styleUrls: ['./client-customisations.component.scss']
})
export class ClientCustomisationsComponent implements OnInit, OnDestroy  {

  currentSubscription: Subscription;
  searchResult: MatTableDataSource<Client> = new MatTableDataSource<Client>();
  columns: KFTableColumn<Client>[] = [
    {
      name: 'id',
      label: 'id',
      type: 'text',
      width: '150px',
      sortable: true,
    },
    {
      name: 'name',
      label: 'name',
      type: 'text',
      sortable: true,
      click: (client: Client) => {
        this.selectClient(client);
      },
    },
    {
      name: 'dateCreated',
      label: 'Date created',
      type: 'date',
      dateFormat: 'MMM dd, yyyy',
      sortable: true,
      width: '150px',
    },
    {
      name: 'actions',
      label: 'actions',
      type: 'rowOptions',
      width: '150px',
    },
  ];
  actions: KFTableAction<Client>[] = [
    {
      label: 'Client details',
      icon: 'list_alt',
      click: (client: Client) => {
        this.selectClient(client);
        this.router.navigate(['/clients/client-details'], {
          queryParams: {
            clientId: client.id,
          },
        });
      },
    },
  ];
  loading = false;
  filtering = false;
  filteredClients: Client[];
  displayedColumns: string[] = ['id', 'name', 'dateCreated'];
  selectedClient: Client;
  outletActive = false;
  tiles = [
    {
      title: 'Custom Branding',
      routerLink: ['/clients/report-branding'],
      logoUrl: 'assets/images/configurable.svg',
      allowedRoles: ['admin', 'reportManagement', 'productDelivery'] as AuthRole[],
      bgColor: 'green'
    },
    {
      title: 'Report Preview',
      routerLink: ['/clients/report-preview-download'],
      logoUrl: 'assets/images/configurable.svg',
      allowedRoles: ['admin', 'reportManagement'] as AuthRole[],
      bgColor: 'yellow',
    },
    {
      title: 'Manage norms',
      routerLink: ['/clients/client-norms'],
      logoUrl: 'assets/images/configurable.svg',
      allowedRoles: ['admin', 'productDelivery'] as AuthRole[],
      bgColor: 'pink',
    },
    {
      title: 'Custom Project Types',
      routerLink: ['/clients/client-custom-project-types'],
      logoUrl: 'assets/images/configurable.svg',
      allowedRoles: ['admin', 'reportManagement',  'usageReporting', 'productDelivery', 'projectParticipantManagement', 'dataScientist', 'participantExtract'] as AuthRole[],
      bgColor: 'purple',
    },
  ];
  brandingRoutes = [
    {
      title: 'Assets',
      routerLink: ['/clients/branding-assets'],
      queryParams: {},
      allowedRoles: ['admin', 'reportManagement', 'productDelivery'] as AuthRole[]
    },
    {
      title: 'Branding',
      routerLink: ['/clients/report-branding'],
      queryParams: {},
      allowedRoles: ['admin', 'reportManagement', 'productDelivery'] as AuthRole[]
    },
  ];
  brandingRoute: boolean;
  tableData: Client[];
  snapshot: any;

  @HostBinding('class') get themeClass() {
    return this.outletActive ? '' : 'bg';
  }

  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;
  searchString = '';
  timer: any;

  constructor(
    private clientSearchService: ClientSearchService,
    private authenticationService: AuthenticationService,
    private spinnerService: SpinnerService,
    private route: ActivatedRoute,
    private location: Location,
    private router: Router
  ) {}

  ngOnInit() {
    combineLatest(
      this.route.data,
      this.route.queryParams.pipe(
        map((data) => data.search || ''),
        tap((query) => (this.searchString = query))
      )
    ).subscribe(([resolved, query]) => {
      if (resolved.client) {
        this.selectedClient = this.selectedClient || resolved.client;
        this.searchString = query || this.selectedClient.name;
        this.brandingRoutes.forEach(
          (route) => (route.queryParams = { clientId: this.selectedClient.id })
        );
      } else {
        if (query) {
          this.confirmSearch(query);
        }
      }
    });
  }

  /**
   * Method to search for clients based on name search string.
   * @param  searchString search parameter.
   */
  searchClients() {
    if (this.searchString.length <= 2) {
      this.searchResult = new MatTableDataSource<Client>();
      this.searchResult.data = null;
      return;
    }
    this.filtering = true;

    this.spinnerService.activate();
    this.location.go(`clients?search=${this.searchString}`);
    this.currentSubscription = this.clientSearchService
      .getClients(this.searchString)
      .subscribe(
        (result) => {
          this.spinnerService.deactivate();
          this.filtering = false;
          this.tableData = result.clients.filter(
            (client) =>
              client.name
                .toLowerCase()
                .indexOf(this.searchString.toLowerCase()) >= 0
          );
        },
        () => {
          this.spinnerService.deactivate();
          this.filtering = false;
          this.tableData = [];
        }
      );
  }

  confirmSearch(searchKey: string) {
    this.filtering = true;
    this.searchString = searchKey.trim();
    this.searchClients();
  }

  debouncedSearchInput(msDuration: number, searchKey: string) {
    this.selectedClient = null;
    clearTimeout(this.timer);
    this.searchString = searchKey.trim();
    this.timer = setTimeout(() => this.searchClients.call(this), msDuration);
  }

  selectClient(client: Client) {
    this.selectedClient = client;
    this.brandingRoutes.forEach(
      (route) => (route.queryParams = { clientId: this.selectedClient.id })
    );
    this.location.go(
      `clients?search=${this.searchString}&clientId=${client.id}`
    );
  }

  returnToClientSearch() {
    this.router.navigate(['/clients'], {
      queryParams: {
        search: this.searchString,
        clientId: this.selectedClient.id,
      },
    });
  }

  get queryParams() {
    return { clientId: this.selectedClient.id };
  }

  ngOnDestroy() {
    // unsubscribe to ensure no memory leaks
    if (this.currentSubscription) {
      this.currentSubscription.unsubscribe();
    }
  }
}
