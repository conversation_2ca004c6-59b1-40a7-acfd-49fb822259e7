import { KFTableColumn } from "@/components/controls";

export const SPS_COLLABORATION_COLUMNS: KFTableColumn<any>[] = [
    {
        name: 'status',
        label: 'Status',
        type: 'text',
        width: '100px',
        sortable: true,
    },
    {
        name: 'collabId',
        label: 'ID',
        type: 'text',
        sortable: true,
        width: '65px',
    },
    {
        name: 'spTitle',
        label: 'NAME',
        type: 'text',
        sortable: true,
        width: '25%',
    },
    {
        name: 'sourceSystemName',
        label: 'SOURCE SYSTEM',
        type: 'text',
        sortable: true,
    },
    {
        name: 'createdDate',
        label: 'CREATED Date',
        type: 'date',
        dateFormat: 'MMM dd, yyyy',
        sortable: true,
    },
    {
        name: 'numberOfStakeholders',
        label: 'STAKEHOLDER COUNT',
        type: 'text',
        width: '200px',
        sortable: true,
    },

];
