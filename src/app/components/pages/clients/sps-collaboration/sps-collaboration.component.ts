import { Component, OnInit } from '@angular/core';
import { SPS_COLLABORATION_COLUMNS } from './sps-collaboration-columns';
import { Subscription } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { SPSCollaborationService } from '@/services/sps-collaboration.service';
import { PageHeaderService, SpinnerService } from '@/services';
import { CollaborationList, CollaborationListResponse } from '@/shared/models/sps-client/collaboration-list';
import { KFTableColumn } from '@/components/controls';

@Component({
  selector: 'app-sps-collaboration',
  templateUrl: './sps-collaboration.component.html',
  styleUrls: ['./sps-collaboration.component.scss']
})
export class SpsCollaborationComponent implements OnInit {
  tableData: CollaborationList[];
  columns = SPS_COLLABORATION_COLUMNS;
  clientId: string;
  currentSubscription: Subscription;
  customPaging = {
    pageIndex: 1,
    pageSize: 10,
    totalPages: null,
    totalResultRecords: null,
  };
  constructor(private activatedRoute: ActivatedRoute, private pageHeaderService:PageHeaderService, private spsCollaborationService: SPSCollaborationService, private router: Router, private route: ActivatedRoute, private spinnerService: SpinnerService) { }

  ngOnInit() {
    this.activatedRoute.queryParams.subscribe(params => {
      this.clientId = params['clientId'];
    })
    if (!this.clientId) {
      const selectedSpsClient = JSON.parse(localStorage.getItem('selectedClient'));
      this.clientId = selectedSpsClient ? selectedSpsClient.id : '';
    }
    this.pageHeaderService.setSPSDetails('Collaboration Log')
    this.getData(this.clientId);
    this.columns.forEach((column: KFTableColumn<CollaborationList>) => {
      if (column.name === 'collabId' || column.name === 'spTitle') {
        column.click = (t: any) => {
          this.router.navigate(['../sps-collaboration-details'], {
            relativeTo: this.route,
            queryParams: {
              collabId: t.collabId
            },
          });


        }
        column.url = (t: any) =>
          this.router
            .createUrlTree(['../sps-collaboration-details'], {
              relativeTo: this.route,
              queryParams: {
                collabId: t.collabId
              },
            })
            .toString();
      }
    });
  }

  getData(clientId: string) {
    this.getPaginationData(clientId);
  }

  onPageClick(event) {
    if (
      this.customPaging.pageSize !== event.pageSize ||
      this.customPaging.pageIndex !== event.pageIndex + 1
    ) {
      this.customPaging.pageSize = event.pageSize;
      this.customPaging.pageIndex = event.pageIndex + 1;
      this.getPaginationData(this.clientId, event.pageIndex + 1, event.pageSize);
    }
    
  }

  getPaginationData(clientId: string, pageIndex?: number, pageSize?: number) {
    const resolvedPageIndex = pageIndex === undefined ? 1 : pageIndex;
    const resolvedPageSize = pageSize === undefined ? 10 : pageSize;
    this.spinnerService.activate();
    this.currentSubscription = this.spsCollaborationService.getCollaborationListData(clientId, resolvedPageIndex, resolvedPageSize).subscribe((response: CollaborationListResponse) => {
      this.customPaging = {
        pageIndex: resolvedPageIndex,
        pageSize: resolvedPageSize,
        totalResultRecords: response.data.totalRecords,
        totalPages: response.data.totalRecords / resolvedPageSize
      }
      this.spinnerService.deactivate();
      this.tableData = response.data.collabListResponse;
    }),
      (error: Error) => {
        console.error(error);
        this.spinnerService.deactivate();
        this.tableData = []; 
      }
  }

  ngOnDestroy() {
    if (this.currentSubscription) {
      this.currentSubscription.unsubscribe();
    }
  }
}
