@import "../../../../../styles/colors.scss";
@import "../../../../../styles/theme.scss";
@import "../../../../../styles/typography.scss";

:host {
  .accordion-group {
    &-header {
      display: flex;
      align-items: center;
      padding: 1rem 0;
      color: $primary--blue;

      &.disabled {
        color: $primary--grey;
        cursor: default;
      }

      .mat-icon {
        font-size: 1.5rem;
        line-height: 1.5rem;
        height: 1.5rem;
        margin: 0 1rem;
        transform-origin: center center;
        transition: 0.2s;

        &.expanded {
          transform: rotate(90deg);
        }
      }

      h5 {
        text-transform: uppercase;
        letter-spacing: 1.5px;
        line-height: 1.5rem;
        font-size: 0.9rem;
        margin-right: 1rem;

        .badge {
          margin-left: 1rem;
        }
      }
    }

    &:not(:last-of-type),
    &:not(.expanded) {
      border-bottom: solid thin rgba(0, 0, 0, 0.15);
    }
  }
}
