<div class="page-content white-glass">
  <div
    *ngFor="let group of scheduleGroups"
    [class.expanded]="group.expanded"
    class="accordion-group"
  >
    <a
      class="accordion-group-header"
      [class.disabled]="!group.schedules.length"
      (click)="group.schedules.length && group.expanded = !group.expanded"
    >
      <mat-icon [class.expanded]="group.expanded">chevron_right</mat-icon>
      <h5>
        {{ group.header }}
        <span *ngIf="group.schedules.length" class="badge">{{ group.schedules.length }}</span>
      </h5>
    </a>

    <div *ngIf="group.expanded" class="accordion-group-body">
      <kf-table
        size="small"
        [noMaxHeight]="true"
        [data]="group.schedules"
        [columns]="columns"
      ></kf-table>
    </div>
  </div>
</div>

<ng-template #DETAILS_TPL let-cell>
  <pre>{{ cell.element | scheduleDetails }}</pre>
</ng-template>

<ng-template #PPT_CONFIG_TPL let-cell>
  <div>{{ cell.element.participantSendConfig | scheduleParticipantConfig }}</div>
</ng-template>
