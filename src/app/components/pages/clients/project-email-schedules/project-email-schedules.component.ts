import { KFTableColumn } from "@/components/controls";
import { EmailTemplateComponent } from "@/components/dialogs";
import { AlertService, ProjectService, SpinnerService } from "@/services";
import {
  ProjectEmailSchedule,
  templateTypes,
  TemplateTypes
} from "@/shared/models/ProjectEmailSchedule";
import { Component, OnInit, TemplateRef, ViewChild } from "@angular/core";
import { MatDialog } from "@angular/material";
import { ActivatedRoute } from "@angular/router";

/**
 * Project email schedules component.
 */
@Component({
  selector: "app-project-email-schedules",
  templateUrl: "./project-email-schedules.component.html",
  styleUrls: ["./project-email-schedules.component.scss"],
})
export class ProjectEmailSchedulesComponent implements OnInit {
  scheduleGroups: { header: any; expanded: boolean; schedules: any[] }[];

  @ViewChild("DETAILS_TPL") DETAILS_TPL: TemplateRef<any>;
  @ViewChild("PPT_CONFIG_TPL") PPT_CONFIG_TPL: TemplateRef<any>;
  private invitationType: TemplateTypes  = 'INVITATION';
  private invitationSSOType: TemplateTypes = 'INVITATION_SSO'; 
  private reminderType: TemplateTypes = 'REMINDER';
  private reminderSSOType: TemplateTypes = 'REMINDER_SSO';

  constructor(
    private route: ActivatedRoute,
    private projectService: ProjectService,
    private spinnerService: SpinnerService,
    private alertService: AlertService,
    private dialog: MatDialog
  ) {}

  get columns(): KFTableColumn<ProjectEmailSchedule>[] {
    return [
      {
        type: "text",
        name: "settingName",
        label: "Name",
        click: this.openSchedule.bind(this),
      },
      {
        type: "text",
        name: "pptConfig",
        label: "Participants Config",
        template: this.PPT_CONFIG_TPL,
      },
      {
        type: "text",
        name: "settingType",
        label: "Trigger Event",
      },
      {
        type: "text",
        name: "details",
        label: "details",
        template: this.DETAILS_TPL,
      },
    ];
  }

  ngOnInit() {
    this.initScheduleGroups();
  }

  initScheduleGroups() {
    let schedules = this.route.snapshot.data.schedules;
    schedules = this.extractSSOTemplates(schedules, this.invitationType, this.invitationSSOType);
    schedules = this.extractSSOTemplates(schedules, this.reminderType, this.reminderSSOType);
    
    this.scheduleGroups = Object.keys(templateTypes).map((key) => ({
      header: templateTypes[key],
      expanded: false,
      schedules: schedules.filter((schedule) => {
        return schedule.templateType === key;
      }),
    }));
  }

  openSchedule(schedule: ProjectEmailSchedule) {
    this.spinnerService.activate();
    this.projectService
      .getNotificationTemplate(
        schedule.templateId,
        this.route.snapshot.data.client.externalRef
      )
      .subscribe((template) => {
        this.spinnerService.deactivate();
        this.dialog.open(EmailTemplateComponent, {
          width: "66%",
          data: { template, schedule }, // TODO use actual template
        });
      }, (error) => {
        this.spinnerService.deactivate();
        this.alertService.error(error.message)
      });
  }

  extractSSOTemplates(schedules: ProjectEmailSchedule[], templateType: TemplateTypes, templateSSOType: TemplateTypes){
    if (this.route.snapshot.data.project.ssoSetting === "NONE") {
      return schedules;
    }
   
    let templatesSSO = schedules.filter((schedule) => {
      return schedule.templateType === templateType && schedule.templateId !== schedule.templateSsoId;
    });

    templatesSSO = templatesSSO.map((schedule) => {
      let ssoSchedule = Object.assign({}, schedule);
      ssoSchedule.templateType = templateSSOType;
      ssoSchedule.templateId = schedule.templateSsoId;
      return ssoSchedule;
    });

    if (this.route.snapshot.data.project.ssoSetting === "SSO_ONLY") {
      schedules = schedules.filter(s => s.templateType !== templateType)
    }

    schedules = schedules.concat(templatesSSO);   

    return schedules;
  }
}
