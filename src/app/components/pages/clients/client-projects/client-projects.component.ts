import {
  KFTableAction,
  KFTableColumn,
} from '@/components/controls/kf-table/kf-table.component';
import { AlertService, AuthenticationService, ProjectService, SpinnerService } from '@/services';
import { AuthRole, Client, ClientProjects, ProjectSummary } from '@/shared/models';
import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { MatPaginator, MatSort, Sort } from '@angular/material';
import { ActivatedRoute } from '@angular/router';
import { Router } from '@angular/router';
import * as _ from 'lodash';
import { PROJECT_TABLE_COLUMNS } from './client-projects.columns';
import { Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter } from 'rxjs/operators';
import { MatDialog } from '@angular/material/dialog';
import { DeleteProjectDialogComponent } from '../../../dialogs/delete-project-dialog/delete-project-dialog.component';

/** Component to display client projects. */
@Component({
  selector: 'app-client-projects',
  templateUrl: './client-projects.component.html',
  styleUrls: ['./client-projects.component.scss'],
})
export class ClientProjectsComponent implements OnInit {
  client: Client;
  clientProjects: ClientProjects;
  columns = PROJECT_TABLE_COLUMNS;
  actions: KFTableAction<any>[] = [
    {
      displayCondition: (t: any) =>
        this.authenticationService.isInAllowedRoles([
          "admin",
          "productDelivery",
          "projectParticipantManagement",
        ] as AuthRole[]),
      icon: 'list_alt',
      label: 'Project Details',
      css: 'default',
      click: (t: any) => {
        this.router.navigate(['../project-details'], {
          relativeTo: this.route,
          queryParams: {
            clientId: this.client.id,
            projectId: t.projectId,
            selectedProjectId: '',
            status: t.statusConstant
          },
        });
      },
      url: (t: any) =>
        this.router
          .createUrlTree(['../project-details'], {
            relativeTo: this.route,
            queryParams: {
              clientId: this.client.id,
              projectId: t.projectId,
              selectedProjectId: '',
              status: t.statusConstant
            },
          })
          .toString(),
    },
    {
      displayCondition: (t: any) =>
        this.authenticationService.isInAllowedRoles([
          "admin",
          "productDelivery",
          "projectParticipantManagement",
        ] as AuthRole[]),
      icon: 'people',
      label: 'View Participants',
      css: 'default',
      click: (t: any) => {
        this.router.navigate(['../project-candidates'], {
          relativeTo: this.route,
          queryParams: {
            clientId: this.client.id,
            projectId: t.projectId,
            selectedProjectId: '',
          },
        });
      },
      url: (t: any) =>
        this.router
          .createUrlTree(['../project-candidates'], {
            relativeTo: this.route,
            queryParams: {
              clientId: this.client.id,
              projectId: t.projectId,
              selectedProjectId: '',
            },
          })
          .toString(),
    },
    {
      displayCondition: (t: any) =>
        this.authenticationService.isInAllowedRoles([
          "admin",
          "productDelivery",
          "projectParticipantManagement",
        ] as AuthRole[]),
      icon: 'email',
      label: 'Email Schedules',
      css: 'default',
      click: (t: any) => {
        this.router.navigate(['../project-email-schedules'], {
          relativeTo: this.route,
          queryParams: {
            clientId: this.client.id,
            projectId: t.projectId,
            selectedProjectId: '',
          },
        });
      },
      url: (t: any) =>
        this.router
          .createUrlTree(['../project-email-schedules'], {
            relativeTo: this.route,
            queryParams: {
              clientId: this.client.id,
              projectId: t.projectId,
              selectedProjectId: '',
            },
          })
          .toString(),
    },
    {
      displayCondition: (t: any) =>
        t.projectType.toUpperCase() !== "SJT" &&
        t.projectType.toUpperCase() !== "POTENTIAL" &&
        this.authenticationService.isInAllowedRoles([
          "admin",
          "productDelivery",
          "participantExtract",
          "dataScientist",
        ] as AuthRole[]),
      icon: 'file_upload',
      label: 'Participant Details Extract',
      css: 'default',
      click: (t: any) => {
        this.router.navigate(['../data-extracts-upload'], {
          relativeTo: this.route,
          queryParams: {
            clientId: this.client.id,
            projectId: t.projectId,
            selectedProjectId: '',
          },
        });
      },
      url: (t: any) =>
        this.router
          .createUrlTree(['../data-extracts-upload'], {
            relativeTo: this.route,
            queryParams: {
              clientId: this.client.id,
              projectId: t.projectId,
              selectedProjectId: '',
            },
          })
          .toString(),
    },
     {
      displayCondition: (t: any) =>
        //need to update the isInAllowedRoles to systemAdmin from KFAS-13855
        this.authenticationService.isInAllowedRoles(["admin", "projectAdmin"] as AuthRole[]),
      icon: "delete",
      label: "Delete",
      css: "default",
      click: (t: any) => {
        const dialogRef = this.dialog.open(DeleteProjectDialogComponent, {
          width: "500px",
          data: {
            projectId: t.projectId,
            projectName: t.name,
          },
        });

        dialogRef.afterClosed().subscribe((result) => {
          if (result === "deleted") {
            this.alertService.success("Project deleted successfully.");
            this.retrieveProjects(
              this.searchkey,
              this.customPaging.pageIndex,
              this.customPaging.pageSize
            );
          } else if (result && result.error) {
            this.alertService.error(result.error);
          }
        });
      },
    },
  ];

  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;
  @ViewChild('ENDDATETIME_TPL') ENDDATETIME_TPL: TemplateRef<any>;

  loading: boolean;
  tableData: any[];

  filter = {};
  filterOptions = {
    projectTypes: [],
  };
  searchkey = '';
  private searchTerms = new Subject<string>();
  customPaging = {
    pageIndex: 1,
    pageSize: 10,
    totalPages: null,
    totalResultRecords: null,
  };
  debounced: any;
  sortColumn: string = 'createdDate';
  sortBy: string = 'desc';

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private spinnerService: SpinnerService,
    private projectService: ProjectService,
    private authenticationService: AuthenticationService,
    private alertService: AlertService,
    private dialog: MatDialog
  ) {}

  ngOnInit() {
    this.route.data.subscribe(
      (result) => {
        this.client = result.client;
        this.filterOptions = this.mapMetadataToFilterOptions(
          result.projectSearchMetadata.data.metadata[0]
        );

        this.retrieveProjects();
      },
      (error) => {
        this.alertService.error(error.message);
      }
    );

    this.columns.forEach((column: KFTableColumn<any>) => {
      if (!this.authenticationService.projectsAllowedOnlyForExtract && (column.name === 'name' || column.name === 'projectId')) {
        column.click = (t: any) =>
          this.router.navigate(['../project-details'], {
            relativeTo: this.route,
            queryParams: {
              clientId: this.client.id,
              projectId: t.projectId,
              status: t.statusConstant
            },
          });
        column.url = (t: any) =>
          this.router
            .createUrlTree(['../project-details'], {
              relativeTo: this.route,
              queryParams: {
                clientId: this.client.id,
                projectId: t.projectId,
                status: t.statusConstant
              },
            })
            .toString();
      }
    });

    // filter project with keyword
    this.searchTerms.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      filter(term => (term.length > 2 || term.length === 0))
    ).subscribe(term => {
      this.retrieveProjects(term, this.customPaging.pageIndex, this.customPaging.pageSize);
    });
  }

  mapMetadataToFilterOptions(metadata) {
    return {
      projectTypes: metadata.searchOn
        .find((m) => m.name === 'PROJECTTYPES')
        .options.map((option) => ({
          id: option.id,
          value: option.name,
        })),
    };
  }

  onSearch(term: string): void {
    this.customPaging.pageIndex = 1; // Reset to the first page on search
    this.searchTerms.next(term);
  }

  onFilterChange(filterType, values) {
    this.filter[filterType] = values;
    this.retrieveProjects();
  }

  onPageChange(event) {
    if (
      this.customPaging.pageSize !== event.pageSize ||
      this.customPaging.pageIndex !== event.pageIndex + 1
    ) {
      this.customPaging.pageSize = event.pageSize;
      this.customPaging.pageIndex = event.pageIndex + 1;
      this.retrieveProjects('', this.customPaging.pageIndex, this.customPaging.pageSize)
    }
  }

  sortData(sort: Sort) {
    if (sort.active === 'createdDateTime') {
      sort.active = 'createdDate';
    }
    this.sortColumn = sort.active;
    this.sortBy = sort.direction;

    this.customPaging.pageIndex = 1; // Reset to the first page on sorting change
    this.retrieveProjects(this.searchkey, this.customPaging.pageIndex, this.customPaging.pageSize);
  }

  retrieveProjects(searchTerm?: string, pageIndex?: number, pageSize?: number) {
    const resolvedPageIndex = pageIndex === undefined ? 1 : pageIndex;
    const resolvedPageSize = pageSize === undefined ? 10 : pageSize;
    this.spinnerService.activate();
    this.projectService
      .getProjects(this.client.id, this.filter, searchTerm, resolvedPageIndex, resolvedPageSize, this.sortColumn, this.sortBy)
      .subscribe((result: any) => {
        this.tableData = this.resolveProjectVersion(result.data);
        this.customPaging = result.paging;
        this.spinnerService.deactivate();
      });
  }

  resolveProjectVersion(projects: ProjectSummary[]): ProjectSummary[] {
    projects.map(project => {
      project.version = (project.isKFAssess2) ? "2.0" : "1.0";
    });

    return projects;
  }
  
}
