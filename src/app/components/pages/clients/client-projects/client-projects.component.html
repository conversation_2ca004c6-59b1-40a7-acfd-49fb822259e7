<div class="page-content">
  <div class="table-actions-panel">
    <mat-icon class="input-icon">search</mat-icon>
    <input
      type="search"
      class="strong-glass with-icon"
      placeholder="Search projects"
      (input)="onSearch($event.target.value)"
    />

    <kf-select
      class="strong-glass"
      placeholder="Project Type"
      [options]="filterOptions.projectTypes"
      (selectionChange)="onFilterChange('projectTypes', $event)"
    ></kf-select>
  </div>

  <div class="client-reports">
    <kf-table
      [data]="tableData || []"
      [columns]="columns"
      [actions]="actions"
      [customPaging]="customPaging"
      (paginatorChanged)="onPageChange($event)"
      (serverSortChanged)="sortData($event)"
      [useServerSorting]="true"
    ></kf-table>
    
  </div>
</div>

<ng-template #ENDDATETIME_TPL let-cell>
  <span *ngIf="cell.element.endDateTime">
    {{ cell.element.endDateTime | date: "MMM dd, yyyy" }}</span
  >
  <span *ngIf="!cell.element.endDateTime" class="grey"> No end date</span>
</ng-template>
