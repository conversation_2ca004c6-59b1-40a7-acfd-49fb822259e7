import { KFTableColumn } from '@/components/controls/kf-table/kf-table.component';
import { ProjectSummary } from '@/shared/models';

export const PROJECT_TABLE_COLUMNS: KFTableColumn<ProjectSummary>[] = [
  {
    name: 'status',
    label: 'Status',
    type: 'text',
    width: '100px',
    sortable: false,
  },
  {
    name: 'projectId',
    label: 'ID',
    type: 'text',
    sortable: true,
    width: '65px',
  },
  {
    name: 'name',
    label: 'NAME',
    type: 'text',
    sortable: true,
    width: '25%',
  },
  {
    name: 'projectType',
    label: 'TYPE',
    type: 'text',
    sortable: true,
    width: '180px',
  },
  {
    name: 'createdDateTime',
    label: 'CREATED Date',
    type: 'date',
    dateFormat: 'MMM dd, yyyy',
    sortable: true,
  },
  {
    name: 'endDateTime',
    label: 'End Date',
    type: 'date',
    dateFormat: 'MMM dd, yyyy',
    sortable: false,
  },
  {
    name: 'version',
    label: 'Version',
    type: 'text',
    sortable: false,
  },
  {
    name: 'participantCount',
    label: 'Participant count',
    type: 'text',
    width: '160px',
    sortable: true,
  },
  {
    name: 'actions',
    label: 'Actions',
    type: 'rowOptions',
    width: '150px',
  },
];
