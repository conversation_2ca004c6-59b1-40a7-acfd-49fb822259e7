import {
  SpinnerService,
  AlertService,
  AuthenticationService
} from '@/services';
import { ClientDetails } from '@/shared/models';
import { MatSlideToggleChange, MatDialog, MatDialogRef} from '@angular/material';

import { Component, HostBinding, OnInit, OnDestroy } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import * as _ from 'lodash';
import { 
  AssessmentsObject,
  AssessmentSelectedObj,
  CognitiveAssessmentsFields,
  OtherAssessmentsFields,
  assessmentOptionsObj,
  AssessmentOptions,
  CustomProjectTypeObj
} from '@/shared/models/customProjectTypeDetails';
import { CPTHistoryDialogComponent } from '../../../dialogs/cpt-history-dialog/cpt-history-dialog.component';
import { CPTAmendmentReasonDialogComponent } from '../../../dialogs/cpt-amend-reason-dialog/cpt-amend-reason-dialog.component';
import { Subscription } from 'rxjs';
import { CPTReportManagementConfirmationComponent } from '../../../dialogs/cpt-report-management-confirmation/cpt-report-management-confirmation.component';
import { CustomProjectTypeService } from './../../../../services/custom-project-type.service';

@Component({
  selector: 'app-custom-project-type-details',
  templateUrl: './custom-project-type-details.component.html',
  styleUrls: ['./custom-project-type-details.component.scss']
})
export class CustomProjectTypeDetailsComponent implements OnInit, OnDestroy {
  currentSubscriptions: Subscription[] = [];

  public error: string;
  client: ClientDetails;
  projectType: CustomProjectTypeObj;
  loading = false;
  form: FormGroup;
  cognitiveAssessmentsFields: AssessmentsObject[] = [];
  otherAssessmentsFields: AssessmentsObject[] = [];
  assessmentOptions: assessmentOptionsObj[] = [];
  requiredFields: AssessmentsObject[] = [];
  recommendedFields: AssessmentsObject[] = [];
  optionalFields: AssessmentsObject[] = [];
  basedOnSPFields: AssessmentsObject[] = [];
  assessmentsSelected: AssessmentSelectedObj[] = [];
  isSaveEnabled = false;
  projectTypeId: number = null;
  dialogRef: MatDialogRef<any, any>;
  private clientProjectTypes: CustomProjectTypeObj[] = [];
  
  isDropdownOpen = false;
  userGroupOptions: { value: string, label: string }[] = [];
  selectedUserGroups: string[] = ['ALL'];

  get formattedError() {
    return this.error ? this.error.split('\n') : [];
  }

  @HostBinding('class') get themeClass() {
    return this.loading ? 'loading' : '';
  }

  constructor(
    private fb: FormBuilder,
    private spinnerService: SpinnerService,
    private authenticationService: AuthenticationService,
    private apiService: CustomProjectTypeService,
    private alertService: AlertService,
    private route: ActivatedRoute,
    private router: Router,
    public dialog: MatDialog,
  ) {
    this.cognitiveAssessmentsFields = CognitiveAssessmentsFields;
    this.otherAssessmentsFields = OtherAssessmentsFields;
    this.assessmentOptions = AssessmentOptions;
  }

  ngOnInit() {
    this.spinnerService.activate();
    this.currentSubscriptions.push(
      this.route.queryParams.subscribe(params => {
        if (params.projectTypeId) {
          this.projectTypeId = params.projectTypeId;
        }
      })
    );

    this.form = this.fb.group({
      projectTypeName: ['', [Validators.required, Validators.minLength(5), Validators.maxLength(50)]],
      elementsNumerical: ['NotIncluded', Validators.required],
      elementsVerbal: ['NotIncluded', Validators.required],
      elementsLogical: ['NotIncluded', Validators.required],
      aspectsNumerical: ['NotIncluded', Validators.required],
      aspectsVerbal: ['NotIncluded', Validators.required],
      aspectsChecking: ['NotIncluded', Validators.required],
      abstractReasoning: ['NotIncluded', Validators.required],
      competencies: ['NotIncluded', Validators.required],
      traits: ['NotIncluded', Validators.required],
      drivers: ['NotIncluded', Validators.required],
      tsi: ['NotIncluded', Validators.required],
      experiences: ['NotIncluded', Validators.required],
      rolePreferences: ['NotIncluded', Validators.required],
      inclusiveLeaderSJT: ['NotIncluded', Validators.required],
      scoring:['Not_Assigned', Validators.required],
      useSuccessProfile: [false],
      usePotentialFunctionality: [false],
    });

    const fieldsToSubscribe = Object.keys(this.form.controls).filter(fieldName => fieldName !== 'scoring');

    fieldsToSubscribe.forEach((fieldName) => {
      this.form.get(fieldName).valueChanges.subscribe(() => {
        this.updateFields();
      });
    });

    this.loadValuesToAmend();
  }

  toggleDropdown() {
      this.isDropdownOpen = !this.isDropdownOpen;
    }

  fetchUserGroupNames() {
    if (!this.client || !this.client.id) {
      this.userGroupOptions = [];
      return;
    }
    this.apiService.getUserGroups(this.client.id).subscribe(
      (response) => {
        const groups = response && response.data && response.data.userGroups || [];
        this.userGroupOptions = groups
          .filter(group => group.userGroupName.toUpperCase() !== 'ALL')
          .map(group => ({
            value: group.userGroupId.toString(),
            label: group.userGroupName
          }));
      },
      (error) => {
        console.error('Error fetching usergroup data:', error);
        this.userGroupOptions = [];
      }
    );
  }

  selectAllUserGroups(checked: boolean) {
    this.selectedUserGroups = ['ALL'];
    this.checkSaveButton();
  }

  isAllUserGroupSelected() {
    return (this.selectedUserGroups.length === 0
        || (this.selectedUserGroups.length === 1 && this.selectedUserGroups[0].toUpperCase() === 'ALL'));
  }

  getUserGroupLabel() {
    if (this.selectedUserGroups.length === 0) {
      return 'ALL';
    } else if (this.selectedUserGroups.length === 1) {
      const userGroup = _.find(this.userGroupOptions, {'value': this.selectedUserGroups[0]});
      return userGroup ? userGroup.label : 'ALL';
    } else {
      return this.selectedUserGroups.length + ' User Groups selected';
    }
  }

  updateSelectedUserGroups(value: string, checked: boolean) {
    if (checked) {
      if (this.selectedUserGroups[0] === '' || this.selectedUserGroups[0].toUpperCase() === 'ALL') {
        this.selectedUserGroups[0] = value;
      } else {
        this.selectedUserGroups.push(value);
      }
    } else {
      const index = this.selectedUserGroups.indexOf(value);
      if (index > -1) {
        this.selectedUserGroups.splice(index, 1);
      }
      if (this.selectedUserGroups.length === 0) {
        this.selectedUserGroups = [''];
      }
    }
    this.checkSaveButton();
  }
  
  loadValuesToAmend() {
    this.currentSubscriptions.push(
      this.route.data.subscribe((resolved) => {
        this.spinnerService.deactivate();
        Object.assign(this, resolved);
        this.client = resolved.client;
        this.fetchUserGroupNames();
        this.projectType = resolved.projectType;
        if(this.projectType) {
          this.setCPTValues();
        }
      })
    );
  }

  setCPTValues() {
    this.form.get('projectTypeName').setValue(this.projectType.name);
    this.form.get('scoring').setValue(this.projectType.scoring);
    this.form.get('useSuccessProfile').setValue(this.projectType.useSuccessProfile);
    this.form.get('usePotentialFunctionality').setValue(this.projectType.usePotentialFunctionality);
    if (this.projectType.usePotentialFunctionality) {
      if (!this.projectType.useSuccessProfile) {
        this.checkForOnlyPotentialFunctionality();
      }
    }

    this.projectType.assessments.forEach(assessment => {
      const cognitiveAssessment = _.find(this.cognitiveAssessmentsFields, { 'value': assessment.assessmentType });
      if (cognitiveAssessment) {
        this.form.get(cognitiveAssessment.name).setValue(assessment.assessmentSetting);
        this.cognitiveAssessmentsChange(cognitiveAssessment.name, assessment.assessmentSetting);
      }
      const otherAssessment = _.find(this.otherAssessmentsFields, { 'value': assessment.assessmentType });
      if (otherAssessment) {
        this.form.get(otherAssessment.name).setValue(assessment.assessmentSetting);
        this.checkAssessmentsForUseSP();
      }
    });

    this.selectedUserGroups = this.projectType.userGroup.split(',');
    this.checkSaveButton();
  }

  checkAssessmentsForUseSP(toCheckForOnlyPotential?: boolean) {
    if (this.basedOnSPFields.length > 0) {
      this.form.get('useSuccessProfile').disable();
      this.form.get('useSuccessProfile').setValue(true);
    } else {
      this.form.get('useSuccessProfile').enable();
    }
    if (toCheckForOnlyPotential) {
      this.checkForOnlyPotentialFunctionality();
    }
  }

  setCognitiveAssessmentsValue(field, value, mainField) {
    switch (value) {
      case 'NotIncluded':
        this.form.get(field).enable();
        this.form.get(field).setValue('NotIncluded');
        break;
      case 'BasedOnSP':
        this.form.get(mainField).enable();
        if (mainField === 'abstractReasoning' || mainField === 'elementsLogical') {
          this.form.get(field).disable();
          this.form.get(field).setValue('NotIncluded');
        } else {
          this.form.get(field).setValue('BasedOnSP');
        }
        break;
      case 'Required':
      case 'Recommended':
      case 'Optional':
        this.form.get(field).disable();
        this.form.get(field).setValue('NotIncluded');
        break;
    }
  }

  cognitiveAssessmentsChange(fieldName, value, toCheckForOnlyPotential?) {
    switch (fieldName) {
      case 'elementsNumerical':
        this.setCognitiveAssessmentsValue('aspectsNumerical', value, 'elementsNumerical');
        break;
      case 'elementsVerbal':
        this.setCognitiveAssessmentsValue('aspectsVerbal', value, 'elementsVerbal');
        break;
      case 'elementsLogical':
        this.setCognitiveAssessmentsValue('abstractReasoning', value, 'elementsLogical');
        break;
      case 'aspectsNumerical':
        this.setCognitiveAssessmentsValue('elementsNumerical', value, 'aspectsNumerical');
        break;
      case 'aspectsVerbal':
        this.setCognitiveAssessmentsValue('elementsVerbal', value, 'aspectsVerbal');
        break;
      case 'abstractReasoning':
        this.setCognitiveAssessmentsValue('elementsLogical', value, 'abstractReasoning');
        break;
    }
    this.checkAssessmentsForUseSP(toCheckForOnlyPotential);
  }

  getAssessmentTypeId(assessmentValue) {
    const assessment = this.projectType ? _.find(this.projectType.assessments, {'assessmentType': assessmentValue}) : null;
    return assessment ? assessment.assessmentTypeId : undefined
  }

  checkSaveButton() {
    const validAssessment = this.getClassAssessment() === 'success';
    const validProjectType = this.getClassProjectTypeName() === 'success';
    const validSpOrPotential = this.getClassSuccessProfileOrPotential() === 'success';

    this.isSaveEnabled = validProjectType && validAssessment && validSpOrPotential && this.selectedUserGroups.length > 0;
  }

  updateFields() {
    this.requiredFields = [];
    this.recommendedFields = [];
    this.optionalFields = [];
    this.basedOnSPFields = [];
    this.assessmentsSelected = [];

    _.concat(CognitiveAssessmentsFields, OtherAssessmentsFields).forEach((assessment: AssessmentsObject) => {
      const assessmentValue = this.form.get(assessment.name).value;
      if (assessmentValue === 'Required') {
        this.requiredFields.push(assessment);
      } else if (assessmentValue === 'Recommended') {
        this.recommendedFields.push(assessment);
      } else if (assessmentValue === 'Optional') {
        this.optionalFields.push(assessment);
      } else if (assessmentValue === 'BasedOnSP') {
        this.basedOnSPFields.push(assessment);
      }

      if (assessmentValue !== 'NotIncluded') {
        this.assessmentsSelected.push({
          assessmentTypeId: this.getAssessmentTypeId(assessment.value),
          assessmentType: assessment.value,
          assessmentSetting: assessmentValue
        })
      }
    });
    this.checkSaveButton();
  }

  getPayload(reasonForChange?: string) {
    const formControl = this.form.controls;
    const payload = {
      KfasClientId: this.client.id,
      clientId: this.client.id,
      name: formControl.projectTypeName.value.trim(),
      assessments: this.assessmentsSelected,
      initiatedBy: {
        firstNameKey: this.authenticationService.currentUser.firstName,
        lastNameKey: this.authenticationService.currentUser.lastName,
        clientId: this.client.id
      },
      useSuccessProfile: !!formControl.useSuccessProfile.value,
      usePotentialFunctionality: !!formControl.usePotentialFunctionality.value,
      scoring: formControl.scoring.value,
      customProjectTypeId: this.projectTypeId ? Number(this.projectTypeId) : undefined,
      reasonForChange: reasonForChange || undefined,
      UserGroup: this.selectedUserGroups[0] === '' ? 'ALL' : this.selectedUserGroups.join(','),
    };
    return payload;
  }

  navigateBack() {
    this.spinnerService.activate();
    this.form.reset();
    const that = this;
    setTimeout(function() {
      that.router.navigate(['../clients/client-custom-project-types'], {
        queryParams: {
          clientId: that.client.id
        }
      });
      that.spinnerService.deactivate();
    }, 500);
  }

  save(reasonForChange?: string) {
    const payload = this.getPayload(reasonForChange);
    let apiSubscription = null;
    if (!this.projectTypeId) {
      apiSubscription = this.apiService.createCustomProjectType(payload);
    } else {
      apiSubscription = this.apiService.updateCustomProjectType(payload);
    }
    this.currentSubscriptions.push(
      apiSubscription.subscribe((resolved) => {
        this.alertService.success('Custom project type ' + (this.projectTypeId ? 'amended' : 'created'));
        if (!this.projectTypeId) {
          const dialogRef = this.dialog.open(CPTReportManagementConfirmationComponent, {
            width: '250px',
            data: {
              clientId: this.client.id
            }
          });
          dialogRef.afterClosed().subscribe((result) => {
            if (result === 'yes') {
              this.navigateToReportManagement(this.client.id.toString());
            } else {
              this.navigateBack();
            }
          });
        }
        else {
          this.navigateBack();
        }
        this.error = '';
      },
      (error) => {
        if (error && error.message) {
          const errorMessage = error.message;
          if (errorMessage.includes("The specified condition was not met for 'Name'")) {
            this.error = 'This Project Type Name already exists for this client.';
          } else {
            this.alertService.error(errorMessage);
          }
        } else {
          this.alertService.error('An error occurred while saving the data. Please try again later.');
        }
        })
    );
  }

  onSubmit() {
    if (this.isSaveEnabled) {
      if (!this.projectTypeId) {
        this.save();
      } else {
        this.dialogRef = this.dialog.open(CPTAmendmentReasonDialogComponent, {});
        this.dialogRef.afterClosed().subscribe(reasonForChange => {
          if (reasonForChange) {
            this.save(reasonForChange);
          }
        });
      }
    }
  }

getClassProjectTypeName() {
  const projectTypeNameControl = this.form.get('projectTypeName');
  const projectTypeNameValue = projectTypeNameControl.value;

  let isValidLength = false;
  let isDuplicateStandard = false;

  if (
    projectTypeNameValue &&
    projectTypeNameValue.trim().length >= 5 &&
    projectTypeNameValue.trim().length <= 50
  ) {
    isValidLength = true;
    isDuplicateStandard = this.isDuplicateStandardProjectType(projectTypeNameValue);
  }

  if (isValidLength && !isDuplicateStandard) {
    return 'success';
  } else if (!isValidLength) {
    return 'error-length';
  } else if (isDuplicateStandard) {
    return 'error-duplicate-standard';
  }
}

  getClassAssessment() {
    const assessments = Object.keys(this.form.controls)
      .filter(fieldName => fieldName !== 'projectTypeName')
      .map(fieldName => this.form.get(fieldName).value);

    const requiredCount = assessments.filter(assessment => assessment === 'Required').length;
    const othersCount = assessments.filter(assessment => ['Recommended', 'Optional', 'BasedOnSP'].indexOf(assessment) !== -1).length;

    if (requiredCount >= 1 || othersCount >= 2) {
      return 'success';
    }

    return 'error';
  }

  getClassSuccessProfileOrPotential() {
    const useSuccessProfile = this.form.get('useSuccessProfile').value;
    const usePotentialFunctionality = this.form.get('usePotentialFunctionality').value;
  
    if (useSuccessProfile || usePotentialFunctionality) {
      return 'success';
    }

    return 'error';
  }

  onCancel() {
    this.router.navigate(['../clients/client-custom-project-types'], {
      queryParams: {
        clientId: this.client.id
      }
    });
  }

  onToggleChange(event: MatSlideToggleChange, field: string) {
    const toggledOn = event.checked;
    const useSuccessProfile = this.form.get('useSuccessProfile').value;
    const usePotentialFunctionality = this.form.get('usePotentialFunctionality').value;

    if (!usePotentialFunctionality) {
      this.form.get('traits').enable();
      this.form.get('drivers').enable();
    }
    this.checkForOnlyPotentialFunctionality();

    if (!useSuccessProfile && !usePotentialFunctionality) {
      this.form.get('scoring').setValue('Not_Assigned');
      return;
    }
    if (field === 'useSuccessProfile' && useSuccessProfile) {
      this.form.get('scoring').setValue('Success_Profile');
      return;
    } else if (field === 'usePotentialFunctionality' && usePotentialFunctionality) {
      this.potentialFunctionalityOnChanges();
      return;
    } else if (useSuccessProfile) {
      this.form.get('scoring').setValue('Success_Profile');
    } else if (usePotentialFunctionality) {
      this.form.get('scoring').setValue('Levels');
    }
  }

  potentialFunctionalityOnChanges() {
    this.form.get('scoring').setValue('Levels');
    this.form.get('traits').setValue('Required');
    this.form.get('drivers').setValue('Required');
  }

  checkForOnlyPotentialFunctionality() {
    const onlyPotentialFunctionality = !this.form.get('useSuccessProfile').value && this.form.get('usePotentialFunctionality').value;

    _.concat(CognitiveAssessmentsFields, OtherAssessmentsFields).forEach((assessment: AssessmentsObject) => {
      if (!assessment.canBeOnlyPotential) {
        if (onlyPotentialFunctionality) {
          this.form.get(assessment.name).setValue('NotIncluded');
          this.form.get(assessment.name).disable();
        } else {
          this.form.get(assessment.name).enable();
        }
      } else if (assessment.name === 'abstractReasoning') {
        this.form.get(assessment.name).enable();
      }
    });

    if (!onlyPotentialFunctionality) {
      this.cognitiveAssessmentsFields.forEach((assessment: AssessmentsObject) => {
        const assessmentSetting = this.form.get(assessment.name).value
        if (assessmentSetting !== 'NotIncluded') {
          this.form.get(assessment.name).enable();
          this.cognitiveAssessmentsChange(assessment.name, assessmentSetting);
        }
        if (assessment.name === 'abstractReasoning' && this.form.get('elementsLogical').value === 'NotIncluded') {
          this.form.get(assessment.name).enable();
        }
      });

      if (this.form.get('usePotentialFunctionality').value) {
        this.potentialFunctionalityOnChanges();
      }
    }
  }

  openHistoryDialog() {
    this.dialog.open(CPTHistoryDialogComponent, {
      width: '800px',
      data: {
        projectTypeId: this.projectTypeId
      },
    });
  }

  isDuplicateStandardProjectType(projectTypeName: string): boolean {
    const prohibitedNames = [
      'Leadership',
      'Potential',
      'Professional Development',
      'Professional_Development',
      'ProfessionalDevelopment',
      'Learning Agility',
      'Learning_Agility',
      'LearningAgility',
      'Management',
      'Professional',
      'Graduate',
      'Entry'
    ];
  
    const normalizedProjectTypeName = projectTypeName.trim().replace(/[\s_]/g, '').toLowerCase();

    // Check if normalizedProjectTypeName matches any prohibited name
    const isDuplicate = prohibitedNames.some(name => {
    // Trimed and normalized the current prohibited name for comparison
    const normalizedProhibitedName = name.replace(/[\s_]/g, '').toLowerCase();

    return normalizedProjectTypeName === normalizedProhibitedName;
  });

  return isDuplicate;
  }

  navigateToReportManagement(clientId: string) {
    const url = `/clients/report-management?clientId=${clientId}`;
    this.router.navigateByUrl(url);
  }
  
  ngOnDestroy() {
    if (this.currentSubscriptions.length > 0) {
      this.currentSubscriptions.forEach(subscription => subscription.unsubscribe());
    }
  }
}
