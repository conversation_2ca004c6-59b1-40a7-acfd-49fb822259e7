@import "../../../../../styles/theme.scss";
@import "../../../../../styles/colors.scss";

:host {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  .details-page-content {
   padding: 10px 20px;
   margin: auto;
   width: 100%;
   margin-bottom: 20px;
  }

  .form {
    .button-group {
      display: flex;
      justify-content: flex-end;
      margin-top: 5px;
  
      button {
        margin-left: 10px;
      }
    }
    input[type="text"],
    select {
      @extend .glass;
      border: 1px solid $primary--blue;
      width: 100%;
      padding: 6px 12px;
    }

    input[type="checkbox"].checkbox-margin {
      margin: 12px 0;
    }

    input[type="text"],
    select,
    .form-control.full-width input[type="text"] {
      &:not(:focus) {
        &.ng-touched.ng-invalid {
          box-shadow: 0px 0px 3px 0px $secondary--red;
        }
      }
    }

    .form-control {
      margin: 6px 0;
      display: flex;
      justify-content: flex-start;
      align-items: center;

      .label {
        width: 150px;
        min-width: 150px;
      }

      .input {
        width: 250px;
        min-width: 200px;

        label {
          &:not(:focus) {
            &.ng-touched.ng-invalid {
              input {
                box-shadow: 0px 2px 4px 0px $secondary--red;
              }
            }
          }
        }
      }

      .validation {
        flex-grow: 1;
        color: $secondary--red;
        font-size: 12px;
        margin-left: 12px;
      }
    }

    .error-container {
      border-top: solid thin #e5e1e1;
      padding-top: 24px;
      margin-top: 24px;

      .message {
        @extend .glass;
        margin-top: 12px;
        padding: 12px;
        color: red;
      }
    }
  }

  .form-row {
    display: flex;
    margin-bottom: 10px;
    flex-wrap: nowrap;
  }

  .form-column,
  .third,
  .two-columns {
    flex-basis: 50%;
    padding-right: 30px;
    box-sizing: border-box;

    &.third {
      flex-basis: 30%;
      padding-right: 20px;
    }

    &.two-columns {
      flex-basis: 70%;
    }

    .form-control {
      &.two-columns {
        .label,
        .input {
          width: 100%;
        }
      }
    }
  }

  .form-control {
    margin-bottom: 10px;

    .label {
      margin-bottom: 5px;
    }

    .input {
      margin-bottom: 5px;
    }
  }

  .error-container {
    margin-top: 10px;
  }

  .heading {
    width: 100%;
    font-weight: bold;
    padding: 20px 0px;
  }

  .pb-0 {
    padding-bottom: 0px;
  }

  .form-control.full-width input[type="text"] {
    width: 600px; 
    min-width: 600px; 
    padding: 6px 12px;
  }
  div {

    &.error-text {
      color: $secondary--red;
      font-weight: 600;
    }

    &.error {
      color: $secondary--red;
      font-weight: 600;
      &::before {
        content: '✕';
        margin-right: 4px;
      }
    }
    &.default {
      &::before {
        content: '•';
        margin-right: 4px;
      }
    }
    &.success {
      color: $secondary--green;
      &::before {
        content: '✔';
        margin-right: 4px;
      }
    }
  }
  .history-icon {
    display: inline-flex;
    align-items: center;
    margin-right: 20px;
    button {
      mat-icon {
        font-size: 45px; 
      }
  }
}

.error-message {
  color: red;
  margin: 0;
  padding: 0; 
}

.custom-select {
  position: relative;
}

.selected-options {
  border: 1px solid $primary--blue;
  border-radius: 4px;
  padding: 5px 5px 5px 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.selected-options::after {
  content: "";
  border: solid;
  border-width: 0 2px 2px 0;
  display: inline-block;
  padding: 2.5px;
  transform: rotate(45deg);
  margin-left: 5px;
}

.dropdown-options {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: #fff;
  border: 1px solid $primary--blue;
  border-radius: 4px;
  max-height: 100px;
  overflow-y: auto;
  padding: 5px;
  z-index: 999;
}

.dropdown-options label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 5px;
  padding: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}


.dropdown-options label span {
  font-weight: normal;
}

.dropdown-options input[type="checkbox"] {
  margin-left: auto;
  margin-right: 5px;
}
.reports-link {
  text-decoration: underline;
}
  
}
