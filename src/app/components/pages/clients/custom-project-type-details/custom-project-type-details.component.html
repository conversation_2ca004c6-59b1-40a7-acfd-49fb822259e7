<div class="details-page-content white-glass form">
  <div class="form-row" *ngIf="this.projectTypeId">
    <div  class="label heading">Please note that changes made here will NOT affect projects that have already been created using this Custom Project Type.</div>
    <div class="history-icon">
      <button mat-icon-button (click)="openHistoryDialog()">
        <mat-icon>access_time</mat-icon>
      </button>
    </div>
  </div>

  <form [formGroup]="form" (ngSubmit)="onSubmit()">
    <div class="form-row">
      <div class="form-control full-width">
        <div class="label">Project Type Name</div>
        <div class="input">
          <input
            type="text"
            formControlName="projectTypeName"
          />
        </div>
      </div>
    </div>

    <div class="form-row">
      <div class="form-column two-columns">
        <div class="form-row">
          <div class="form-column">
            <div class="label heading">Cognitive Ability Assessments</div>
            <div class="form-control" *ngFor="let field of cognitiveAssessmentsFields">
              <div class="label">{{ field.label }}</div>
              <div class="input">
                <select
                  formControlName="{{ field.name }}"
                  class="custom-select"
                  (change)="cognitiveAssessmentsChange(field.name, $event.target.value, true)"
                >
                  <option *ngFor="let option of assessmentOptions" [value]="option.value">{{ option.label }}</option>
                </select>
              </div>
            </div>
          </div>

          <div class="form-column">
            <div class="label heading">Other Assessments</div>
            <div class="form-control" *ngFor="let field of otherAssessmentsFields">
              <div class="label">{{ field.label }}</div>
              <div class="input">
                <select
                  formControlName="{{ field.name }}"
                  class="custom-select"
                  (change)="checkAssessmentsForUseSP(true)"
                >
                  <option *ngFor="let option of assessmentOptions" [value]="option.value">{{ option.label }}</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <div class="form-row">
          <div class="form-column">
            <div class="form-control two-columns">
              <div class="label">Use Success Profile</div>
              <div class="input">
                <mat-slide-toggle
                  formControlName="useSuccessProfile"
                  color="primary"
                  (change)="onToggleChange($event, 'useSuccessProfile')"
                ></mat-slide-toggle>
              </div>
            </div>

            <div class="form-control two-columns">
              <div class="label">Include Potential Functionality</div>
              <div class="input">
                <mat-slide-toggle
                  formControlName="usePotentialFunctionality"
                  color="primary"
                  (change)="onToggleChange($event, 'usePotentialFunctionality')"
                ></mat-slide-toggle>
              </div>
            </div>

            <div class="form-control two-columns">
              <div class="label">Scoring to use for mixed reports</div>
              <div class="input">
                <select
                  formControlName="scoring"
                  class="custom-select"
                >
                  <option value="Not_Assigned">N/A</option>
                  <option value="Success_Profile" [disabled]="!form.get('useSuccessProfile').value">Success Profile</option>
                  <option value="Levels">Potential Levels</option>
                </select>
              </div>
            </div>

          <div class="form-control two-columns">
            <div class="label">User Groups</div>
            <div class="input">
              <div class="custom-select" (click)="toggleDropdown()">
                <div class="selected-options">
                  {{ getUserGroupLabel() }}
                  <span class="arrow"></span>
                </div>
                <div class="dropdown-options" *ngIf="isDropdownOpen">
                  <label (click)="$event.stopPropagation()">
                    <span>ALL</span>
                    <input
                      type="checkbox"
                      value="ALL"
                      [checked]="isAllUserGroupSelected()"
                      [disabled]="isAllUserGroupSelected()"
                      (change)="selectAllUserGroups($event.target.checked)"
                    >
                  </label>
                  <label *ngFor="let option of userGroupOptions" (click)="$event.stopPropagation()">
                    <span>{{ option.label }}</span>
                    <input
                      type="checkbox"
                      [value]="option.value"
                      [checked]="selectedUserGroups.includes(option.value)"
                      (change)="updateSelectedUserGroups(option.value, $event.target.checked)"
                    >
                  </label>
                </div>
              </div>
            </div>
          </div>
          </div>
        </div>

        <div class="form-row">
          <div class="form-column two-columns">
            <div class="validation-group">
              <div class="note">NOTE:</div>
              <div [class]="getClassProjectTypeName() === 'error-length' ? 'error' : 'success'">Project Type Name must be at least 5 characters & max 50 characters long (multiple spaces in between the characters will be considered as single space).</div>
              <div *ngIf="getClassProjectTypeName() === 'error-duplicate-standard'"
              [class]="'error'">The Project Type Name is a duplicate of a standard Korn Ferry project type.</div>
              <div [class]="getClassAssessment()">At least 1 assessment should be set to Required or at least 2 assessments should be set to Recommended, Optional, or Based on SP.</div>
              <div [class]="getClassSuccessProfileOrPotential()">Please enable at least one of the Use Success Profile or Include Potential Functionality options.</div>
            </div>
          </div>
        </div>
      </div>

      <div class="form-column third">
        <div class="form-row label heading pb-0">Required</div>
        <div class="form-row" *ngFor="let requiredAssessment  of requiredFields">
          <div class="form-column two-columns">
            <div class="label">
              {{ requiredAssessment.label }}
            </div>
          </div>
          <div class="form-column third">
            <div class="label">
              {{ requiredAssessment.timeDuration }} mins
            </div>
          </div>
        </div>

        <div class="form-row label heading pb-0">Recommended</div>
        <div class="form-row" *ngFor="let recommendedAssessment  of recommendedFields">
          <div class="form-column two-columns">
            <div class="label">
              {{ recommendedAssessment.label }}
            </div>
          </div>
          <div class="form-column third">
            <div class="label">
              {{ recommendedAssessment.timeDuration }} mins
            </div>
          </div>
        </div>

        <div class="form-row label heading pb-0">Optional</div>
        <div class="form-row" *ngFor="let optionalAssessment  of optionalFields">
          <div class="form-column two-columns">
            <div class="label">
              {{ optionalAssessment.label }}
            </div>
          </div>
          <div class="form-column third">
            <div class="label">
              {{ optionalAssessment.timeDuration }} mins
            </div>
          </div>
        </div>

        <div class="form-row label heading pb-0">Based on SP</div>
        <div class="form-row" *ngFor="let basedOnSPAssessment  of basedOnSPFields">
          <div class="form-column two-columns">
            <div class="label">
              {{ basedOnSPAssessment.label }}
            </div>
          </div>
          <div class="form-column third">
            <div class="label">
              {{ basedOnSPAssessment.timeDuration }} mins
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="form-row">
      <div class="form-column two-columns">
        <div class="label heading" *ngIf="!this.projectTypeId">
          Remember to define report filters for this project type before use.
        </div>
        <div class="label heading" class="error-text" *ngIf="this.projectTypeId && !this.projectType.isFilterCreated">
          <a (click)="navigateToReportManagement(client.id)" class="reports-link">Report Filters</a> have not yet been defined for this project type.
        </div>
      </div>

      <div class="form-column">
        <div class="button-group">
          <button type="button" class="btn btn-secondary" (click)="onCancel()">Cancel</button>
          <button type="submit" class="btn btn-primary" [disabled]="!isSaveEnabled">Save</button>
        </div>
    </div>
  </div>
  <div class="form-row " *ngIf="error">
    <div class="form-column two-columns">
      <div class="error-message">
        {{ error }}
      </div>
    </div>
  </div>
  </form>

</div>
