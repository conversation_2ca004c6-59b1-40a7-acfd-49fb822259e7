export interface ConfigurationOption {
  value: string;
  viewValue: string;
}

export interface ConfigurationNumericOption {
  value: number;
  viewValue: string;
}

/**
 * Report filters and the corresponding settings options available her filter type.
 */
export interface ReportFilterSettings {
  filterType: ConfigurationOption;
  filterPolicies: ConfigurationOption[];
  filterValues: ConfigurationOption[];
}

/**
 * Request object for report filter override request.
 */
export class FilterOverrideRequest {
  kfasClientId: number;
  blendedReportId: number;

  constructor(kfasClientId: number, blendedReportId: number) {
    this.kfasClientId = kfasClientId;
    this.blendedReportId = blendedReportId;
  }
}

/**
 * Options that are available to add a new filters.
 * TODO: Get the configuration from api call.
 */
export class ReportFilterOptions {
  /**
   * Report filter types. with equivalent enum number.
   */
  private filterTypes: ConfigurationOption[] = [
    { value: '0', viewValue: 'Assessment' },
    { value: '1', viewValue: 'SuccessProfile' },
    { value: '2', viewValue: 'ProjectType' },
    { value: '3', viewValue: 'NormType' }
  ];

  /**
   * Report filter policies.( should have / should not have) with equivalent enum number.
   */
  filterPolicy: ConfigurationOption[] = [
    { value: '0', viewValue: 'ShouldHave' },
    { value: '1', viewValue: 'ShouldNotHave' }
  ];

  /**
   * Blended projects with equivalent enum number.
   */
  private projects: ConfigurationOption[] = [
    { value: '1', viewValue: 'Entry' },
    { value: '2', viewValue: 'Graduate' },
    { value: '3', viewValue: 'Professional' },
    { value: '4', viewValue: 'Managerial' },
    { value: '5', viewValue: 'Leadership' },
    { value: '6', viewValue: 'LeadershipDevelopment' },
    { value: '7', viewValue: 'LeadershipSelection' },
    { value: '8', viewValue: 'ProfessionalDevelopment' },
    { value: '9', viewValue: 'Potential' },
    { value: '10', viewValue: 'SJT' },
    { value: '11', viewValue: 'LearningAgility'}
  ];

  /**
   * Assessment types with equivalent enum number.
   */
  private assessmentTypes: ConfigurationOption[] = [
    { value: '15', viewValue: 'AspectsNumerical' },
    { value: '2', viewValue: 'ElementsNumerical' },
    { value: '16', viewValue: 'AspectsVerbal' },
    { value: '5', viewValue: 'ElementsVerbal' },
    { value: '10', viewValue: 'ElementsLogical' },
    { value: '17', viewValue: 'AspectsChecking' },
    { value: '3', viewValue: 'Dimensions' },
    { value: '23', viewValue: 'Kf4dBehaviours' },
    { value: '24', viewValue: 'Kf4dDrivers' },
    { value: '25', viewValue: 'Kf4dTraits' },
    { value: '26', viewValue: 'Experiences' },
    { value: '27', viewValue: 'Preferences' },
    { value: '20', viewValue: 'PlayerTest' }
  ];

    /**
   * Norm types with equivalent enum number.
   */
     private normTypes: ConfigurationOption[] = [
      { value: '1', viewValue: 'LevelNorms' },
      { value: '2', viewValue: 'SuccessProfileNorms' },
      { value: '3', viewValue: 'Assessment' }
    ];

  /**
   * Report filter types with the settings values.
   */
  allFilters: ReportFilterSettings[] = [
    {
      filterType: { value: '0', viewValue: 'Assessment' },
      filterPolicies: this.filterPolicy,
      filterValues: this.assessmentTypes
    },
    {
      filterType: { value: '1', viewValue: 'SuccessProfile' },
      filterPolicies: this.filterPolicy,
      filterValues: []
    },
    {
      filterType: { value: '2', viewValue: 'ProjectType' },
      filterPolicies: this.filterPolicy,
      filterValues: this.projects
    },
    {
      filterType: { value: '3', viewValue: 'NormType' },
      filterPolicies: this.filterPolicy,
      filterValues: this.normTypes
    }
  ];
}
