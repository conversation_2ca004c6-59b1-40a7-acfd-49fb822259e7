export class Client {
  dateCreated?: string;
  id: number;
  name: string;
}

export class ClientDetails extends Client {
  clients: Client[];
  dataProtectionAutoAcknowledge: boolean;
  dateCreated: string;
  defaultSsoSetting?: SsoSetting;
  defaultReminderTemplateId?: number;
  defaultReportReleaseNotificationId?: number;
  defaultSsoInvitationTemplateId?: number;
  defaultSsoReminderTemplateId?: number;
  defaultTemplateId?: number;
  externalRef?: string;
  hideBioDataPage: false;
  useCustomReflexDomains: false;
  id: number;
  name: string;
  isProctoringEnabled?: boolean;
  defaultProctoringConfigurationId?: number;
  proctoringConfigurations?: ProctoringConfiguration[];
  isTestProctoringEnabled?: boolean;
}

// To sync with backend ENUM values {NONE, OPTIONAL, EMAIL_BASED, SSO_ONLY} added 0, 2, 3
export enum SsoSetting {
  NONE = 0,
  EMAIL_BASED = 2,
  SSO_ONLY = 3,
}

export enum  ClientTypes {
  KFAS = "KF ASSESS AND SELECT",
  SPS = "SUCCESS PROFILE SURVEY"
}

export enum ProctoringStake {
  LOW = "Low Stake",
  MEDIUM = "Medium Stake",
  HIGH = "High Stake",
}

export class ProctoringConfiguration {
  proctoringConfigurationId: number;
  priorityOrder: number;
  name: string;
}

export enum AssessmentStatusEnum {
  ABOVE_AVERAGE = 'ABOVE_AVERAGE',
  BELOW_AVERAGE = 'BELOW_AVERAGE',
  COMPLETED = 'COMPLETED',
  LOCKED = 'LOCKED',
  IN_PROGRESS = 'IN_PROGRESS',
  NOT_STARTED = 'NOT_STARTED',
  TERMINATED = 'TERMINATED',
}