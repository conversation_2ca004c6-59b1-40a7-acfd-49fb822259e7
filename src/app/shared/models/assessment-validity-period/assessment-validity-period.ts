import { AssessmentType } from "@/shared/pipes";

/**
 * Assessment validity period type
 */
export enum AssessmentValidityPeriodType {
  Day = 1,
  Week = 2,
  Month = 3,
  Year = 4
}

/**
 * Class for assessment validity period
 */
export class AssessmentValidityPeriod {
  clientAssessmentValidityPeriodId: number;
  clientId: number;
  assessmentType: AssessmentType;
  assessmentTypeAsText: string;
  periodType: AssessmentValidityPeriodType;
  periodTypeAsText: string;
  periodDuration: number;
}

/**
 * Model for getting assessment validity periods for client
 */
export class ClientAssessmentValidityPeriodsModel {
  periods: AssessmentValidityPeriod[];
  clientId: number;
  periodsInhertitedFromParent: boolean;
}

export class AssessmentValidityPeriodData {
  selectedPeriod: AssessmentValidityPeriod;
  type: string;
  assessmentTypes: AssessmentType[];
}

export class AssessmentValidityPeriodDataWrapper {
  data: AssessmentValidityPeriodData;
}
