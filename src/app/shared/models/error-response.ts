/**
 * Represents the details of a response that returned a non-successful HTTP code.
 */
export class ResponseError {
    /**
     * The HTTP status code of the response.
     */
    readonly status: number;

    /**
     * The message contained in the response.
     */
    readonly message: string;

    /**
     *Creates an instance of ResponseError.
     * @param status , status code.
     * @param message, error message.
     */
    constructor(status: number, message: any) {
        this.status = status;
        if (message instanceof Object) {
          this.message = message.message;
        } else {
        this.message = message;
        }
    }
}

