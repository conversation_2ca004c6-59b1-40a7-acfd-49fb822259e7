export const templateTypes = {
  INVITATION: "INVITATIONS",
  REMINDER: "Reminders",
  INVITATION_SSO: "SSO Invitations",
  REPORT_RELEASE: "Report Release Notifications",
  REMINDER_SSO: "SSO Reminder",
};

export type TemplateTypes = keyof typeof templateTypes;

/* export enum ScheduleTypes {
    MANUALLY = 'MANUALLY',
    PARTICIPANT_ADDED = 'PARTICIPANT_ADDED', // not available for reminder
    BY_DATE = 'BY_DATE',
    RECURRING_SCHEDULE = 'RECURRING_SCHEDULE',
}*/

export type ScheduleTypes =
  | "MANUALLY"
  | "PARTICIPANT_ADDED"
  | "BY_DATE"
  | "RECURRING_SCHEDULE";

/* export enum ParticipantProgress {
    NOT_INVITED = 'NOT_INVITED',
    NOT_STARTED = 'NOT_STARTED',
    IN_PROGRESS = 'IN_PROGRESS',
    COMPLETED = 'COMPLETED',
}*/

export type ParticipantProgress =
  | "NOT_INVITED"
  | "NOT_STARTED"
  | "IN_PROGRESS"
  | "COMPLETED";

/* export enum RecurringScheduleRepeats {
    WEEKDAY = 'WEEKDAY',
    WEEK = 'WEEK',
    MONTH = 'MONTH',
}*/

export type RecurringScheduleRepeats = "WEEKDAY" | "WEEK" | "MONTH";

/* export enum ProjectRecurringEndTypes {
    PROJECT_END_DATE = 'PROJECT_END_DATE',
    ON_DATE = 'ON_DATE',
    AFTER_OCCURRENCE = 'AFTER_OCCURRENCE',
}*/

export type ProjectRecurringEndTypes =
  | "PROJECT_END_DATE"
  | "ON_DATE"
  | "AFTER_OCCURENCE";

/* export enum ProjectRecurringScheduleDayOfMonthType {
    NUMBER = 'NUMBER',
    WEEK = 'WEEK',
}*/

export type ProjectParticipantSend =
  | "ALL_PARTICIPANTS"
  | "PARTICIPANTS_BY_PROGRESS"; // 'SELECTED_PARTICIPANTS'

export type ProjectRecurringScheduleDayOfMonthType = "DAY_OF_MONTH" | "WEEK_DAY_SELECTION";

export type DayOfMonth = number | "LASTDAY";

export type WeekDayOptions = number | "LAST";

export type WeekDays =
  | "MONDAY"
  | "TUESDAY"
  | "WEDNESDAY"
  | "THURSDAY"
  | "FRIDAY"
  | "SATURDAY"
  | "SUNDAY";

export interface ProjectRecurringEndDate {
  endType: ProjectRecurringEndTypes;
  endDate: ProjectDateTimeWithTimezone;
  afterOccurrenceCount: number;
}

export interface ProjectDayOfMonth {
  type: ProjectRecurringScheduleDayOfMonthType;
  /** required if dayOfMonthType is NUMBER */
  dayOfMonth: DayOfMonth;
  dayInWeek: string;
  /** required if dayOfMonthType is WEEK */
  weekDayNumber: WeekDayOptions;
  weekDaySelection: string;
}

export interface ProjectByDateSendDates {
  /**
   * identity of the send date entity 
   */
  id: number;
  /** UTC datetime and user timezone
   */
  dueDate: ProjectDateTimeWithTimezone;
}
export interface ProjectDateTimeWithTimezone {
  /** UTC datetime */
  dateTime: Date;
  /** timezone for UI purposes */
  timeZone: number;
}

export interface ProjectRecurringConfig {
  recurrenceInterval : {
    /** recurring shedule start date
     * required if schedule type is 'RECURRING_SCHEDULE'
     */
    sendDate: ProjectDateTimeWithTimezone;
    /** WEEKDAY' | 'WEEK' | 'MONTH'
     * required if schedule type is 'RECURRING_SCHEDULE'
     */
    scheduleRepeats: RecurringScheduleRepeats;
    /** recurring schedule end date
     * required if schedule type is 'RECURRING_SCHEDULE'
     */
    recurringEnd: ProjectRecurringEndDate;
    /** required if scheduleRepeats is 'WEEK'
     */
    recurringWeekDayConfig: ProjectRecurringWeekDayConfig;
    /** day of the month
     * required if scheduleRepeats is 'MONTH'
     */
    recurringMonth: ProjectDayOfMonth;
  }
}

export interface ProjectRecurringWeekDayConfig {
  businessDays: WeekDays[];
}

export type ProjectEmailSchedule =
  | ProjectEmailScheduleBase
  | ProjectEmailScheduleByDate
  | ProjectEmailScheduleRecurring;

export interface ProjectEmailScheduleBase {
  /** empty for new schedules */
  projectId?: number;
  /** empty for new schedules */
  emailSettingId?: number;
  /** email schedule name */
  settingName: string;
  /** 'INVITATION' | 'REMINDER' | 'INVITATION_SSO' | 'REPORT_RELEASE' | 'REMINDER_SSO' */
  templateType: TemplateTypes;
  /** email template Id */
  templateId: number;
  /** emaile template SSO Id */
  templateSsoId:number;
  /**  'MANUALLY' | 'PARTICIPANT_ADDED' | 'BY_DATE' | 'RECURRING_SCHEDULE' */
  settingType: ScheduleTypes;
  /**  'NONE' | TODO: print all possible values */
  emailLanguageType: string;

  participantSendConfig: ParticipantSendConfig;
}

export interface ParticipantSendConfig {
  participantSendType: ProjectParticipantSend;
  participantProgressTypes: ParticipantProgress[];
  selectedParticipantsConfig?: {
    // Anton G
    selectedParticipants: { participantId: number }[];
  };
}

export interface ProjectEmailScheduleByDate extends ProjectEmailScheduleBase {
  /** send dates
   * required if schedule type is 'BY_DATE'
   */
  byDateConfig: {
    sendDates: ProjectByDateSendDates[];
  };
}

export interface ProjectEmailScheduleRecurring
  extends ProjectEmailScheduleBase {
  byRecurringConfig: ProjectRecurringConfig;
}
