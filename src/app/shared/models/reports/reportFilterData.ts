import { ReportFilter } from '../client-reports/client-report-configuration';
import { ReportFilterOptions } from '../report-filter-options';

export class ReportFilterData {
    selectedFilter: ReportFilter;
    reportName?: string;
    options: ReportFilterOptions;
    type: string;
    selectedReportName?: string;
    valueSelectDropdown?: boolean;
    clientId?: number;
  }

export class ReportFilterDataWrapper {
    data: ReportFilterData;
}
