export class PortalBrandingSettings {
  name: string;
  kfasClientId: number;
  interfaceBrandingId: number;
  settings: PortalBrandingSettingsData;
  isNewBranding: boolean;
  authKfasClientId: number;
  clientId: number;
  userGroup: string;
  locale: string;
}

export class PortalBrandingSettingsData {
  general: {
    logoImageUrl: string;
    headerBarColour: string;
    headerBarColourLeft: string;
    headerBarColourRight: string;
    headerBarUseGradient: boolean;
    headerBrandBarColour: string;
    headerBrandBarColourLeft: string;
    headerBrandBarColourRight: string;
    headerBrandBarUseGradient: boolean;
    footerBrandBarColour: string;
    buttonBackgroundColour: string;
    buttonHoverColour: string;
    buttonTextColour: string;
  };
  signIn: {
    desktopImageUrl: string;
    mobileImageUrl: string;
    customManifest: string;
  };
  dashboard: {
    heroImageUrl: string;
    hamburgerTextColour: string;
    progressBarColour: string;
    customManifest: string;
    assessments: PortalBrandingAssessmentIconSetting[];
  };
  assessmentManifests: PortalBrandingAssessmentManifestSetting[];
}

class PortalBrandingAssessmentIconSetting {
  type: number;
  name: string;
  url: string;
}

class PortalBrandingAssessmentManifestSetting {
  type: number;
  name: string;
  manifest: string;
}
