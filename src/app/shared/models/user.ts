﻿import { AuthRole } from "./authRoles";

/**
 * Site user model
 */
export class User {
    id: number;
    username: string;
    password: string;
    firstName: string;
    lastName: string;
    email: string;
    locale: string;
    token: string;
    access_token: string;
    expires: number;
    roles: AuthRole[]
}

/**
 * User registration model.
 */
export class UserRegisterRequest {
  username: string;
  password: string;
  firstName: string;
  lastName: string;
  email: string;
  captcha: string;
}

/**
 * User password update request object.
 */
export class UpdatePasswordRequest {

  userName: string;
  password: string;
  confirmationCode: string;
}

/**
 * Object with user's roles and policies
 */
export class UserPermissions {
  roles: Array<AuthRole>;
  policies: Array<string>;
}
