/**
 * Translations and display order for AWS Cognito roles
 */
export const AuthRoles = {
  admin: { name: "Admin", order: 0 },
  productDelivery: { name: "Product Delivery", order: 1 },
  projectParticipantManagement: {name: "Project & Participant Access", order: 2 },
  reportManagement: { name: "Report Management", order: 3 },
  participantExtract: { name: "Participant Extract", order: 4 },
  dataScientist: { name: "Data scientist", order: 5 },
  usageReporting: { name: "Usage Reporting", order: 6 },
  systemAdmin :{ name: "System Admin", order: 7 },
  projectAdmin :{ name: "Project Admin", order: 8}
};

/**
 * User roles (user groups) available for the site.
 */
export type AuthRole = keyof typeof AuthRoles;

/**
 * Full list of AuthRoles
 */
export const AuthRolesList: AuthRole[] = Object.keys(AuthRoles) as AuthRole[];

/**
 * Aliases for roles came from AWS Cognito
 */
export const AuthRolesAliases: { [key: string]: AuthRole } = {
  admin: "projectParticipantManagement",
  productOwner: "reportManagement",
  superAdmin: "admin",
};

/**
 * Returns an alias for AWS Cognito role (if defined)
 * @param awsCognitoRole AWS Cognito role
 * @returns an alias if exists, or the default role name
 */
export const getAliasForAuthRole = (awsCognitoRole: string): AuthRole => {
  return AuthRolesAliases[awsCognitoRole] || awsCognitoRole as AuthRole;
}

/**
 * Returns display order of the auth role (if defined)
 * @param authRole auth role
 * @returns display order if exists, or 100 by default
 */
export const getDisplayOrderForAuthRole = (authRole: AuthRole): number => {
  return AuthRoles[authRole] ? AuthRoles[authRole].order : 100;
}

/**
 * Returns display name of the auth role (if defined)
 * @param authRole auth Role
 * @returns display name if exists, or returns authRole back by default
 */
export const getDisplayNameForAuthRole = (authRole: AuthRole): string => {
  return AuthRoles[authRole] ? AuthRoles[authRole].name : authRole;
}
