export class StakeholderDetails{
    data: {
        stakeholderDetailsResponse: {
            stakeholderName: string,
            emailId: string,
            clientId: string,
            participantId: number,
            collabDetails: [
                {
                    collabId: number,
                    spId: string,
                    originalSpId: any,
                    spTitle: any,
                    createdDate: string,
                    clentId: string,
                    projectId: string,
                    dueDate: string,
                    sourceSysId: number,
                    sourceSystemName: string,
                    participants: [
                        {
                            id: number,
                            firstName: string,
                            lastName: string,
                            userName: string,
                            email: string,
                            permissions: [
                                {
                                    permissionId: number,
                                    permissionLevel: string,
                                    selected: boolean,
                                    disabled: boolean,
                                    prptId: number
                                }
                            ],
                            outputAccess: string,
                            roleId: number,
                            roleName: string,
                            surveyInvite: boolean,
                            surveyReminder: boolean,
                            outputInvite: boolean,
                            outputReminder: boolean,
                            outputEditInvite: boolean,
                            outputEditReminder: boolean,
                            internalUser: boolean,
                            userType: string,
                            surveydashboardResponse: [
                                {
                                    sortType: string,
                                    stage: string,
                                    completedDate: string,
                                    sortOrder: number
                                }
                            ]
                        }
                    ]
                }
                    ]
        totalRecords: number,
        pageIndex: number,
        pageSize: number
    }
}
}
