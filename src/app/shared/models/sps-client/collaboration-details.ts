export class CollaborationDetails{
    data: {
        collabDetails: [
            {
                collabId: number,
                spId: number,
                originalSpId: any,
                spTitle:string,
                createdDate:string,
                clentId: string,
                projectId: number,
                dueDate: string,
                sourceSysId: 3,
                sourceSystemName: string,
                participants: [
                    {
                        id: 10806,
                        email: [],
                        outputAccess: string,
                        surveyInvite: boolean,
                        surveyReminder: boolean,
                        outputInvite: boolean,
                        outputReminder: boolean,
                        outputEditInvite: boolean,
                        outputEditReminder: boolean,
                        internalUser: boolean,
                        userType: string
                    }
                ]
            }
        ],
        collabOwnerEmailId: string,
        collabOwnerName: string,
        Level: string,
        SubLevel: string,
        Function: string,
        SubFunction: string,
        GlobalNorm: boolean,
        LocationSpecificNorm: boolean,
        NormCountryID: number,
        Normid: number,
        isPrepopulatedJAScore: boolean
    }
}