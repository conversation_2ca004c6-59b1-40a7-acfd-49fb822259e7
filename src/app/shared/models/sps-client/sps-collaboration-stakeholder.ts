export class SpsCollaborationStakeholder {
    responseCode: string;
    responseMessage: string;
    data: {
        collabStakeholderResponselist: {
            collabId: number;
            clientId: string;
            spId: any;
            spTitle: string;
            createdDate: any;
            participants: [{
                id: number;
                firstName: string;
                lastName: string;
                userName: string;
                email: string;
                surveyStatus: string;
                completionDate: string;
                permissions: [
                    {
                    permissionId: number;
                    permissionLevel: string;
                    selected: boolean;
                    disabled: boolean;
                    prptId: number;
                }
            ];
                outputAccess: string;
                outputStatus: string;
                roleId: number;
                roleName: string;
                menus: [
                    {
                    name: string;
                    displayName: string;
                }
            ];
                surveyInvite: boolean;
                surveyReminder: boolean;
                outputInvite: boolean;
                outputReminder: boolean;
                outputEditInvite: boolean;
                outputEditReminder: boolean;
                internalUser: boolean;
                userType: string;
                emailData: [
                    { 
                    templateType: string; 
                    templateId: number; 
                }
            ];
                surveydashboardResponse: [
                    {
                        sortType: string;
                        stage: string;
                        completedDate: string;
                        sortOrder: number;
                    }
                ];
            }];
        };
        totalRecords: number;
        pageIndex: number;
        pageSize: number;
    };
}

export class SpsCollaborationStakeholderResponse {
    name: string;
    email: string;
    access: string;
    surveyIcon: HTMLElement;
    surveyStatus: number;
    outputStatus: string;
}