export class StakeholderLog {
    stakeholderName:string;
    email:string;
    collabName:string;
    collabId:any;
  }

export class StakeholderDetails {
    collabId:any;
    spTitle:string;
    dueDate: any;
    sourceSystemName: any;
    access: any;
    survey: any;
}

export class CollaborationStakeholdersList {
    name: string;
    email: string;
    access: string;
    surveyStatus: string;
    outputStatus: string;
    survey:string
}

export enum  SurveyStatuses {
  NOT_INVITED = "Not Invited",
  IN_PROGRESS = "In Progress",
  COMPLETED = "Complete",
  NO_ACCESS = "No Access"
}