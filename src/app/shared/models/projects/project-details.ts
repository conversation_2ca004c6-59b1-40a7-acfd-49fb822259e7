import * as _ from 'lodash';
import { AssessmentType } from '@/shared/pipes';

export class NotificationHeaderInfo {
  id: number;
  name: string;
  languages: string[];
}

export class SuccessProfile {
  projectId: number;
  id: number;
  name: string;
  successProfileCustomized: boolean;
  assessments: string;
  driversFullNames: any;
  traitsFullNames: any;
}

export class ProjectDetails {
  name: string;
  projectId: number;
  isChildProject: boolean;
  userGroup: string;
  clientId: number;
  projectType: string;
  owner: {
    id: string;
    firstNameKey: string;
    lastNameKey: string;
    clientId: string;
  };
  ownerDisplayName: string;
  norm: RegionalNorm;
  country: string;
  location: ProjectLocation;
  locale: string;
  successProfileId: number;
  ssoSetting: string;
  platform: string;

  assessments: ProjectAssessments;

  successProfiles: SuccessProfile[];

  defaultNotificationTemplateHeader: NotificationHeaderInfo;
  reminderNotificationTemplateHeader: NotificationHeaderInfo;
  invitationSsoTemplateHeader: NotificationHeaderInfo;

  scoreDisplay: string;
  createdDateTime: number;
  endDateTime: number;
  includeFitScore: boolean;
  includeDerailers: boolean;
  candidatesAccessScores: boolean;
  completionNotifications: boolean;
  overrideDataProtectionNotice: boolean;
  allowAssessmentReuse: boolean;
  currentLevel: string;
  targetLevel: string;
  productType: any;
  candidatesAccessLearningContent: boolean;
  includeLearningAgility: boolean;
  isKFAssess2: boolean;
}

export class RegionalNorm {
  normCountry: string;
  normVersion: string;
  normLabel: string;
  normId: number;
}

export class ProjectLocation {
  countryId: number;
  isNormCountryId: boolean;
}

export class ProjectAssessmentCustomNorm {
  assessmentType: number;
  normNo: number;
}

export class GetNormResponse {
  data: {
    norms: NormModel[];
    locations: LocationModel[];
  };
}

export class NormModel {
  normCountry: string;
  normVersion: string;
  normLabel: string;
  normId: number;
}

export class LocationModel {
  countryId: number;
  countryName: string;
  isNormCountry: boolean;
}

export class ProjectAssessments {
  abstract?: Ability;
  behavioural?: any;
  checking?: Ability;
  drivers?: any;
  experiences?: any;
  inclusiveLeaderSjt?: any;
  logical?: Ability;
  numerical?: Ability;
  preferences?: any;
  sjt?: any;
  technicalSkillsInventory?: any;
  traits?: any;
  verbal?: Ability;
}

export class ProjectAssessment {
  assessmentId: number;
  measure: boolean;
}

export class Ability extends ProjectAssessment {
  customNormNo?: number;
  target: number;
}

export class ProjectUpdateRequestModel {
  projectId: number;
  newNorm?: RegionalNorm;
  newLocation?: ProjectLocation;
  newAssessmentCustomNorm?: ProjectAssessmentCustomNorm;

  constructor(projectId: number, newNorm?: RegionalNorm, newLocation?: ProjectLocation, newAssessmentCustomNorm?: ProjectAssessmentCustomNorm) {
    this.projectId = projectId;
    this.newNorm = newNorm;
    this.newLocation = newLocation;
    this.newAssessmentCustomNorm = newAssessmentCustomNorm;
  }
}
