export class ParticipantInfo {
  createdBy: {
    id: string;
    firstNameKey: string;
    lastNameKey: string;
  };
  status: string;
  completed: Date;
  lastActivity: number;
  results: {
    ability: {
      numerical: {
        status: string;
        score: number;
      };
      verbal: {
        status: string;
        score: number;
      };
      status: string;
      score: number;
    };
    behavioural: {
      status: string;
      score: number;
    };
    completionPercentage: number;
  };
  reports: {
    type: string;
    name: string;
  }[];
  participantId: number;
  displayName: string;
  forename: string;
  surname: string;
  email: string;
  locale: string;
  hired: string;
  displayStatus: string;
  iC2EmployeeId: number;
}

export class SearchProjectParticipantsDataConfig {
  scoreDisplay: string;
  sjtCompetencyNames?: any[];
  targets: {
    ability: number;
    behavioural: number;
  };
}

export class PagingConfig {
  pageIndex: number;
  pageSize: number;
  totalPages: number;
  totalResultRecords: number;
}

export class SearchProjectParticipantsData {
  config: SearchProjectParticipantsDataConfig;
  data: ParticipantInfo[];
  paging: PagingConfig;
}

export class CandidateReportLog{
  requestCreated: Date;
  responseCreated: Date;
  responseFileUrl:string;
  requestFileUrl:string;
}

export enum ParticipantStatusType {
  'COMPLETED' = 'COMPLETED',
  'IN_PROGRESS' = 'IN_PROGRESS',
  'NOT_STARTED' = 'NOT_STARTED',
  'HIRED' = 'HIRED',
  'NOT_HIRED' = 'NOT_HIRED',
  'UNDEFINED' = 'UNDEFINED',
  'NOT_INVITED' = 'NOT_INVITED',
  'LOCKED' = 'LOCKED'
}

export enum ParticipantHiredType {
  'UNDEFINED' = 0,
  'NOT_HIRED' = 1,
  'HIRED' = 2
}

export interface UpdateHiredStatus {
  projectId: number;
  successProfileId: number;
  participantIds: number[];
  hired: ParticipantHiredType;
}

// Interface for the nested 'project' object
export interface Project {
  proctoringConfigurationId: number;
  proctoringConfigurationLabel: string;
}

// Interface for the nested 'assessments' array
export interface Assessment {
  assessmentId: number;
  kfasAssessmentId: number;
  type: string;
  name: string;
  score: string;
  proctoringConfigurationId: number;
  proctoringConfigurationLabel: string;
  isProctoringResultsAvailable: boolean;
}

// Main interface for the proctoring result
export interface ProctoringResult {
  overallScore: string;
  project: Project;
  assessments: Assessment[];
}