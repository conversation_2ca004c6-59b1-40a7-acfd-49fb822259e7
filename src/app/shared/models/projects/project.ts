/**
 * Client projects result set.
 */
export class ClientProjects {
  totalResults: number;
  clientId: number;
  projects: ProjectSummary[];
}

/**
 * Project summary class.
 */
export class ProjectSummary {
  projectId: number;
  type: string;
  projectName: string;
  createdDate: Date;
  latestAssessmentCompletedData: Date;
  completedAssessments: number;
  totalAssessments: number;
  userGroup: string;
  totalAssessmentSummaryText: string;
  clientName: string;
  clientId: number;
  isKFAssess2: boolean;
  version: string;
}

/**
 * Project summary class.
 */
export class ParticipantMetadata {
  data: {
    metadata: {
      searchOn: FilterOption[];
      id: string;
      name: string;
      value: string;
    }[];
  };
}

/**
 * Filter option object
 */
export class FilterOption {
  id: string;
  name: string;
  value: string;
  options?: FilterOption[];
}

export enum ProjectProductType {
  'SELECT' = 'SELECT',
  'ASSESS' = 'ASSESS'
}
