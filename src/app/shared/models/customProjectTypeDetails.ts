/**
 * Class for Cognitive Ability & other Assessments
 */
export class AssessmentsObject {
    name: string;
    desc: string;
    value: string;
    id: number;
    label: string;
    timeDuration: number;
    canBeOnlyPotential: boolean;
}

export class AssessmentSelectedObj {
    customProjectTypeAssessmentId?: number;
    assessmentTypeId?: number;
    assessmentType: string;
    assessmentSetting: string;
}

export const CognitiveAssessmentsFields: AssessmentsObject[] = [
    { name: 'elementsNumerical', desc: 'Elements Numerical', value: 'ElementsNumerical', id: 2, label: 'Elements Numerical', timeDuration: 16, canBeOnlyPotential: false },
    { name: 'elementsVerbal', desc: 'Elements Verbal', value: 'ElementsVerbal', id: 5, label: 'Elements Verbal', timeDuration: 16, canBeOnlyPotential: false },
    { name: 'elementsLogical', desc: 'Elements Logical', value: 'ElementsLogical', id: 10, label: 'Elements Logical', timeDuration: 15, canBeOnlyPotential: false },
    { name: 'aspectsNumerical', desc: 'Aspects Numerical', value: 'AspectsNumerical', id: 15, label: 'Aspects Numerical', timeDuration: 15, canBeOnlyPotential: false },
    { name: 'aspectsVerbal', desc: 'Aspects Verbal', value: 'AspectsVerbal', id: 16, label: 'Aspects Verbal', timeDuration: 13, canBeOnlyPotential: false },
    { name: 'aspectsChecking', desc: 'Aspects Checking', value: 'AspectsChecking', id: 17, label: 'Aspects Checking', timeDuration: 8, canBeOnlyPotential: false },
    { name: 'abstractReasoning', desc: 'AbstractReasoning', value: 'AbstractReasoning', id: 28, label: 'Abstract Reasoning', timeDuration: 40, canBeOnlyPotential: true }
];

export const OtherAssessmentsFields: AssessmentsObject[] = [
    { name: 'competencies', desc: 'Kf4d Behaviours', value: 'Kf4dBehaviours', id: 23, label: 'Competencies', timeDuration: 20, canBeOnlyPotential: false },
    { name: 'traits', desc: 'Kf4d Traits', value: 'Kf4dTraits', id: 25, label: 'Traits', timeDuration: 15, canBeOnlyPotential: true },
    { name: 'drivers', desc: 'Kf4d Drivers', value: 'Kf4dDrivers', id: 24, label: 'Drivers', timeDuration: 10, canBeOnlyPotential: true },
    { name: 'tsi', desc: 'TechnicalSkillsInventory', value: 'TechnicalSkillsInventory', id: 29, label: 'TSI', timeDuration: 20, canBeOnlyPotential: false },
    { name: 'experiences', desc: 'Experiences', value: 'Experiences', id: 26, label: 'Experiences', timeDuration: 10, canBeOnlyPotential: true },
    { name: 'rolePreferences', desc: 'Preferences', value: 'Preferences', id: 27, label: 'Role Preferences', timeDuration: 3, canBeOnlyPotential: true },
    { name: 'inclusiveLeaderSJT', desc: 'InclusiveLeaderSjt', value: 'InclusiveLeaderSjt', id: 32, label: 'Inclusive Leader SJT', timeDuration: 40, canBeOnlyPotential: false }
];

export class assessmentOptionsObj {
    label: string;
    value: string;
}

export const AssessmentOptions: assessmentOptionsObj[] = [
    {label: 'Not Included', value: 'NotIncluded'},
    {label: 'Required', value: 'Required'},
    {label: 'Recommended', value: 'Recommended'},
    {label: 'Optional', value: 'Optional'},
    {label: 'Based on SP', value: 'BasedOnSP'}
];

export class CustomProjectTypeDescription {
    id: number;
    projectType: string;
    createdBy: string;
    lastAmended: string;
    usedIn: number;
    reportFilterCreated: boolean;
}

export class AssessmentInitiatedBy {
    firstNameKey: string;
    lastNameKey: string;
    clientId: number;
}

export class CustomProjectTypeObj {
    customProjectTypeId: number;
    name: string;
    useSuccessProfile: boolean;
    usePotentialFunctionality: boolean;
    scoring: string;
    initiatedBy: AssessmentInitiatedBy;
    lastAmendedDate: string;
    assessments: AssessmentSelectedObj[];
    projectCount: number;
    clientId: number;
    userGroup: string;
    isFilterCreated: boolean
}

export class CustomProjectTypeHistory{
  historyId: number;
  customProjectTypeId: number;
  dateTimeOfChange: string;
  amendedBy: string;
  createdBy: AssessmentInitiatedBy;
  reasonForChange: string;
}

export const CPTColumns = [
  {
    name: 'projectType',
    label: 'PROJECT TYPE',
    width: '30%',
    type: 'text'
  },
  {
    name: 'createdBy',
    label: 'CREATED BY',
    type: 'text',
    width: '20%',
  },
  {
    name: 'lastAmended',
    label: 'LAST AMENDED',
    type: 'text',
    width: '15%',
  },
  {
    name: 'usedIn',
    label: 'USED IN (PROJECTS)',
    type: 'text',
    width: '20%',
  },
  {
    name: 'reportFilterCreated',
    label: '',
    width: '15%',
    type: 'text'
  }
];

export const HISTORY_COLUMNS = [
  {
    name: 'dateTimeOfChange',
    label: 'Date',
    width: '30%',
    type: 'text',
  },
  {
    name: 'amendedBy',
    label: 'BY',
    type: 'text',
    width: '30%',
  },
  {
    name: 'reasonForChange',
    label: 'Comments',
    type: 'text',
    width: '40%',
  }
];

export const Months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];