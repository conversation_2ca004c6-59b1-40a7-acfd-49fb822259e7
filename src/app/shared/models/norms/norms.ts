/**
 * Class for getting custom norms for a client.
 */
 export class ClientCustomNormDescriptions {
  customNorms: CustomNormDescription[];
}

/**
 * Class for saving custom norm with details
 */
 export class SavedCustomNorm {
  normNo: number;
}

/**
 * Class for custom norm
 */
export class CustomNormDescription {
  normNo: number;
  clientId: number;
  displayOrder: number;
  textId: number;
  text: string;
  norms: Norm[];
}

/**
 * Class for norm
 */
 export class Norm {
  Id: number;
  normNo: number;
  testId: number;
  versionId: number;
  mean: number;
  sd: number;
}

/**
 * Client Custom Norms per Assessment Type
 */
 export class ClientAssessmentNorms {
  kfasAssessmentId: number;
  norms: NormOption[];
}

export class NormOption {
  normNo?: number;
  text: string;
}

export class ClientAssessmentNormsModel{
  norms: ClientAssessmentNorms[];
}
