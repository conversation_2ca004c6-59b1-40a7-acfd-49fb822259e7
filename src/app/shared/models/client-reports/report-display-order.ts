import { ReportConfiguration } from '..';

/**
 * Object to hold  report properties required for display order component.
 */
export class ReportDisplayOrder {
  id: number;
  reportName: string;
  kfasClientId: number;
  originalDisplayOrder: number;

  /**
   *Creates an instance of ReportDisplayOrder.
   */
  constructor(reportConfiguration: ReportConfiguration) {
    this.id = reportConfiguration.id;
    this.kfasClientId = +reportConfiguration.kfasClientId;
    this.reportName = reportConfiguration.nameReference;
    this.originalDisplayOrder = reportConfiguration.displayOrder;
  }
}

/**
 * Report display order update Request object.
 */
export class ReportDisplayOrderUpdateRequest {
  reportIds: number[];
  kfasClientId: number;

  /**
   * Creates a new instance of ReportDisplayOrderUpdateRequest.
   * @param reportDisplayOrder  display order object.
   */
  constructor(reportDisplayOrder: ReportDisplayOrder[]) {
    if (reportDisplayOrder === undefined || reportDisplayOrder.length === 0) {
     throw new Error('report display order object must contain values');
    }
    this.reportIds = reportDisplayOrder.map(r => r.id);
    this.kfasClientId = reportDisplayOrder[0].kfasClientId;
  }
}
