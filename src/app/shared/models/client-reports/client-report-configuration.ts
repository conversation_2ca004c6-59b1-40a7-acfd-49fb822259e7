import { Type } from 'class-transformer';

/**
 * Class for report configurations for a client.
 */
export class ReportConfiguration {
  id: number;
  nameReference: string;
  reportKey: string;
  isParticipant: boolean;
  cmsManifestId: string;
  labels: string;
  kfasClientId: string;

  // This flag is set at this level as all filters for this report instance are either inherited or are applicable directly to given client.
  filtersInhertitedFromParent: boolean;

  // projects this report is valid for.
  reportProjects: string[];

  reportAssesments: string[];

  @Type(() => LanguageDetail)
  languages: LanguageDetail[];
  displayOrder: number;

  @Type(() => ReportFilter)
  reportFilters: ReportFilter[];
  name: string;

  getLanguages(): string[] {
    return this.languages.map(
      x => `${x.language.languageName}(${x.language.culture})`
    );
  }
}

export interface IDictionary {
  [key: string]: number[];
}

export class ReportLanguages {
  reportLanguages: IDictionary;

  @Type(() => LanguageDetail)
  languages: LanguageDetail[];
}

export class Language {
  languageID: number;
  languageName: string;
  languageNameTextId: number;
  culture: string;
  productsLocale: string;
}

export class LanguageDetail {
  languageId: number;

  @Type(() => Language)
  language: Language;
}

/**
 * Filters applicable for a given report
 */
export class ReportFilter {
  reportId: number;
  filterValues: string;
  filterType: string;
  filterPolicy: string;
  clientId: number;
  filterValuesText: string[];
  filterId?: number;
}
