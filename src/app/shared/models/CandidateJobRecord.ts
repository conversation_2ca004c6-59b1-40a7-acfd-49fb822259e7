import { Candidate<PERSON>ob<PERSON>ogR<PERSON>ord } from '.';

export class Candidate<PERSON><PERSON><PERSON><PERSON>ord {
  jobId: number;
  received: string;
  request: {
    clientApplication: string;
    clientMachine: string;
    content: string;
    contentType: string;
    handler: string;
    headers: any;
    isInternal: boolean;
    parentJobId: number;
    requestorUserId: number;
    sent: string;
  };
  status: string;
  logs?: CandidateJobLogRecord[];
  details: CandidateJobDetails;
}


export class CandidateJobDetails {
  displayName: string;
  handlerPath: string;
  jobId: number;
  jobNodeName: string;
  jobNodeType: number;
  parentJobId: number;
  processTime: string;
  received: string;
  request: string;
  retryAttempts: number;
  sent: string;
  status: number;
  statusUpdated: string;
  userId: number;
  userName: string;
  userType: number;
}
