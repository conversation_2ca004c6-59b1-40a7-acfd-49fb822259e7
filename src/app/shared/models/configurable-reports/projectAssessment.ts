export class ProjectAssessmentOptions {
    maxAssessmentsAllowed: number;
    hasRoleLevels: boolean;
    levels: Level[] = [];
    projectAssessments: ProjectAssessment[] = [];
    scoreDisplay: ProjectAssessmentOptionsScoreDisplay = new ProjectAssessmentOptionsScoreDisplay();
  }

export class Level {
    name: string;
    description: string;
    code: string;
    order: number;
  }

export class ProjectAssessment {
    name: string;
    kfasAssessmentId: number;
    type: string;
    order: number;
    isMandatory: boolean;
    isRecommended: boolean;
    isOptional: boolean;
  }

export class ProjectAssessmentOptionsScoreDisplay {
    requiresScoreDisplay: boolean;
    scoreDisplayTypes: string[] = [];
  }

