export class ReportPreviewOptions {
    hasCustomCompetencies: boolean;
    projects: ReportPreviewOptionsProject[] = [];
    scoreTypes: string[] = [];
    cmsEnvironment: string[] = [];
    showFitScores: boolean;
    successProfiles: ReportPreviewOptionsSuccessProfile[] = [];
    languages: ReportPreviewOptionsLanguage[] = [];
  }

export class ReportPreviewOptionsProject {
    name: string;
    type: string;
    order: number;
    requiresSuccessProfile: boolean;
  }

export class ReportPreviewOptionsSuccessProfile {
    name: string;
    id: number;
  }

export class ReportPreviewOptionsLanguage {
    name: string;
    id: number;
  }
