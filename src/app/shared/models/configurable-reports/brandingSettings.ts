export class BrandingSettings {
  solutionTextColour: string;
  titleTextColour: string;
  subTitleTextColour: string;
  metadataTextColour: string;
  metadataDetailsTextColour: string;
  textInputTextColour: string;
  footerBarColour: string;
  coverImagePath: string;
  coverImageFormat: null | 'LowerThird' | 'LowerTwoThirds' | 'UpperThird' | 'FullPage' | 'NoImage';
  clientLogoPath: string;
  clientLogoPosition: null | 'Left' | 'Right';
  coBranded: boolean;
}

export class BrandingData {
  id: number;
  clientId: number;
  created: any;
  createdBy: any;
  enabled: boolean;
  name: string;
  reports: any[];
  settings: BrandingSettings;
}

// The options for cover image position
export const bgPositions = [
  {
    label: 'Upper 1/3',
    value: 'UpperThird',
  },
  {
    label: 'Lower 1/3',
    value: 'LowerThird',
  },
  {
    label: 'Lower 2/3',
    value: 'LowerTwoThirds',
  },
  {
    label: 'Full Page',
    value: 'FullPage',
  },
  {
    label: 'No Image',
    value: 'NoImage',
  },
];

// The options for client logo position
export const logoPositions = [
  {
    label: 'Right',
    value: 'Right',
  },
  {
    label: 'Left',
    value: 'Left',
  },
];
