import { AuthenticationService } from "@/services";
import { Directive, ElementRef, Input, OnInit } from "@angular/core";
import { AuthRole } from "../models";

/**
 * This directive defines whether user can see the element or not (based on his auth roles).
 */
@Directive({
  selector: "[allowedRoles]",
})
export class AllowedRolesDirective implements OnInit {
  /**
   * List of AuthRoles allowed to see this element
   */
  @Input("allowedRoles") allowedRoles: AuthRole[];

  constructor(
    private el: ElementRef,
    private authenticationService: AuthenticationService
  ) {}

  ngOnInit() {
    if (!this.authenticationService.isInAllowedRoles(this.allowedRoles)) {
      this.el.nativeElement.disabled = true;
      this.el.nativeElement.style.display = "none";
    }
  }
}
