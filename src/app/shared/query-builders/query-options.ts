import { Injectable } from '@angular/core';


export interface QueryBuilder {
  toQueryString: (queryMap: Map<string, string>) => string;
}


@Injectable({
  providedIn: 'root'
})
export class QueryOptions implements QueryBuilder {
  public pageNumber: number;
  public pageSize: number;

  constructor() {
  }


  toQueryString(queryMap: Map<string, string>) {
    let queryString = '?';
    if (queryMap == null) {
      return '';
    }
    queryMap.forEach((value: string, key: string) => {
      queryString = queryString.concat(`${key}=${value}&`);
    });

    return queryString.substring(0, queryString.length - 1);
  }
}
