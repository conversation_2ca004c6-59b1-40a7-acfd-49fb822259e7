import { UploadAssetsService } from '@/services/upload-assets.service';
import { ReportAsset } from '@/shared/models/configurable-reports/ReportAsset';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, Router, RouterStateSnapshot } from '@angular/router';
import { Observable } from 'rxjs';

@Injectable()
export class UploadAssetsResolver implements Resolve<ReportAsset[]> {
  constructor(private uploadAssetsService: UploadAssetsService, private router: Router) {}

  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<ReportAsset[]> {
    const clientId = parseInt(route.queryParams.clientId, 10);
    return this.uploadAssetsService.getClientAssets(clientId);
  }
}
