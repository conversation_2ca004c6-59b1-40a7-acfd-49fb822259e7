import { ParticipantService } from '@/services';
import { Injectable } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  Resolve,
  Router,
  RouterStateSnapshot,
} from '@angular/router';
import { of } from 'rxjs';

@Injectable()
export class ClientParticipantsSearchResolver implements Resolve<any> {
  constructor(private service: ParticipantService, private router: Router) {}

  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
    // if clientProjects is resolved by parent already- return it instead of making new request
    if (route.parent && route.parent.data.clientProjects) {
      return of(route.parent.data.clientProjects);
    }
    // go through the parents until we get route with clientId
    while (route.parent && !route.queryParams.clientId) {
      route = route.parent;
    }
    const clientId = parseInt(route.queryParams.clientId, 10);

    return this.service.getClientParticipantMetadata(clientId);
  }
}
