import { ReportService } from '@/services/report.service';
import { NewReportOptions } from '@/shared/models/reports/newReportOptions';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, Router, RouterStateSnapshot } from '@angular/router';
import { Observable } from 'rxjs';

@Injectable()
export class NewReportOptionsResolver implements Resolve<NewReportOptions> {
  constructor(private service: ReportService, private router: Router) { }

  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot):
    NewReportOptions | Observable<NewReportOptions> | Promise<NewReportOptions> {
    return this.service.getOptions();
  }
}
