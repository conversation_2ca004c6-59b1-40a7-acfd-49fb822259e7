import { ParticipantService } from '@/services';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, Router, RouterStateSnapshot } from '@angular/router';

@Injectable()
export class AssessmentsResolver implements Resolve<any> {
  constructor(private service: ParticipantService) {}

  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): any {
    const candidateId = parseInt(route.queryParams.candidateId, 10);
    const projectId = parseInt(route.queryParams.projectId, 10);
    return this.service.getAssessments(candidateId, projectId);
  }
}
