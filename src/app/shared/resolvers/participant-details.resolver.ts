import { ParticipantService } from '@/services';
import { Injectable } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  Resolve,
  Router,
  RouterStateSnapshot,
} from '@angular/router';
import { of } from 'rxjs';

@Injectable()
export class ParticipantDetailsResolver implements Resolve<any> {
  constructor(private service: ParticipantService, private router: Router) {}

  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): any {
    const participantId = parseInt(route.queryParams.candidateId, 10);
    return this.service.getParticipantDetails(participantId);
  }
}
