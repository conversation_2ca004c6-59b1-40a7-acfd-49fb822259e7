import { BrandingService } from '@/services/branding.service';
import { PortalBrandingSettings } from '@/shared/models';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';
import { Observable } from 'rxjs';

@Injectable()
export class PortalBrandingResolver implements Resolve<PortalBrandingSettings> {
  constructor(private brandingService: BrandingService) {}

  resolve(route: ActivatedRouteSnapshot): Observable<PortalBrandingSettings> {
    const clientId = parseInt(route.queryParams.clientId, 10);
    return this.brandingService.getPortalSettings(clientId);
  }
}
