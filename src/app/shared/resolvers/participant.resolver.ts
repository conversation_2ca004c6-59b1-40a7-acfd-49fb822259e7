import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, Router, RouterStateSnapshot } from '@angular/router';
import { of, Observable } from 'rxjs';

@Injectable()
export class ParticipantResolver implements Resolve<any> {
  constructor() { }

  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot)
    : any | Observable<any> | Promise<any> {
    // if project is resolved by parent already- return it instead of making new request
    if (route.parent && route.parent.data.project) {
      return of(route.parent.data.project);
    }

    const participantId = this.findNumberInRoute(route, params => params.participantId);

    // TODO use actual API method to get participant
    return {
      participantId: participantId,
      firstName: 'Andrew',
      lastName: 'Grainger',
      username: 'ABCD1234',
      email: '<EMAIL>',
      createdDate: (new Date(2020, 5, 23, 11, 45, 12)).toString().split(' ').slice(1, 5).join(' '),
      createdBy: 'Admin Products Hub',
      lastLoggedIn: (new Date(2020, 5, 23, 18, 45, 12)).toString().split(' ').slice(1, 5).join(' '),
      language: 'English (US)',
      platform: 'Product Hub',
      reference: 'XYZ98765',

      userId: 84586868,
      personId: 23055999,
      candidateId: 5991122,
      clientOwnerId: 23001,
      redirectMode: 'RedirectAfterEach',
      redirect: ''
    };
  }

  findNumberInRoute(route: ActivatedRouteSnapshot, selector: Function): number {
    // go through the parents until we get route with required parameter
    while (route.parent && !selector(route.params)) {
      route = route.parent;
    }
    return parseInt(selector(route.params), 10);
  }
}
