import { BrandingService } from '@/services/branding.service';
import { BrandingData } from '@/shared/models/configurable-reports/brandingSettings';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, Router } from '@angular/router';
import { Observable } from 'rxjs';

@Injectable()
export class ReportBrandingResolver implements Resolve<BrandingData> {
  constructor(
    private brandingService: BrandingService,
    private router: Router
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<BrandingData> {
    const clientId = parseInt(route.queryParams.clientId, 10);
    return this.brandingService.getSettings(clientId);
  }
}
