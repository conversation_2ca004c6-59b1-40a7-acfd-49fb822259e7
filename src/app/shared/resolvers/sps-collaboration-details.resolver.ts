import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { SpsCollaborationDetailsService } from '@/services/sps-collaboration-details.service';
import { CollaborationDetails } from '../models/sps-client/collaboration-details';

@Injectable()
export class SpsCollaborationDetailsResolver implements Resolve<CollaborationDetails> { 
    constructor(private spsCollaborationDetailsService: SpsCollaborationDetailsService, private router: Router) { }

    resolve(route: ActivatedRouteSnapshot): Observable<any> {
        if (! localStorage.getItem('selectedClient')) {
            this.router.navigate(['/client-tiles']);
        }
        const selectedSpsClient = JSON.parse(localStorage.getItem('selectedClient'));
        const clientId = parseInt(selectedSpsClient.id, 10);
        const collabId = parseInt(route.queryParams.collabId, 10);
        return this.spsCollaborationDetailsService.getCollaborationDetails(clientId, collabId);
    }

}