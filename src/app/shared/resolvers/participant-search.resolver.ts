import { ProjectService } from '@/services';
import { Injectable } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  Resolve,
  Router,
  RouterStateSnapshot,
} from '@angular/router';
import { Observable } from 'rxjs';
import { ParticipantMetadata } from '../models';

@Injectable()
export class ParticipantsSearchResolver
  implements Resolve<ParticipantMetadata> {
  constructor(private projectService: ProjectService, private router: Router) {}

  resolve(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ):
    | ParticipantMetadata
    | Observable<ParticipantMetadata>
    | Promise<ParticipantMetadata> {
    return this.projectService.getParticipantMetadata();
  }
}
