
import { ReportService } from '@/services/report.service';
import { Report } from '@/shared/models/reports/report';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, Router, RouterStateSnapshot } from '@angular/router';
import { Observable } from 'rxjs';

@Injectable()
export class ReportsResolver implements Resolve<Report[]> {
  constructor(private service: ReportService, private router: Router) { }

  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Report[] | Observable<Report[]> | Promise<Report[]> {
    return this.service.getAllReports();
  }
}
