import { ProjectService } from '@/services';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, Router, RouterStateSnapshot } from '@angular/router';

@Injectable()
export class ProjectReportsResolver implements Resolve<any> {
  constructor(private service: ProjectService) { }

  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
    const projectId = parseInt(route.queryParams.projectId, 10);
    return this.service.getProjectReports(projectId);
  }
}
