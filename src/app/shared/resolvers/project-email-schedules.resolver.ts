import { ProjectService } from '@/services';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';
import { Observable } from 'rxjs';
import { ProjectDetails } from '../models';
import { ProjectEmailSchedule } from '../models/ProjectEmailSchedule';

@Injectable()
export class ProjectEmailSchedulesResolver implements Resolve<ProjectEmailSchedule[]> {
  constructor(private projectService: ProjectService) {}

  resolve(route: ActivatedRouteSnapshot): Observable<ProjectEmailSchedule[]> {
    const projectId = parseInt(route.queryParams.projectId, 10);

    return this.projectService.getEmailSchedules(projectId);
  }
}
