import { ProjectService } from '@/services';
import { Injectable } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  Resolve,
  RouterStateSnapshot,
} from '@angular/router';
import { of, Observable } from 'rxjs';

@Injectable()
export class ProjectSearchMetadataResolver implements Resolve<any> {
  constructor(private projectService: ProjectService) {}

  resolve(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): any | Observable<any> | Promise<any> {
    const clientId = parseInt(route.queryParams.clientId, 10);
    return this.projectService.getProjectSearchMetadata(clientId);
  }
}
