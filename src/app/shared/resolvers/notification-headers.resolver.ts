import { ClientSearchService } from '@/services';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';
import { of, Observable } from 'rxjs';
import { NotificationHeader } from '../models/NotificationHeader';

@Injectable()
export class NotificationHeadersResolver
  implements Resolve<NotificationHeader[]> {
  constructor(private clientService: ClientSearchService) {}

  resolve(
    route: ActivatedRouteSnapshot
  ):
    | NotificationHeader[]
    | Observable<NotificationHeader[]>
    | Promise<NotificationHeader[]> {
    const clientId = parseInt(route.queryParams.clientId, 10);
    return clientId ? this.clientService.getClientNotificationHeaders(clientId) : of(null);
  }
}
