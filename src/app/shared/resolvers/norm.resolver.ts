import { NormService } from '@/services';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';
import { Observable, of } from 'rxjs';
import { CustomNormDescription } from '../models';

@Injectable()
export class NormResolver implements Resolve<CustomNormDescription> {
  constructor(private normService: NormService) {}

  resolve(route: ActivatedRouteSnapshot): Observable<CustomNormDescription> {
    const normNo = parseInt(route.queryParams.normNo, 10);
    const clientId = parseInt(route.queryParams.clientId, 0);

    if (normNo < 0) {

      let newNorm: CustomNormDescription = {
        normNo: normNo,
        clientId: clientId,
        textId: 0,
        text: 'New norm',
        displayOrder: 0,
        norms: new Array()
      };
      
      return of(newNorm);
    }

    return this.normService.getCustomNormWithDetails(clientId, normNo);
  }
}
