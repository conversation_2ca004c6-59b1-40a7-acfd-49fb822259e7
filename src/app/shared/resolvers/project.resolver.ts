import { ProjectService } from '@/services';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';
import { Observable } from 'rxjs';
import { ProjectDetails } from '../models';

@Injectable()
export class ProjectResolver implements Resolve<ProjectDetails> {
  constructor(private projectService: ProjectService) {}

  resolve(route: ActivatedRouteSnapshot): Observable<ProjectDetails> {
    const clientId = parseInt(route.queryParams.clientId, 10);
    const projectId = parseInt(route.queryParams.projectId, 10);

    return this.projectService.getProjectDetails(clientId, projectId);
  }
}
