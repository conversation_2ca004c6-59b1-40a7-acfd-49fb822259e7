import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';
import { Observable } from 'rxjs';
import { SpsCollaborationStakeholder } from '../models/sps-client/sps-collaboration-stakeholder';
import { CollaboratorStakeholderService } from '@/services/sps-collboration-stakeholder.service';

@Injectable()
export class SpsCollaborationDetailsResolver implements Resolve<SpsCollaborationStakeholder> { 
    constructor(private spsCollaboratiorStakeholderService: CollaboratorStakeholderService) { }

    resolve(route: ActivatedRouteSnapshot): Observable<any> {
        const collabId = parseInt(route.queryParams.collabId, 10);
        const pageIndex = route.queryParams.pageIndex;
        const pageSize = route.queryParams.pageSize;
        return this.spsCollaboratiorStakeholderService.getCollaborationStakeholderDetails(collabId, pageIndex, pageSize );
    }

}