import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { StakeholderDetailsService } from '@/services/sps-stakeholder-details.service';

@Injectable()
export class StakeholderResolver implements Resolve<any> {
    constructor(private stakeholderDetailsService: StakeholderDetailsService, private router: Router) { }

    resolve(route: ActivatedRouteSnapshot): Observable<any> {
        if (! localStorage.getItem('selectedClient')) {
            this.router.navigate(['/client-tiles']);
        }
        const selectedSpsClient = JSON.parse(localStorage.getItem('selectedClient'));
        const clientId = parseInt(selectedSpsClient.id, 10);
        const emailId = route.queryParams.emailId;
        const pageIndex = route.queryParams.pageIndex;
        const pageSize = route.queryParams.pageSize;
        return this.stakeholderDetailsService.getStakeholderDetails(clientId, emailId, pageIndex, pageSize );
    }

}
