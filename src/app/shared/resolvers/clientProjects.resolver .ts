import { ClientSearchService, ProjectService } from '@/services';
import { Injectable } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  Resolve,
  Router,
  RouterStateSnapshot,
} from '@angular/router';
import { of, Observable } from 'rxjs';
import { ClientProjects } from '../models';

@Injectable()
export class ClientProjectsResolver implements Resolve<ClientProjects> {
  constructor(private projectService: ProjectService, private router: Router) {}

  resolve(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): ClientProjects | Observable<ClientProjects> | Promise<ClientProjects> {
    const clientId = parseInt(route.queryParams.clientId, 10);
    return this.projectService.getProjects(clientId);
  }
}
