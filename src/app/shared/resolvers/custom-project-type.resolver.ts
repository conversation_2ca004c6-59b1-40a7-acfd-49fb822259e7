import { CustomProjectTypeService } from '@/services';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';
import { of, Observable } from 'rxjs';
import { CustomProjectTypeObj } from '../models/customProjectTypeDetails';

@Injectable()
export class CustomProjectTypeResolver implements Resolve<CustomProjectTypeObj> {
  constructor(private customProjectTypeService: CustomProjectTypeService) {}

  resolve(
    route: ActivatedRouteSnapshot
  ): CustomProjectTypeObj | Observable<CustomProjectTypeObj> | Promise<CustomProjectTypeObj> {
    const projectTypeId = parseInt(route.queryParams.projectTypeId, 10);
    return projectTypeId ? this.customProjectTypeService.getCustomProjectTypeDetails(projectTypeId) : of(null);
  }
}
