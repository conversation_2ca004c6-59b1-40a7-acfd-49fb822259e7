
import { ReportService } from '@/services/report.service';
import { Report } from '@/shared/models/reports/report';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, Router, RouterStateSnapshot } from '@angular/router';
import { of, Observable } from 'rxjs';
import { map } from 'rxjs/operators';

@Injectable()
export class ReportResolver implements Resolve<Report> {
  constructor(private service: ReportService, private router: Router) { }

  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Report | Observable<Report> | Promise<Report> {
    const reportId = parseInt(route.params.reportId, 10);
    return this.service.getAllReports().pipe(map(x => x.find(r => r.reportId === reportId)));
  }
}
