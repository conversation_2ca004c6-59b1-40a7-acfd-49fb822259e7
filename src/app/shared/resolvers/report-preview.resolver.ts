import { ReportPreviewService } from '@/services/report-preview.service';
import { ReportPreviewOptions } from '@/shared/models/configurable-reports/reportPreviewOptions';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';
import { Observable } from 'rxjs';

@Injectable()
export class ReportPreviewResolver implements Resolve<ReportPreviewOptions> {
  constructor(private previewService: ReportPreviewService) {}

  resolve(route: ActivatedRouteSnapshot): Observable<ReportPreviewOptions> {
    const clientId = parseInt(route.queryParams.clientId, 10);
    return this.previewService.getOptions(clientId);
  }
}
