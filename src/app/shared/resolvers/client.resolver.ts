import { ClientSearchService } from '@/services';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';
import { of, Observable } from 'rxjs';
import { ClientDetails } from '../models';

@Injectable()
export class ClientResolver implements Resolve<ClientDetails> {
  constructor(private clientService: ClientSearchService) {}

  resolve(
    route: ActivatedRouteSnapshot
  ): ClientDetails | Observable<ClientDetails> | Promise<ClientDetails> {
    const clientId = parseInt(route.queryParams.clientId, 10);
    return clientId ? this.clientService.getClientById(clientId) : of(null);
  }
}
