import { LanguagesService } from '@/services';
import { Pipe, PipeTransform } from '@angular/core';

@Pipe({ name: 'language' })
export class LanguagePipe implements PipeTransform {
  constructor(private languagesService: LanguagesService) {}

  transform(locale: string): number {
    const langs = this.languagesService.getLanguages();
    const lang = langs.find((item) => item.locale === locale);
    return lang ? lang.name : locale;
  }
}
