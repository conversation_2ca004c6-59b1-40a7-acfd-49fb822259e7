import { Pipe, PipeTransform } from "@angular/core";
import { AuthRoles, getAliasForAuthRole } from "@/shared/models/authRoles";

@Pipe({ name: "usergroup" })
export class UsergroupPipe implements PipeTransform {
  transform(groupName: string): string {
    try {
      const groupnameAlias = getAliasForAuthRole(groupName);
      return AuthRoles[groupnameAlias].name;
    }
    catch {
      return groupName;
    }
  }
}
