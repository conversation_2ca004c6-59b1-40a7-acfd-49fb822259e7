import { Pipe, PipeTransform } from "@angular/core";
import { ParticipantSendConfig } from "../models/ProjectEmailSchedule";

@Pipe({ name: `scheduleParticipantConfig` })
export class ScheduleParticipantConfigPipe implements PipeTransform {
  /**
   * Transforms ParticipantSendConfig object to its short human-readable descrition
   * @param schedule ParticipantSendConfig object
   * @returns a short detailed description
   */
  transform(pptConfig: ParticipantSendConfig): string {
    if(!pptConfig) {
      return ``;
    }

    switch (pptConfig.participantSendType) {
      case `ALL_PARTICIPANTS`: {
        return `All`;
      }
      case `PARTICIPANTS_BY_PROGRESS`: {
        const progress =
          pptConfig.participantProgressTypes;

        return `By progress: ${progress.join(", ")}`;
      }
      default: {
        return pptConfig.participantSendType;
      }
    }
  }
}
