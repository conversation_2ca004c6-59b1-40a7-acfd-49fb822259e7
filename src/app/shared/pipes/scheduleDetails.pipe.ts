import { Pipe, PipeTransform } from "@angular/core";
import {
  ProjectByDateSendDates,
  ProjectDateTimeWithTimezone,
  ProjectDayOfMonth,
  ProjectEmailSchedule,
  ProjectEmailScheduleByDate,
  ProjectEmailScheduleRecurring,
  ProjectRecurringConfig,
  ProjectRecurringEndDate,
} from "../models/ProjectEmailSchedule";

@Pipe({ name: `scheduleDetails` })
export class ScheduleDetailsPipe implements PipeTransform {
  /** Keep all text sentences here */
  text = {
    none: () => `-`,
    byDates: (d, n) => `${d}\nand ${n} more date(s) set...`,
    recurringByDate: (d, s, z) => `${d} (UTC${s}${z})`,
    recurringEndsOnDate: (d) => `Ends on ${d}`,
    recurringEndsOnProjectEnd: () => `Ends on PROJECT_END_DATE`,
    recurringEndsAfterOccurences: (o) => `Ends after ${o} occurence(s)`,
    recurringRepeatsByMonth: (p) => `Repeats each ${p} of the month`,
    recurringRepeatsByOthers: (p) => `Repeats every ${p}`,
    dayNumberOfMonth: (n) => {
      if (typeof n !== `number`) return `${n} day`;

      if (n > 10 && n < 20) return `${n}th day`;

      switch (n % 10) {
        case 1: {
          return `${n}st day`;
        }
        case 2: {
          return `${n}nd day`;
        }
        case 3: {
          return `${n}rd day`;
        }
        default: {
          return `${n}th day`;
        }
      }
    },
  };

  /**
   * Transforms ProjectEmailSchedule object to its short human-readable descrition
   * @param schedule Email Schedule object
   * @returns a short detailed description
   */
  transform(schedule: ProjectEmailSchedule): string {
    switch (schedule.settingType) {
      case `PARTICIPANT_ADDED`:
      case `MANUALLY`: {
        return this.text.none();
      }
      case `BY_DATE`: {
        const scheduleByDate = schedule as ProjectEmailScheduleByDate;
        return this.transformByDate(scheduleByDate.byDateConfig.sendDates);
      }
      case `RECURRING_SCHEDULE`: {
        const scheduleRecurring = schedule as ProjectEmailScheduleRecurring;
        return this.transformByRecurring(scheduleRecurring.byRecurringConfig);
      }
    }
  }

  transformByDate(dates: ProjectByDateSendDates[]): string {
    switch (dates.length) {
      case 0: {
        return this.text.none();
      }
      case 1: {
        return this.transformDateTimeWithTimeZone(dates[0].dueDate);
      }
      default: {
        const date = this.transformDateTimeWithTimeZone(dates[0].dueDate);
        const andMoreCount = dates.length - 1;
        return this.text.byDates(date, andMoreCount);
      }
    }
  }

  transformDateTimeWithTimeZone(date: ProjectDateTimeWithTimezone) {
      const { dateTime, timeZone } = date;
      const datetime = dateTime.toLocaleString();
      const sign = timeZone > 0 ? `+` : ``;

      return this.text.recurringByDate(datetime, sign, timeZone);
    }

  transformByRecurring(config: ProjectRecurringConfig): string {
    let repeats;
    const interval = config.recurrenceInterval;

    switch (interval.scheduleRepeats) {
      case `MONTH`: {
        repeats = this.transformRepeatsByMonth(interval.recurringMonth);
        break;
      }
      case `WEEK`:
      case `WEEKDAY`: {
        repeats = this.text.recurringRepeatsByOthers(interval.scheduleRepeats);
        break;
      }
    }

    const ends = this.transformEnds(interval.recurringEnd);

    return `${repeats}\n${ends}`;
  }

  transformEnds(config: ProjectRecurringEndDate) {
    switch (config.endType) {
      case `PROJECT_END_DATE`: {
        return this.text.recurringEndsOnProjectEnd();
      }
      case `ON_DATE`: {
        const date = this.transformDateTimeWithTimeZone(config.endDate);
        return this.text.recurringEndsOnDate(date);
      }
      case `AFTER_OCCURENCE`: {
        const times = config.afterOccurrenceCount;
        return this.text.recurringEndsAfterOccurences(times);
      }
    }
  }

  transformRepeatsByMonth(config: ProjectDayOfMonth) {
    let eachPeriod;

    switch (config.type) {
      case `DAY_OF_MONTH`: {
        eachPeriod = this.text.dayNumberOfMonth(config.dayOfMonth);
        break;
      }
      case `WEEK_DAY_SELECTION`: {
        eachPeriod = this.getWeekDayNumberOfMonth(config);
        break;
      }
    }

    return this.text.recurringRepeatsByMonth(eachPeriod);
  }

  getWeekDayNumberOfMonth(config: ProjectDayOfMonth) {
    const order = config.weekDaySelection.toLowerCase();
    const weekday = config.dayInWeek[0] +config.dayInWeek.slice(1).toLowerCase();

    return `${order} ${weekday}`;
  }
}
