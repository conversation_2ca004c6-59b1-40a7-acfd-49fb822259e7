import { Pipe, PipeTransform } from '@angular/core';

export enum AssessmentType {
  'None' = 0,
  'Analytic' = 1,
  'Elements Numerical' = 2,
  'Dimensions Competency' = 3,
  'Elements Verbal' = 5,
  'Verbal Trial' = 6,
  'Numerical Trial' = 7,
  'Verbal Verification' = 8,
  'Numerical Verification' = 9,
  'Elements Logical' = 10,
  'Logical Verification' = 11,
  'Aspects Styles' = 12,
  'MultiView' = 13,
  'Drives' = 14,
  'Aspects Numerical' = 15,
  'Aspects Verbal' = 16,
  'Aspects Checking' = 17,
  'Aspects Judgement' = 18,
  'Composite' = 19,
  'SJT' = 20,
  'KF4D' = 21,
  'Kf4d JobProfile' = 22,
  'Kf4d Competency' = 23,
  'Kf4d Drivers' = 24,
  'Kf4d Traits' = 25,
  'Experiences' = 26,
  'Preferences' = 27,
  'Abstract Reasoning' = 28,
  'Technical Skills Inventory' = 29,
  'ELCA' = 30,
  'Virtual Recruiter' = 31,
  'Inclusive Leader Sjt' = 32,
}

@Pipe({ name: 'assessmentById' })
export class AssessmentByIdPipe implements PipeTransform {
  transform(assessmentId: number | string) {
    return AssessmentType[assessmentId] || assessmentId;
  }
}

export function isCognitiveAbilityAssessment(assessmentType: AssessmentType): boolean {
  return assessmentType == AssessmentType['Elements Logical'] ||
    assessmentType == AssessmentType['Elements Numerical'] ||
    assessmentType == AssessmentType['Elements Verbal'] ||
    assessmentType == AssessmentType['Aspects Checking'] ||
    assessmentType == AssessmentType['Aspects Numerical'] ||
    assessmentType == AssessmentType['Aspects Verbal'];
}
