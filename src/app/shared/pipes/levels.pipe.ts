import { Pipe, PipeTransform } from '@angular/core';

@Pipe({ name: 'levels' })
export class LevelsPipe implements PipeTransform {
  levels = {
    POT_LVL_09: 'Individual Contributor/ Professional',
    POT_LVL_08: 'Team Lead',
    POT_LVL_07: 'First-level Leader',
    POT_LVL_06: 'Mid-level Leader',
    POT_LVL_05: 'Functional Leader',
    POT_LVL_04: 'Business or Organizational Unit/Division Leader',
    POT_LVL_03: 'Senior/ Top Functional Executive',
    POT_LVL_02: 'Top Business or Organizational Group Executive',
    POT_LVL_01: 'Chief Executive Officer/Top Organizational Executive',
  };

  constructor() {}

  transform(levelCode: string): number {
    return this.levels[levelCode] || levelCode;
  }
}
