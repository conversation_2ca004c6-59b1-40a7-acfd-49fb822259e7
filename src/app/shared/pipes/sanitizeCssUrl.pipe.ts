import { <PERSON>pe, PipeTransform } from '@angular/core';
import { Dom<PERSON>anitizer, SafeStyle } from '@angular/platform-browser';

@Pipe({ name: 'sanitizeCssUrl' })
export class SanitizeCssUrlPipe implements PipeTransform {
  constructor(private sanitizer: DomSanitizer) {}

  transform(path: string): SafeStyle {
    return path !== ''
      ? this.sanitizer.bypassSecurityTrustStyle(`url("${path}")`)
      : null;
  }
}
