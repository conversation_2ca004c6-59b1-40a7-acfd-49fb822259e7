import { AbstractControl, ValidationErrors } from '@angular/forms';

export class CustomValidators {
    private static digitPattern = /\d/;
    private static uppercasePattern = /[A-Z]/;
    private static lowercasePattern = /[a-z]/;
    private static specialPattern = /[!@#$%^&*()\-+=`~><,.]/;

    private static patternValidator(control: AbstractControl, pattern: RegExp, error: ValidationErrors): ValidationErrors {
        if (!control.value) {
            // if control is empty return no error
            return null;
        }

        // test the value of the control against the regexp supplied
        const valid = pattern.test(control.value);

        return valid ? null : error;
    }

    static hasNumber(control: AbstractControl): ValidationErrors {
        return CustomValidators.patternValidator(control, CustomValidators.digitPattern, { hasNumber: true });
    }

    static hasUppercase(control: AbstractControl): ValidationErrors {
        return CustomValidators.patternValidator(control, CustomValidators.uppercasePattern, { hasUppercase: true });
    }

    static hasLowercase(control: AbstractControl): ValidationErrors {
        return CustomValidators.patternValidator(control, CustomValidators.lowercasePattern, { hasLowercase: true });
    }

    static hasSpecial(control: AbstractControl): ValidationErrors {
        return CustomValidators.patternValidator(control, CustomValidators.specialPattern, { hasSpecial: true });
    }
}
