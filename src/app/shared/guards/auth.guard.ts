﻿import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, Router, RouterStateSnapshot } from '@angular/router';

import { AlertService, AuthenticationService } from '@/services';

@Injectable({ providedIn: 'root' })
export class AuthGuard implements CanActivate {
  constructor(
    private router: Router,
    private authenticationService: AuthenticationService,
    private alertService: AlertService
  ) { }

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
    if (!this.authenticationService.currentUser) {
      // not logged in so redirect to login page with the return url
      this.router.navigate(['/login'], { queryParams: { returnUrl: state.url } });
      return false;
    }

    const { allowedRoles } = route.data;
    const allowed = this.authenticationService.isInAllowedRoles(allowedRoles);

    if (!allowed) {
      // restricted by roles granted for this user
      if (!this.authenticationService.currentUser.roles.length) {
        this.alertService.error('User doesn\'t have any role.');
        this.router.navigate(['/login']);
        return false;
      }
      this.router.navigate(['/']);
      return false;
    }

    // authorised so return true
    return true;
  }
}
