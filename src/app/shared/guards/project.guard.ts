﻿import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, Router, RouterStateSnapshot } from '@angular/router';

import { AuthenticationService, ProjectService } from '@/services';
import { ProjectDetails } from '../models';

@Injectable({ providedIn: 'root' })
export class ProjectGuard implements CanActivate {
  constructor(private router: Router, private authenticationService: AuthenticationService, 
    private projectService: ProjectService) {}

  async canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
    const clientId = state.root.queryParams.clientId;
    const projectId = state.root.queryParams.projectId;
    const project:ProjectDetails = await this.projectService.getProjectDetails(clientId, projectId).toPromise();

    return (project.projectType.toLocaleLowerCase() !== 'sjt' &&
            project.projectType.toLocaleLowerCase() !== 'potential')
        ? true
        : this.router.navigate(['../clients/project-candidates'], {
          queryParams: {
            clientId: clientId,
            projectId: projectId,
          },
        });
  }
}
