﻿import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, Router, RouterStateSnapshot } from '@angular/router';

import { AlertService, AuthenticationService } from '@/services';

@Injectable({ providedIn: 'root' })
export class SessionGuard implements CanActivate {
  readonly ALERT_NOTIFICATION_TIME = 15;

  logoutTimer: any;
  alertTimer: any;

  constructor(
    private router: Router,
    private alertService: AlertService,
    private authenticationService: AuthenticationService,
  ) {}

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {
    const timeout = Math.max(this.authenticationService.getSessionRemainingTime(), 0);

    if (timeout > 0) {
      clearTimeout(this.alertTimer);
      this.alertTimer = setTimeout(
        () => this.alertTimerAction(),
        Math.max(0, timeout - this.ALERT_NOTIFICATION_TIME * 1000),
      );
    }

    clearTimeout(this.logoutTimer);
    this.logoutTimer = setTimeout(() => this.logoutTimerAction(state), timeout);

    return true;
  }

  alertTimerAction() {
    this.alertService.error(
      `Your session is about to be expired in less than ${this.ALERT_NOTIFICATION_TIME} seconds`, true
    );
  }

  logoutTimerAction(state: RouterStateSnapshot): any {
    this.alertService.error('Your session expired', true);
    this.authenticationService.logout();
    this.router.navigate(['/login'], { queryParams: { returnUrl: state.url } });
  }
}
