// This file can be replaced during build by using the `fileReplacements` array.
// `ng build --prod` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production: false,
  ADMIN_API_BASE_URL: 'https://testapi.kfassessment.com/support/api/',
  ADMIN_API_TOKEN_URL: 'https://testapi.kfassessment.com/support/Token',
  // ADMIN_API_BASE_URL: 'https://localkfasapi.talentqgroup.com/support/api/',
  // ADMIN_API_TOKEN_URL: 'https://localkfasapi.talentqgroup.com/support/Token',
  PP_DASHBOARD_PREVIEW_URL: 'https://testportal.kfassessment.com/Preview',
  PP_SIGNIN_PREVIEW_URL: 'https://test.kfassessment.com',
  ADMIN_APP_API_VERSION: '1.0',
  ADMIN_APP_ENV: 'local',
  ADMIN_APP_VERSION: '#{Octopus.Release.Number}',
  ADMIN_API_CAPTCHA_KEY: '6LfrPScUAAAAAJDseVVRtH06DzRJPNv8PmgaN_3h',
  ADMIN_APP_USEFAKE_BACKEND: false,
  REPORT_DOWNLOAD_LIMIT: 100,
  GOOGLE_CAPTCHA_ENABLED: true,
  UCP_SPS_BASE_URL: 'https://testproductsapi.kornferry.com/v1/hrms/ucp/',
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/dist/zone-error';  // Included with Angular CLI.
