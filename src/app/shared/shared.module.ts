import { AppMaterialModule } from "@/app.material.module";
import { DatePipe } from "@angular/common";
import { NgModule } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { MatCheckboxModule, MatSlideToggleModule } from "@angular/material";
import { BrowserModule } from "@angular/platform-browser";
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";
import { RouterModule } from "@angular/router";
import { ColorPickerModule } from "ngx-color-picker";
import { CdkDetailRowDirective } from "./directives/cdk-detail-row.directive";
import { AllowedRolesDirective } from "./directives/allowed-roles.directive";
import {
  AssessmentByIdPipe,
  LanguagePipe,
  LevelsPipe,
  UsergroupPipe,
  ScheduleDetailsPipe,
  ScheduleParticipantConfigPipe,
  SanitizeCssUrlPipe
} from "./pipes";

@NgModule({
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    ColorPickerModule,
    MatCheckboxModule,
    FormsModule,
    ReactiveFormsModule,
    MatSlideToggleModule,
    AppMaterialModule,
    RouterModule,
  ],
  declarations: [
    AssessmentByIdPipe,
    CdkDetailRowDirective,
    LanguagePipe,
    LevelsPipe,
    ScheduleDetailsPipe,
    ScheduleParticipantConfigPipe,
    AllowedRolesDirective,
    UsergroupPipe,
    SanitizeCssUrlPipe
  ],
  exports: [
    AssessmentByIdPipe,
    CdkDetailRowDirective,
    LanguagePipe,
    LevelsPipe,
    ScheduleDetailsPipe,
    ScheduleParticipantConfigPipe,
    AllowedRolesDirective,
    UsergroupPipe,
    SanitizeCssUrlPipe
  ],
  entryComponents: [],
  providers: [
    AssessmentByIdPipe,
    DatePipe,
    LanguagePipe,
    LevelsPipe,
    ScheduleDetailsPipe,
    ScheduleParticipantConfigPipe,
    UsergroupPipe,
    SanitizeCssUrlPipe
  ],
})
export class SharedModule {
  constructor() {}
}
