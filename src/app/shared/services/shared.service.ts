import { Injectable } from "@angular/core";
import { BehaviorSubject } from "rxjs";
import { User } from "../models";

@Injectable({ providedIn: 'root' })
export class SharedService {
    $currentUser: BehaviorSubject<User>;

    constructor() {
        this.$currentUser = new BehaviorSubject<User>(
            JSON.parse(localStorage.getItem("currentUser"))
        );
    }

    getCurrentUser(): User {
        return this.$currentUser.value
    }
}