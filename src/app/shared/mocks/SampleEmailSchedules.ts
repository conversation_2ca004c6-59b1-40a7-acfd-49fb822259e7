import { ProjectEmailSchedule } from "../models/ProjectEmailSchedule";

export const SampleEmailSchedules: ProjectEmailSchedule[] = [
    {
        projectId: null,
        emailSettingId: null,
        settingName: 'Invitation',
        templateType: 'INVITATION',
        templateId: 14584,
        templateSsoId:14584,
        settingType: 'PARTICIPANT_ADDED',
        emailLanguageType: 'NONE',
        byDateConfig: {
            sendDates: [],
        },
        byRecurringConfig: {
            recurrenceInterval: {
                sendDate: null,
                scheduleRepeats: null,
                recurringEnd: null,
                recurringWeekDayConfig: null,
                recurringMonth: null,
            }
        },
        participantSendConfig: {
            participantSendType: 'ALL_PARTICIPANTS',
            participantProgressTypes: null,
        },
    },
    {
        projectId: null,
        emailSettingId: null,
        settingName: 'Reminder',
        templateType: 'REMINDER',
        templateId: 15085,
        templateSsoId:15085,
        settingType: 'MANUALLY',
        emailLanguageType: 'NONE',
        byDateConfig: {
            sendDates: [],
        },
        byRecurringConfig: {
            recurrenceInterval: {
                sendDate: null,
                scheduleRepeats: null,
                recurringEnd: null,
                recurringWeekDayConfig: null,
                recurringMonth: null,
            }
        },
        participantSendConfig: {
            participantSendType: 'ALL_PARTICIPANTS',
            participantProgressTypes: null,
        },
    },
    {
        projectId: null,
        emailSettingId: null,
        settingName: 'Release',
        templateType: 'REPORT_RELEASE',
        templateId: 15473,
        templateSsoId:15473,
        settingType: 'MANUALLY',
        emailLanguageType: 'NONE',
        byDateConfig: {
            sendDates: [],
        },
        byRecurringConfig: {
            recurrenceInterval: {
                sendDate: null,
                scheduleRepeats: null,
                recurringEnd: null,
                recurringWeekDayConfig: null,
                recurringMonth: null,
            }
        },
        participantSendConfig: {
            participantSendType: 'ALL_PARTICIPANTS',
            participantProgressTypes: null,
        },
    },
    {
        projectId: null,
        emailSettingId: null,
        settingName: 'Scheduled by date reminder',
        templateType: 'REMINDER',
        templateId: 15085,
        templateSsoId:15085,
        settingType: 'BY_DATE',
        emailLanguageType: 'NONE',
        byDateConfig: {
            sendDates: [
                {
                    id: null,
                    dueDate: {
                        dateTime: new Date(1640962800000),
                        timeZone: 3,
                    },
                },
                {
                    id: null,
                    dueDate: {
                        dateTime: new Date(1641481200000),
                        timeZone: 3,
                    },
                },
                {
                    id: null,
                    dueDate: {
                        dateTime: new Date(1642086000000),
                        timeZone: 3,
                    },
                },
                {
                    id: null,
                    dueDate: {
                        dateTime: new Date(1640955600000),
                        timeZone: 3,
                    },
                },
                {
                    id: null,
                    dueDate: {
                        dateTime: new Date(1640955600000),
                        timeZone: 3,
                    },
                },
            ],
        },
        byRecurringConfig: {
            recurrenceInterval: {
                sendDate: null,
                scheduleRepeats: null,
                recurringEnd: null,
                recurringWeekDayConfig: null,
                recurringMonth: null,
            }
        },
        participantSendConfig: {
            participantSendType: 'ALL_PARTICIPANTS',
            participantProgressTypes: ['NOT_STARTED', 'NOT_INVITED'],
        },
    },
    { 
        projectId: null,
        emailSettingId: null,
        settingName: 'Weekday reminder',
        templateType: 'REMINDER',
        templateId: 15085,
        templateSsoId:15085,
        settingType: 'RECURRING_SCHEDULE',
        emailLanguageType: 'NONE',
        byDateConfig: {
            sendDates: [],
        },
        byRecurringConfig: {
            recurrenceInterval: {
                sendDate: {
                    dateTime: new Date('2021-12-23T18:00:00.000Z'),
                    timeZone: 3,
                },
                scheduleRepeats: 'WEEKDAY',
                recurringEnd: {
                    endType: 'ON_DATE',
                    endDate: {
                        dateTime: new Date('2022-01-13T14:18:08.918Z'),
                        timeZone: 3,
                    },
                    afterOccurrenceCount: null,
                },
                recurringWeekDayConfig: null,
                recurringMonth: null,
            },
        },
        participantSendConfig: {
            participantSendType: 'ALL_PARTICIPANTS',
            participantProgressTypes: ['NOT_STARTED'],
        },
    },
    {
        projectId: null,
        emailSettingId: null,
        settingName: 'Weekly reminder',
        templateType: 'REMINDER',
        templateId: 15085,
        templateSsoId:15085,
        settingType: 'RECURRING_SCHEDULE',
        emailLanguageType: 'NONE',
        byDateConfig: {
            sendDates: [],
        },
        byRecurringConfig: {
            recurrenceInterval: {
                sendDate: {
                    dateTime: new Date('2021-12-23T18:00:00.000Z'),
                    timeZone: 3,
                },
                scheduleRepeats: 'WEEK',
                recurringEnd: {
                    endType: 'AFTER_OCCURENCE',
                    endDate: null,
                    afterOccurrenceCount: 6,
                },
                recurringWeekDayConfig: null,
                recurringMonth: null,
            },
        },
        participantSendConfig: {
            participantSendType: 'ALL_PARTICIPANTS',
            participantProgressTypes: ['NOT_STARTED'],
        },
    },
    {
        projectId: null,
        emailSettingId: null,
        settingName: 'Month day reminder',
        templateType: 'REMINDER',
        templateId: 15085,
        templateSsoId:15085,
        settingType: 'RECURRING_SCHEDULE',
        emailLanguageType: 'NONE',
        byDateConfig: {
            sendDates: [],
        },
        byRecurringConfig: {
            recurrenceInterval: {
                sendDate: {
                    dateTime: new Date('2021-12-23T18:00:00.000Z'),
                    timeZone: 3,
                },
                scheduleRepeats: 'MONTH',
                recurringEnd: {
                    endType: 'PROJECT_END_DATE',
                    endDate: null,
                    afterOccurrenceCount: null,
                },

                recurringWeekDayConfig: null,
                recurringMonth: {
                    type: 'DAY_OF_MONTH',
                    dayOfMonth: 1,
                    weekDayNumber: null,
                    dayInWeek: null,
                    weekDaySelection: null,
                },
            },
        },
        participantSendConfig: {
            participantSendType: 'PARTICIPANTS_BY_PROGRESS',
            participantProgressTypes: ['NOT_STARTED', 'NOT_INVITED'],
        },
    },
    {
        projectId: null,
        emailSettingId: null,
        settingName: 'Month week reminder',
        templateType: 'REMINDER',
        templateId: 15085,
        templateSsoId:15085,
        settingType: 'RECURRING_SCHEDULE',
        emailLanguageType: 'NONE',
        byDateConfig: {
            sendDates: [],
        },
        byRecurringConfig: {
            recurrenceInterval: {
                sendDate: {
                    dateTime: new Date('2021-12-23T18:00:00.000Z'),
                    timeZone: 3,
                },
                scheduleRepeats: 'MONTH',
                recurringEnd: {
                    endType: 'PROJECT_END_DATE',
                    endDate: null,
                    afterOccurrenceCount: null,
                },
                recurringWeekDayConfig: null,
                recurringMonth: {
                    type: 'WEEK_DAY_SELECTION',
                    dayOfMonth: 1,
                    weekDayNumber: 1,
                    dayInWeek: "",
                    weekDaySelection: null,
                },
            },
        },
        participantSendConfig: {
            participantSendType: 'PARTICIPANTS_BY_PROGRESS',
            participantProgressTypes: ['NOT_STARTED'],
        },
    },
];
