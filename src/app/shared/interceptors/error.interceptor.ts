import { AuthenticationService } from '@/services';
import {
  HttpErrorResponse,
  HttpEvent,
  HttpHandler,
  HttpInterceptor,
  HttpRequest,
} from '@angular/common/http';
import { Injectable } from '@angular/core';
import { throwError, Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { ResponseError } from '../models';

@Injectable()
export class ErrorInterceptor implements HttpInterceptor {
  constructor(private authenticationService: AuthenticationService) {}

  intercept(
    request: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    return next.handle(request).pipe(
      catchError((err) => {
        const httpError = <HttpErrorResponse>err;
        if (httpError.status === 401) {
          // Logout if 401 response returned from api
          this.authenticationService.logout();
          location.reload();
        }

        // Angular HttpResponseErro r stores json message body as BLOB (BUG) , hence parsing to object here.
        if (
          httpError instanceof HttpErrorResponse &&
          httpError.error instanceof Blob &&
          httpError.error.type === 'application/json'
        ) {
          const reader = new FileReader();
          return new Promise<any>((resolve, reject) => {
            reader.addEventListener('loadend', (e: Event) => {
              try {
                reject(
                  new ResponseError(
                    httpError.status,
                    JSON.parse((<any>e.target).result)
                  )
                );
              } catch (e) {
                reject(err);
              }
            });
            reader.readAsText(httpError.error);
          });
        }

        // Create a Response error object that is simpler to interpret by components and services.
        return throwError(new ResponseError(httpError.status, httpError.error));
      })
    );
  }
}
