import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../environments/environment';
import { switchMap } from 'rxjs/operators';
import { ApiService } from '@/services';
import { SharedService } from '../services/shared.service';

@Injectable()
export class JwtInterceptor implements HttpInterceptor {
    constructor(private sharedSevice: SharedService, private apiService: ApiService) { }

    intercept(request: HttpRequest<any>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<any>> {
        // add authorization header with jwt token if available    
        let hubAuthenticate = localStorage.getItem("ucpApiAccessToken");
        const currentUser = this.sharedSevice.$currentUser.value;
        if (request.url.toString().includes(environment.UCP_SPS_BASE_URL)) {
            if (localStorage.getItem("ucpApiAccessToken") === null) {
                return this.apiService.getUcpHubToken().pipe(
                    switchMap(res => {
                        request = request.clone({
                            setHeaders: {
                                authToken: res.apiAccessToken,
                                'Content-Type': 'application/json'
                            }
                        });
                        return next.handle(request);
                    })
                )
            }
            else {
                request = request.clone({
                    setHeaders: {
                        authToken: hubAuthenticate,
                        'Content-Type': 'application/json'
                    }
                });
            }
        }
        else {
            if (currentUser && currentUser.token) {
                request = request.clone({
                    setHeaders: {
                        Authorization: `Bearer ${currentUser.token}`
                    }
                });
            }
        }
        return next.handle(request);
    }
}