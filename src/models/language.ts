/**
 * A system language that can be chosen for the site or an assessment.
 */
export interface Language {
    /**
     * System id for the language
     */
    id: number;

    /**
     * Culture code for the language in iso language-Culture format e.g. en-GB, en-US.
     */
    code: string;

    /**
     * Name of the language.
     */
    name: string;

    /**
     * Name of the language in it's own language, used for changing the site language.
     */
    nativeName: string;

    /**
     * Google code for the language in ISO 639-1 standard.
     */
    googleCode: string;
}