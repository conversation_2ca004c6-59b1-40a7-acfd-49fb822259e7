/**
 *  ProctoringSession model.
 */
export interface ProctoringSession {

    /**
     * The unique identifier for the proctoring session.
     */
    sessionUuid: string;

    /**
     * The assessment id that the proctoring session is for.
     */
    assessmentId: number;

    /**
     * The proctoring configuration id that the proctoring session is for.
     */
    proctoringConfigurationId: number | null;
}

export interface ProctoringModel {

    /**
     * Indicates whether the proctored assessment should be launched in a secure browser (TSB).
     */
    enforceTSB?: boolean;
}

export enum ProctoringAlertTypeIds {
    FaceAuthenticationFailed = 144,
}