import { AssessmentModel } from './assessment'
import { BrandingResult } from './branding';
import { ReportModel } from "./report";
import { eLearningModel } from './elearning';
import { ProctoringModel } from './proctoring';

/**
 * Represents personal information about a candidate.
 */
export interface PersonModel {
    /**
     * Gets the first name of the candidate.
     */
    firstName: string;

    /**
     * Gets the last name of the candidate.
     */
    lastName: string;

    /**
     * Gets the full display name of the candidate.
     */
    displayName: string;

    /**
     * Gets the email address of the candidate.
     */
    email: string;

    /**
     * Session Id of the candidate 
     * @type {number}     
     */
    id: number;

    /**
     * Id of the candidate 
     * @type {number}     
     */
    candidateId: number;
};

/**
 * Represents permissions for a candidate.
 */
export interface PermissionsModel {
    /**
     * Determines whether a candidate can reset all their assessments and personal data.
     */
    canResetAll: boolean;

    /**
     * Determines whether a candidate can reset individual assessments.
     */
    canResetAssessments: boolean;

    /**
     * Determines whether a legacy candidate can request candidate reports.
     */
    viewCandidateReport: boolean;
};

/**
 * Represents the current state values of a candidate.
 */
export interface CandidateStateModel {
    /**
     * Gets the current global language preference of the candidate.
     */
    languageId: number;

    /**
     * Determines if this user must accept the Global Privacy Policy.
     */
    showGPP: boolean;

    /**
     * Determines if this user has already accepted the Global Privacy Policy.
     */
    acceptedGPP: boolean;

    /**
     * Determines if this user must complete the demographics section.
     */
    showDemographics: boolean;

    /**
     * Determines if this user has already completed the demographics section.
     */
    completedDemographics: boolean;

    /**
     * Determines if this user has already completed demographics for a previous project
     * and results are within the validity period
     */
    demographicsReusable: boolean;

    /**
     * Date of most recent demographics that can be reused (null if no reusable)
     */
    mostRecentDemographicsDate?: Date;

    /**
     * BioDataResponseId of most recent demographics that can be reused (null if no reusable)
     */
    mostRecentDemographicsResponseId?: number;

    /**
     * Determines if this user has already read the important information section.
     */
    completedBestPractice: boolean;

    /**
     * Indicates if user is part of a blended (hub) project (could be multiple blended projects
     * but mixture of blended / legacy projects are not supported)
     */
    isInBlendedProject: boolean;
};

/**
 * Represents candidate contact information.
 */
export interface CandidateContactInfo {

    /**
     * Gets the name of the person to contact.        
     */
    contactName: string;


    /**
     * Gets the phone number of the person to contact.                
     */
    contactNumber: number;

    /**
     * Gets the email address of the person to contact.               
     */
    contactEmail: string;

    /**
     * Gets  whether there is enough information to display to a candidate.   
     */
    hasInfo: boolean;
}

/**
 * Represents all details about a candidate.
 */
export interface CandidateModel {
    /**
     * Gets the personal details for this candidate.
     */
    personal: PersonModel;

    /**
     * Gets the permissions for this candidate.
     */
    permissions: PermissionsModel;

    /**
     * Gets the current state of this candidate.
     */
    state: CandidateStateModel;

    /**
     * Gets the list of assessments this candidate will take or has completed.
     */
    assessments: AssessmentModel[];

    /**
     * Gets the list of reports that are available for this candidate.
     */
    reports: ReportModel[];

    /**
     * Gets the list of elearning content from KF Learn.
     */
    learningContents: eLearningModel[];

    /**
     * Indicates if reports are being prepared on the server but not yet ready for download
     */
    reportsPreparing: boolean;

    /**
     * Gets candidate contact information.
     */
    candidateContactInfo: CandidateContactInfo;

    /**
     * Gets the custom site branding information.
     */
    brandingStyles: BrandingResult;

    /**
     * Indicates if Success Profile was published or not. The warning message is 
     * displayed on the dashboard instead of available reports if the success 
     * profile is not published.   
     */
    successProfilePublished: boolean;

    /**
     * Gets the overall proctoring information for the candidate, 
     * whether to launch the assessment in a secure browser (TSB).
     */
    proctoring?: ProctoringModel;
};

/**
 * Represents the details that can be updated for a candidate
 */
export interface CandidateUpdateModel {
    /**
     * The candidates preferred language.
     */
    languageId: number;
}