import {
    getCandidateData,
    GetCandidateDataFinishType,
    GetCandidateDataStartType, 
    GetCandidateDataErrorType
} from '../../actions/candidate';
import { GetBrandingFinishType } from '../../actions/branding';
import configureMockStore from 'redux-mock-store'
import thunk from 'redux-thunk'
import { createAction } from '../../actions/actions';
import { ResponseError } from '../../services/api-service';

describe('Candidate Redux actions', () => {
    // Mock Redux stores
    const middlewares = [thunk];
    const mockStore = configureMockStore(middlewares);

    const mockResponse = (status: number, statusText: string, body: any) => {
        return new Response(body, {
            status: status,
            statusText: statusText,
            headers: {
                'Content-type': 'application/json'
            }
        });
    };

    // Mock sessionStorage API
    global["sessionStorage"] = {
        getItem: jest.fn(),
        setItem: jest.fn(),
        clear: jest.fn()
    };

    it('calls start and finish actions if the fetch response was successful', () => {
        const payload = { foo: 1, brandingStyles: { name: "HayGroup", styles: ""} };
        const expectedPayload = { foo: 1, brandingStyles: { name: "HayGroup"} };

        window.fetch = jest.fn().mockImplementation(() => Promise.resolve(
            mockResponse(
                200,
                '',
                JSON.stringify(payload))));

        const store = mockStore({});

        store.dispatch<any>(getCandidateData())
            .then(() => {
                const actions = store.getActions();

                expect(actions.length).toBe(3);
                expect(actions).toContainEqual(
                    createAction(GetCandidateDataStartType, {}));
                expect(actions).toContainEqual(
                    createAction(GetCandidateDataFinishType, payload));
                expect(actions).toContainEqual(
                    createAction(GetBrandingFinishType, expectedPayload.brandingStyles));
            });
    });

    it('calls start and error actions if the fetch response failed', () => {
        const expectedPayload: ResponseError = {
            status: 400,
            message: 'bad'
        };

        window.fetch = jest.fn().mockImplementation(() => Promise.resolve(
            mockResponse(
                expectedPayload.status,
                '',
                JSON.stringify(expectedPayload))));

        const store = mockStore({});

        store.dispatch<any>(getCandidateData())
            .then(() => {
                const actions = store.getActions();
                expect(actions.length).toBe(2);
                expect(actions).toContainEqual(
                    createAction(GetCandidateDataStartType, {}));
                expect(actions).toContainEqual(
                    createAction(GetCandidateDataErrorType, expectedPayload));
            });
    });
});