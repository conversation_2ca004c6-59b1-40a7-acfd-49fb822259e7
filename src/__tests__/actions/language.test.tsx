import { Actions } from "../../actions/language";
import { allTextFake } from "../../misc/mock-store";

// Mock sessionStorage API
global["sessionStorage"] = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    clear: jest.fn()
};

it("create change language action", () => {
    const payload = {
        languageId: 2,
        alltext: allTextFake,
        code: "de-DE",
        fetchingText: false,
        isLoaded: true,
        isRightToLeft: false
    }
    expect(Actions.changeLanguageSuccess(payload.languageId, payload.alltext, false)).toEqual(
        {
            type: Actions.LanguageChangeSuccessType, payload
        }
    );
});

it("create fetching site text action", () => {
    expect(Actions.fetchingSiteText()).toEqual({ type: Actions.FetchingSiteTextType, payload: true });
});