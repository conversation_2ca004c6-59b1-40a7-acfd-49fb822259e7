import { createAction } from '../../actions/actions';

it('Action object created with the correct type', () => {
  let type = 'string';
  let payload = 'payload';
  expect(createAction(type, payload).type).toEqual(type);
});

it('Action object created with string payload', () => {
  let type = 'string';
  let payload = 'payload';
  expect(createAction(type, payload).payload).toEqual(payload);
});

it('Action object created with type payload', () => {
  let type = 'string';
  let payload = { data: 1, value: 2}
  expect(createAction(type, payload).payload).toEqual(payload);
});