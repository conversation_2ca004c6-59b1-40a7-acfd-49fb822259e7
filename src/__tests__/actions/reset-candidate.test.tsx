import {
    ResetCandidate,
    ResetCandidateFailedType,
    ResetCandidateStartType,
    ResetCandidateSuccessType
} from '../../actions/reset-candidate';
import configureMockStore from 'redux-mock-store'
import thunk from 'redux-thunk'
import { createAction } from '../../actions/actions';
import { ResponseError } from '../../services/api-service';

describe('Reset candidate Redux actions', () => {
    // Mock Redux stores
    const middlewares = [thunk];
    const mockStore = configureMockStore(middlewares);

    const mockResponse = (status: number, statusText: string, body: any) => {
        return new Response(body, {
            status: status,
            statusText: statusText,
            headers: {
                'Content-type': 'application/json'
            }
        });
    };

    // Mock sessionStorage API
    global["sessionStorage"] = {
        getItem: jest.fn(),
        setItem: jest.fn(),
        clear: jest.fn()
    };

    it('calls start and finish actions if the fetch response was successful', () => {
        const expectedPayload = { foo: 1 };

        window.fetch = jest.fn().mockImplementation(() => Promise.resolve(
            mockResponse(
                200,
                '',
                JSON.stringify(expectedPayload))));

        const store = mockStore({});

        store.dispatch<any>(ResetCandidate())
            .then(() => {
                const actions = store.getActions();
                expect(actions.length).toBe(3);
                expect(actions).toContainEqual(
                    createAction(ResetCandidateStartType, true));
                expect(actions).toContainEqual(
                    createAction(ResetCandidateSuccessType, false));
            });
    });

    it('calls start and error actions if the fetch response failed', () => {
        const expectedPayload: ResponseError = {
            status: 400,
            message: 'bad'
        };

        window.fetch = jest.fn().mockImplementation(() => Promise.resolve(
            mockResponse(
                expectedPayload.status,
                '',
                JSON.stringify(expectedPayload))));

        const store = mockStore({});

        store.dispatch<any>(ResetCandidate())
            .then(() => {
                const actions = store.getActions();
                expect(actions.length).toBe(2);
                expect(actions).toContainEqual(
                    createAction(ResetCandidateStartType, true));
                expect(actions).toContainEqual(
                    createAction(ResetCandidateFailedType, expectedPayload));
            });
    });
});