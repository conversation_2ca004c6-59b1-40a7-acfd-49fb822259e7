import App from '../app';
import { createRenderer, ShallowRenderer } from 'react-test-renderer/shallow';

describe('<App />', () => {
  
  // Mock sessionStorage API
  global["sessionStorage"] = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    clear: jest.fn()
  };

  let shallowRenderer: ShallowRenderer = createRenderer();
  it('renders without crashing', () => {
    shallowRenderer.render(<App />);
  });

  it("first element should be a div", () => {
    shallowRenderer.render(<App />);
    const result = shallowRenderer.getRenderOutput();
    expect(result.type).toBe('div');
  });

  // Getting random     Invariant Violation: getNodeFromInstance: Invalid argument. error  will fix after styling work. 
  // it("should contain a header", () => {
  //   const app = create(<Provider store={store}><App /></Provider>);
  //   const children: ReactTestRendererJSON[] = app.toJSON().children as ReactTestRendererJSON[];
  //    expect(children.filter(c => c.type === 'header')).toHaveLength(1);
  // });
});
