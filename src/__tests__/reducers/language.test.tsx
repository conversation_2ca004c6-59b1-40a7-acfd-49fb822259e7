import { LanguageReducer } from '../../reducers/language';
import { Actions } from "../../actions/language";
import * as State from "../../reducers/state";
import { allTextFake } from "../../misc/mock-store";

// Mock sessionStorage API
global["sessionStorage"] = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  clear: jest.fn()
};

it('state is updated with new language code', () => {
  let newLanguageCode = 2;
  let allText = allTextFake;
  let expected = {
    languageId: newLanguageCode,
    code: "de-DE",
    alltext: allText,
    fetchingText: false,
    isLoaded: true,
    isRightToLeft: false
  } as State.Language;
  expect(LanguageReducer(undefined, Actions.changeLanguageSuccess(newLanguageCode, allText, false))).toEqual(expected);
});