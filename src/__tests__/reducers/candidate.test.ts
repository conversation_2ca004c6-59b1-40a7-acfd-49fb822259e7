import { CandidateReducer, initialState } from '../../reducers/candidate';
import { createAction } from '../../actions/actions';
import {
    GetCandidateDataErrorType,
    GetCandidateDataFinishType,
    GetCandidateDataStartType
} from '../../actions/candidate';
import { AssessmentStatus, AssessmentType, CompatibilityStatus } from '../../models/assessment';
import { CandidateModel } from '../../models/candidate';
import * as State from '../../reducers/state';
import { ResponseError } from '../../services/api-service';

describe('Candidate Redux reducers', () => {
    // Replicated internal actions
    const startGetCandidateData = () => createAction(GetCandidateDataStartType, {});
    const finishGetCandidateData = (results: CandidateModel) => createAction(GetCandidateDataFinishType, results);
    const failedGetCandidateData = (error: ResponseError) => createAction(GetCandidateDataErrorType, error);

    // Mock sessionStorage API
    global["sessionStorage"] = {
        getItem: jest.fn(),
        setItem: jest.fn(),
        clear: jest.fn()
    };

    it('should return the initial state when not defined', () => {
        const actual = CandidateReducer(undefined, startGetCandidateData());
        expect(actual).toEqual(initialState);
    });

    it('should return expected state on start action', () => {
        const expected: State.Candidate = {
            isLoaded: false,
            isRefreshing: false,
            isPreviewMode: false,
            reportsPreparing: false,
            successProfilePublished: true
        };

        const actual = CandidateReducer(expected, startGetCandidateData());
        expect(actual).toEqual(expected);
    });

    it('should return expected state on finish action', () => {
        const payload: CandidateModel = {
            personal: {
                firstName: 'foo',
                lastName: 'bar',
                displayName: 'foo bar',
                email: "<EMAIL>",
                id: 123,
                candidateId: 123
            },
            permissions: {
                canResetAll: false,
                canResetAssessments: true,
                viewCandidateReport: true
            },
            state: {
                languageId: 1,
                showGPP: true,
                acceptedGPP: false,
                showDemographics: true,
                completedDemographics: false,
                completedBestPractice: false,
                demographicsReusable: false,
                mostRecentDemographicsDate: new Date("2019-11-13T14:35:22.853"),
                mostRecentDemographicsResponseId: 999,
                isInBlendedProject: false
            },
            assessments: [{
                assessmentId: 1,
                type: AssessmentType.Dimensions,
                subTestId: 0,
                status: AssessmentStatus.NotStarted,
                isEnabled: true,
                isLocked: false,
                languageId: 1,
                needsMouse: true,
                needsNotepad: false,
                needsSound: false,
                needsCalculator: false,
                timeLengthMinutes: 10,
                timeExtraMinutes: 0,
                compatability: CompatibilityStatus.Compatible,
                supportedLanguageIds: [1, 2],
                jobProfileName: '',
                name: '',
                description: '',
                reuseAssessmentAvailable: false,
                reuseAssessmentId: null,
                reuseAssessmentDateTime: null,
                latestProjectDeadline: null,
                isProctored: false,
                proctoringToken: null,
                isProctoringLocal: false,
                scope: null
            }],
            reports: [{
                projectId: 1,
                reportType: "Bob1",
                name: "Bob Report",
                date: new Date(2020, 8, 4, 9, 30, 45, 843),
                languageId: 1,
                blendedReportId: 123,
                format: "PDF"
            }],
            learningContents: [{
                contentId: 1,
                name: "Competency learning microsite"
            }],
            reportsPreparing: true,
            candidateContactInfo: {
                contactName: "foo",
                contactEmail: "<EMAIL>",
                contactNumber: 1234,
                hasInfo: true
            },
            brandingStyles: {
                styles: "",
                version: "",
                errorText: "",
                name: "",
                logoUrl: "",
                hasDashboardManifest: false
            },
            successProfilePublished: true
        };

        const expected: State.Candidate = {
            isLoaded: true,
            isRefreshing: false,
            isPreviewMode: false,
            lastError: undefined,
            personal: payload.personal,
            permissions: payload.permissions,
            state: payload.state,
            assessments: payload.assessments,
            candidateContactInfo: payload.candidateContactInfo,
            reports: payload.reports,
            eLearnings: payload.learningContents,
            reportsPreparing: true,
            successProfilePublished: true
        };

        const actual = CandidateReducer(expected, finishGetCandidateData(payload));
        expect(actual).toEqual(expected);
    });

    it('should return expected state on error action', () => {
        const error: ResponseError = {
            status: 400,
            message: 'bad'
        };

        const expected: State.Candidate = {
            isLoaded: false,
            isRefreshing: false,
            isPreviewMode: false,
            lastError: error,
            reportsPreparing: false,
            successProfilePublished: true
        };

        const actual = CandidateReducer(expected, failedGetCandidateData(error));
        expect(actual).toEqual(expected);
    });
});