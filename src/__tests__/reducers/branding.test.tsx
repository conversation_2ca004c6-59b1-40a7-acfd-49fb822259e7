import { BrandingReducer } from '../../reducers/branding';
import { finishGetBranding } from "../../actions/branding";
import { BrandingResult } from "../../models/branding";
import * as State from "../../reducers/state";

it('should return the initial state', () => {

  // Mock sessionStorage API
  global["sessionStorage"] = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    clear: jest.fn(),
    removeItem: jest.fn()
  };

  const branding: BrandingResult = {
    version: "",
    name: "",
    errorText: "",
    logoUrl: "",
    hasDashboardManifest: false
  };

  const expected: State.Branding = {
    loadingBrandingUrl: false,
    logoUrl: "",
    name: "",
    hasDashboardManifest: false,
    version: ""
  };

  expect(BrandingReducer(undefined, finishGetBranding(branding))).toEqual(expected);
});