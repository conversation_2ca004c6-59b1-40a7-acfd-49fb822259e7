import { render, within } from '@testing-library/react'
import StepProgressBar from "../../../../components/common/step-progress-bar/step-progress-bar";

describe('Common Components - step progress bar', () => {
    it('renders without crashing', () => {
        render(
            <StepProgressBar
                maxSteps={5}
                currentStep={0}
            />
        );
    });

    it('One <div class="tile"> for each step', () => {
        for (let i = 0; i < 10; i++) {
            const maxSteps = i + 1;
            const stepProgressBar = render(
                <StepProgressBar
                    maxSteps={maxSteps}
                    currentStep={0}
                />
            );
            const progressBars = stepProgressBar.queryAllByTestId('dashboard-progress-bar');
            expect(within(progressBars[i]).queryAllByTestId('progress-step').length).toEqual(maxSteps);
        }
    });

    it('Max step count on tiles is correct', () => {
        const maxSteps = 10;
        const stepProgressBar = render(
            <StepProgressBar
                maxSteps={maxSteps}
                currentStep={0}
            />
        );
        expect(stepProgressBar.queryAllByTestId('progress-step').length).toEqual(maxSteps);
    });

    it('One .step-complete for completed step', () => {
        for (let i = 0; i < 10; i++) {
            const completedSteps = i + 1;
            const stepProgressBar = render(
                <StepProgressBar
                    maxSteps={10}
                    currentStep={completedSteps}
                />
            );
            const progressBars = stepProgressBar.queryAllByTestId('dashboard-progress-bar');
            expect(within(progressBars[i]).queryAllByTestId('progress-step-complete').length).toEqual(completedSteps)
        }
    });
});