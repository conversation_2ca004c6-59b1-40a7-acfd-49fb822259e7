import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import { Provider } from "react-redux";
import { Middleware } from "redux";
import thunk from "redux-thunk";
import { logReduxError } from "../../../misc/error";
// @ts-ignore
import reduxCatch from "redux-catch";
import configureStore from "redux-mock-store";
const _alltext = require("../../data/alltextState");
import CompletedAssessmentsMessage from "../../../components/dashboard/completedAssessmentsMessage";
import IntlProviderContainer from "../../../components/intl-provider-container";

describe("Session timeout container component", () => {
  let middlewares: Middleware[] = [];

  middlewares.push(thunk);
  middlewares.push(reduxCatch(logReduxError));

  const mockStore = configureStore(middlewares);

  const state = {
    language: {
      code: "en",
      languageId: 16,
      alltext: _alltext,
    },
  };

  it("renders You've completed all of your assessments! Your results will be available in Korn Ferry Assess shortly.", () => {

    const store = mockStore(state);
    const expectedText = state.language.alltext.dashboard.allAssessmentsCompleted.description;

      render(
          <Provider store={store}>
              <IntlProviderContainer>
                  <CompletedAssessmentsMessage />
              </IntlProviderContainer>
          </Provider>
      );

    expect(screen.queryByText(expectedText)).toBeInTheDocument();
  });
});