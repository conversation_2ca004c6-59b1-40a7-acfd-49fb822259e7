import { render } from '@testing-library/react'
import { OverallProgress } from '../../../components/dashboard/overall-progress';
import { CmsText } from "../../../misc/cms-text";
import '@testing-library/jest-dom'

describe('Dashboard overall-progress', () => {

    const cmsText = new CmsText({
        "noCompletedAssessments": 'None Completed',
        "someCompletedAssessmentsPart1": "Your Progress:",
        "someCompletedAssessmentsPart2": "{completed} of {total} Assessments Complete",
        "someCompletedAssessmentsPart3": "{completed} of {total} Assessments Complete ({locked} assessments have expired)"
    }, 'Overall-Progress');

    it('renders without crashing', () => {
        render(
            <OverallProgress
                completedAssessments={0}
                totalAssessments={4}
                lockedAssessments={0}
                componentText={cmsText}
            />
        );
    });

    it('contains a step progress bar', () => {
        const totalAssessments = 4;
        const overallProgress = render(
            <OverallProgress
                completedAssessments={0}
                totalAssessments={totalAssessments}
                lockedAssessments={0}
                componentText={cmsText} />
        );

        expect(overallProgress.queryAllByTestId("progress-step").length).toEqual(totalAssessments);
    });

    it('do not show no assessment completed when at least one assessment has been completed', () => {
        const overallProgress = render(
            <OverallProgress
                completedAssessments={0}
                totalAssessments={4}
                lockedAssessments={0}
                componentText={cmsText} />
        );
        expect(overallProgress.queryByText("None Completed")).not.toBeNull();
    });

    let testCases = [
        { total: 4, completed: 1, locked: 0 },
        { total: 4, completed: 2, locked: 0 },
        { total: 4, completed: 3, locked: 0 },
        { total: 4, completed: 4, locked: 0 },
        { total: 4, completed: 4, locked: 2 }
    ];

    testCases.forEach(function (run) {
        it(`show progress in assessment completed message with ${run.completed} of ${run.total} completed`, function () {
            const overallProgress = render(
                <OverallProgress
                    completedAssessments={run.completed}
                    totalAssessments={run.total}
                    lockedAssessments={run.locked}
                    componentText={cmsText}
                />
            );
            expect(overallProgress.getByTestId("dashboard-progress-title").textContent).toMatch(`${run.completed} of ${run.total}`)
        });
    });
});