import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import DownloadReports from '../../../components/dashboard/downloadReports';
import configureStore from 'redux-mock-store'
import { Middleware } from 'redux';
import { Provider } from 'react-redux';
import thunk from 'redux-thunk';
import { logReduxError } from '../../../misc/error';
// @ts-ignore
import reduxCatch from 'redux-catch';
import IntlProviderContainer from '../../../components/intl-provider-container';

const _alltext = require('../../data/alltextState');

describe('Download Reports', () => {
   
    let middlewares: Middleware[] = [];

    middlewares.push(thunk);
    middlewares.push(reduxCatch(logReduxError));

    const mockStore = configureStore(middlewares)
    const state = {
        language: {
            code: "en",
            languageId: 16,
            alltext: _alltext
        },
        candidate: {
            reports: [
                {
                    projectId: 191252,
                    reportType: "Fonterra-SJT-Participant-LabTechnician",
                    date: "2023-03-13T20:17:31.917",
                    name: "fonterra.sjt",
                    languageId: 16,
                    reportOptions: null
                },
                {
                    projectId: 191252,
                    reportType: "simple.one",
                    date: "2023-03-13T20:17:31.917",
                    name: "11111",
                    languageId: 16,
                    reportOptions: null
                }
            ],
            successProfilePublished: true
        }
    }

    it('renders DownloadReports component WITH Success Profile warning message. The successProfilePublished property is FALSE.', () => {

        const expectedText = state.language.alltext.dashboard.downloadReport.successProfileNotPublished;

        state.candidate.successProfilePublished = false;

        const store = mockStore(state);

        render(
            <Provider store={store}>
                <IntlProviderContainer>
                    <DownloadReports />
                </IntlProviderContainer>
            </Provider>
        );

        expect(screen.queryByText(expectedText)).toBeInTheDocument();
    });

    it('renders DownloadReports component WITHOUT Success Profile warning message. The successProfilePublished property is TRUE.', () => {

        const expectedText = state.language.alltext.dashboard.downloadReport.successProfileNotPublished;

        state.candidate.successProfilePublished = true;

        const store = mockStore(state);

        render(
            <Provider store={store}>
                <IntlProviderContainer>
                    <DownloadReports />
                </IntlProviderContainer>
            </Provider>
        );

        expect(screen.queryByText(expectedText)).not.toBeInTheDocument();
    });

    it('renders DownloadReports component WITHOUT Success Profile warning message. No successProfilePublished property.', () => {

        const expectedText = state.language.alltext.dashboard.downloadReport.successProfileNotPublished;

        // @ts-ignore
        delete state.candidate.successProfilePublished;

        const store = mockStore(state);

        render(
            <Provider store={store}>
                <IntlProviderContainer>
                    <DownloadReports />
                </IntlProviderContainer>
            </Provider>
        );

        expect(screen.queryByText(expectedText)).not.toBeInTheDocument();
    });
});