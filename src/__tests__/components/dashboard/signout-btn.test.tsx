import { render } from "@testing-library/react";
import { Provider } from "react-redux";
import { Middleware } from "redux";
import thunk from "redux-thunk";

import IntlProviderContainer from "../../../components/intl-provider-container";
import HeaderContainer from "../../../components/header/header";
import { logReduxError } from "../../../misc/error";
// @ts-ignore
import reduxCatch from "redux-catch";
import configureStore from "redux-mock-store";

const _alltext = require("../../data/alltextState");

describe("Dashboard Page component", () => {
  let middlewares: Middleware[] = [];

  middlewares.push(thunk);
  middlewares.push(reduxCatch(logReduxError));

  const mockStore = configureStore(middlewares);

  const state = {
    language: {
      code: "en",
      languageId: 16,
      alltext: _alltext,
    },

    header: {
      showMenu: false,
    },
    branding: {
      logoUrl: "",
      name: "",
    },
  };

  it("hide signout btn ", () => {
    const store = mockStore(state);

    const { container } = render(
      <Provider store={store}>
        <IntlProviderContainer>
          <HeaderContainer showHumburgerMenuIcon={false} showLanguageSelectDropdown={true}/>
        </IntlProviderContainer>
      </Provider>
    );

    const button = container.getElementsByClassName("hamburger-menu-icon");

    expect(button.length).toBe(0);
  });
});