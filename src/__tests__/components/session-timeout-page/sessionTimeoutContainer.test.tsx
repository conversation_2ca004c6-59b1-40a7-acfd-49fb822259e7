import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import { Provider } from "react-redux";
import { Middleware } from "redux";
import thunk from "redux-thunk";
import { logReduxError } from "../../../misc/error";
// @ts-ignore
import reduxCatch from "redux-catch";
import configureStore from "redux-mock-store";
import SessionTimeoutContainer from "../../../components/session-timeout-page/sessionTimeoutContainer";
const _alltext = require("../../data/alltextState");

describe("Session timeout container component", () => {
  let middlewares: Middleware[] = [];

  middlewares.push(thunk);
  middlewares.push(reduxCatch(logReduxError));

  const mockStore = configureStore(middlewares);

  const state = {
    language: {
      code: "en",
      languageId: 16,
      alltext: _alltext,
    },
    header: {
      showMenu: false,
    },
    branding: {
      logoUrl: "",
      name: "",
    },
  };

  it("renders Your session has timed out due to inactivity. Please close this window and use your assessment link to return.", () => {
    
    const store = mockStore(state);
    const expectedText = state.language.alltext.loggedOut.message;

    render(
      <Provider store={store}>
        <SessionTimeoutContainer />
      </Provider>
    );

    expect(screen.queryByText(expectedText)).toBeInTheDocument();
  });
});
