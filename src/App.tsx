import { EmptyObject } from './misc/common';
import PasswordResetRequestPage from './pages/password-reset-request';
import ErrorPage from './pages/error-page';
import SigninPage from './pages/signin';
import TokenPage from './pages/token';
import PasswordResetPage from './pages/password-reset';
import OpenInvitationPage from './pages/open-invitation';
import PasswordExpired from './pages/password-expired';
import SetPassword from './pages/set-password';
import * as React from 'react';
import { BrowserRouter, Route, Routes } from 'react-router-dom';
import { connect, MapDispatchToProps } from 'react-redux';
import State from './reducers/state';
import FontResize from './services/font-resize.service';
import './App.css';

export type AppProps = {
  brandedLogoUrl: string
};

const mapStateToProps = (state: State.All) => ({
  brandedLogoUrl: state.branding.logoUrl
});

const mapDispatchToProps: MapDispatchToProps<EmptyObject, EmptyObject> = () => ({});

/**
 * The main signin app.
 */
class App extends React.Component<AppProps, EmptyObject> {
  private fontResize: FontResize = new FontResize();

  render() {
    /* SUBSCRIBE font-size changes */
    this.fontResize.startDetect();

    return (
      <div className="App">
        <BrowserRouter>
          <Routes>
            <Route path="/:brandingName?/:isPreview?" element={<SigninPage/>} />
            <Route path="/:brandingName?/:isPreview?/open-invitation/:id/:ppaccepted?" element={<OpenInvitationPage/>} />
            <Route path="/:brandingName?/:isPreview?/token/:token" element={<TokenPage/>} />
            <Route path="/:brandingName?/:isPreview?/password-reset/:token?" element={<PasswordResetPage/>} />
            <Route path="/:brandingName?/:isPreview?/password-reset-request" element={<PasswordResetRequestPage/>} />
            <Route path="/:brandingName?/:isPreview?/password-expired" element={<PasswordExpired/>} />
            <Route path="/:brandingName?/:isPreview?/set-password" element={<SetPassword/>} />
            <Route path="/:brandingName?/:isPreview?/error/:initialError" element={<ErrorPage/>} />
          </Routes>
        </BrowserRouter>
      </div>
    );
  }

  componentWillUnmount() {
      /* UNSUBSCRIBE font-size changes */
      this.fontResize.stopDetect();
  }
}

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(App);
