# See https://help.github.com/ignore-files/ for more about ignoring files.

# visual studio code configuration
.vscode/
.idea/

# mac os files
.DS_Store

# dependencies
/node_modules

# testing
/coverage
template/src/__tests__/__snapshots__/

# production
/build
/dist

# Chrome debug folder
/.chrome

# local environment config
.env.local
.env.development.local
.env.test.local
.env.production.local

# package installer logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log
yarn.lock




# misc
*.tgz
/.changelog

# Css
*.css
*.css.map
/.vs

# Runtime environment config (active file - use environment-specific templates)
/public/runtime-env.js
