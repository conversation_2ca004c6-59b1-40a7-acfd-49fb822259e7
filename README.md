# KFAS Sign-in #

The KFAS sign-in app, used as the main entry point for administrators and candidates to enter the KFAS platform. Supports sign-in, password reset and self-service.

The code base was created from the [create-react-app-typescript](https://github.com/wmonk/create-react-app-typescript) (the Typescript version of Facebook's create-react-app) so most of the documentation will still apply.

## Getting started

Ensure you have;

- Node version 16.13.1 or higher
- Typescript 2.3.4 or higher

Clone the application from GitHub, to install all of the node packages: 
```node
npm install
```

Start the application (a localhost url will be created)
```
npm start
````
 
Run unit tests with:
```
npm test
```

## Main frameworks/libraries

- [React](https://facebook.github.io/react/) for the front end
- [React router](https://reacttraining.com/react-router/) for application routing
- [Redux](http://redux.js.org) for state management
- [Redux-form](http://redux-form.com/6.8.0/) for form management, [Redux-form-validators](https://github.com/gtournie/redux-form-validators) for form validation
- [Bootstrap v4](https://v4-alpha.getbootstrap.com/) as the basis for styling

## Folder hierarchy
The application is set up with the follow folder structure, it outlines how the files are divided and where to place new developments.

* `__tests__` - Contains all unit tests, folder structure to match the application framework
* `styles` - Mixture of standard Bootstrap components and our custom styles/variables
* `components` - React components, note these do not know anything about Redux, they should be as simple as possible. Note the sass style files should sit next to the component to keep everything together.
* `containers` - Ties the components to the Redux data store
* `misc` - For files that do not fit easily into other categories, e.g. data models, helper functions.
* `pages` - The web pages are split up using React Routing (see `App.tsx`) for details of the page, the routing requires component props of type `RouteComponentProps`. The components used in the pages shouldn't be aware of this, so these pages hide this.
* `actions` to react to app interactions, use services and dispatch state updates
* `reducers` - Redux reducers for updating the global state. Reducer composition is used so one file per functional area. Files to note:
    * `app.ts` - The parent reducer file to combine all of the other reduces.
    * `state.ts` - The application state interface
* `services` - Services to interact with the API. Note that'app-service' contains generic http calls, used by the other services.
* `resources` - fonts & images, stored here so the build process can decide if they should be embedded or referenced.

## Production
To bundle the application ready for deployment to a production environment run:
```
npm run build
```
This creates a build folder with minified css and js files ready to be packaged into a nuget file.

### Production Configuration
Any configuration required for production should be maintained in the `env.production` configuration file, this is used when building the application.

## DEV Local (to simulate deployed version without hot reload interference):

If you wish to run this app in the web server to simulate lower environments do the following:

1. Run the following command to build DEV LOCAL version and watch for changes
```
npm run build:watch:dev
```
2.  To serve application you need to run DEV LOCAL web server. Please open new terminal window and execute this command:
```
npm run start:server:dev
```
3. You would need to find a way to map remote https://testportal.kfassessment.com to https://localhost:8080. Charles Web Proxy or Proxyman tools have a great feature to Map Remote. There are some Chrome plugins that will help you with that as well.

