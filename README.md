# kfas admin site

This project was generated with [Angular CLI](https://github.com/angular/angular-cli) version 7.0.3.

## Development server

Run `ng serve` for a dev server. Navigate to `http://localhost:4200/`. The app will automatically reload if you change any of the source files.

## Code scaffolding

Run `ng generate component component-name` to generate a new component. You can also use `ng generate directive|pipe|service|class|guard|interface|enum|module`.

## Build

Run `npm run buildLocal` to build the project. The build artifacts will be stored in the `build/` directory. Use the `npm run build`  for a production build.

## Running unit tests

Run `npm run test` to execute the unit tests via [Karma](https://karma-runner.github.io).

## Running end-to-end tests

Run `npm run e2e` to execute the end-to-end tests via [Protractor](http://www.protractortest.org/).

## Further help

To get more help on the Angular CLI use `ng help` or go check out the [Angular CLI README](https://github.com/angular/angular-cli/blob/master/README.md).


## Project Structure 

There is a folder per feature/shared/common code  (home, login & register, services, models, guards, components & helpers).

The index.ts files in each folder are barrel files that group the exported modules from a folder together so they can be imported using the folder path instead of the full module path and to enable importing multiple modules in a single import (e.g. import { AlertService, UserService, AuthenticationService } from '@/services').

A path alias '@' has been configured in the tsconfig.json and angular.js that maps to the '/src/app' directory. This allows imports to be relative to the '/src/app' folder by prefixing the import path with '@', removing the need to use long relative paths like import MyComponent from '../../../MyComponent'.


### Folder hierarchy

 
* `components` - Angular components.
   * `users`- component to display list of registered users. NOTE: will be removed as another app will be used to manage users. 
* `environments` - class with environment variables. see Angular.js files for file replacement details.
* `guards` - The auth guard is used to prevent unauthenticated users from accessing restricted routes.
* `helpers` - Various helper services and http interceptors.
	* `error-interceptor`- The Error Interceptor intercepts http responses from the api to check if there were any errors. If there is a 401 Unauthorized response the user is automatically logged out of the application.
	* `fake-backend` - Provides fakes response based in request url used. This will be used for building this app in isolation.
	* `jwt-interceptor`- The JWT Interceptor intercepts http requests from the application to add a JWT auth token to the Authorization header if the user is logged in.
	* `query-options` - Helper class to to build query string parameters for  API request. ( needs  api-service to be in place)
* `models` - Data models for data that comes from the server
* `services` -  contains all server API related service code
    * `alert-service` - The alert service enables any component in the application to display alert messages at the top of the page via the alert component.
	* `authentication-service` - The authentication service is used to login and logout of the application, to login it posts the users credentials to the api and checks the response for a JWT token, if there is one it means authentication was successful so the user details including the token are added to local storage.

* `assets` - All site assets.

### Styles
 The site is configured to use scss styling. `style.scss `  is the global file and all other style files should be referenced here. 
 TODO: currently bootstrap is referenced though cnd link (cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.1.3/css/bootstrap.min.css) this needs to be replaced using standard kf styling (producthub)
 
 
### Local development without server api.

Set ADMIN_APP_USEFAKE_BACKEND to true in environment.ts and api calls will be intercepted by fake-backend service. 
To login click register to  register a user , and  username and  password to login. 
NOTE: fake-backend service will not be updated going forwards. This was setup to support front end development whilst awaiting api access.
       
 ##TODO: 
   * `Logger` - Add a logger service.
