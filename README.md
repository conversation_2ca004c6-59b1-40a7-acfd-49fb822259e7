# KF Candidate assessment Dashboard

The candidate dashboard site, containing the list of assessments, important information and links to start assessments hosted on the Reflex platform.

The code base was created from the [create-react-app](https://github.com/facebookincubator/create-react-app) so most of the documentation will still apply.

## Getting started

Clone the application from GitHub, to install all of the node packages: 

    npm install

Start the application and load the website at `http://localhost:3002`: 

### **Local Development**
```bash
# Set to local environment and start development
npm run start:local

# Or manually switch environment
npm run env:local
npm start
```

### **Manual Environment Switching**
```bash
# Using NPM scripts
npm run env:local      # Switch to local
npm run env:prod       # Switch to production
npm run env:list       # Show available environments

# Using Node.js script directly
node scripts/set-env.js local
node scripts/set-env.js prod

# Using PowerShell (Windows)
.\scripts\set-env.ps1 local
.\scripts\set-env.ps1 prod
```
 
Run unit tests with:

    npm test

Finally, perform a build with:

### **Building for Different Environments**
```bash
# Build for specific environments
npm run build:local    # Local development build
npm run build:prod     # Production environment
```

## Project Structure

The application is written using [React](https://facebook.github.io/react/) for the front end, using [Redux](http://redux.js.org) for state management.

### Folder hierarchy
The application is set up with the follow folder structure, it outlines how the files are divided and where to place new developments.

* `__tests__` - Contains all unit tests, folder structure to match the application framework
* `actions` - Redux actions, one action file per reducer
* `components` - React components, including any Redux connected containers. They should be as simple as possible. Note the sass style files should sit next to the component to keep everything together.
* `models` - Data models for data that comes from the server
* `pages` - The web pages are split up using React Routing, see `app.tsx` for details of the page. The routing requires component props of type `RouteComponentProps`. The components used in the pages shouldn't be aware of this, so these pages hide this.
* `reducers` - Redux reducers for updating the global state. Reducer composition is used so one file per functional area. Files to note:
    * `app.ts` - The parent reducer file to combine all of the other reduces.
    * `state.ts` - The application state interface
* `service` - contains all server API related service code
* `styles` - Global styles and variables for including and overriding bootstrap.

## Production
To bundle the application ready for deployment to a production environment run:
```
npm run build
```
This creates a build folder with minified css and js files ready to be packaged into a nuget file.

### Production Configuration
Any configuration required for production should be maintained in the `public/runtime-env.prod.js` configuration file, this is used when building the application.

### CDN
The CDN url is configured through the `PUBLIC_URL` environment variable in `public/runtime-env.prod.js`. It has been configured to use an OctopusDeploy variable that will be replaced with the appropriate dev, staging, production value.

## Development Notes

### Entry point and authentication

Normally the entry point/URL is provided via the KF Signin app and Signin API, which should automatically send you to somewhere like this:

    http://localhost:3000/authenticate/[login-token]

The `[login-token]` is based on the usual Talent Q login token and contains relevant encrypted details such as the user to login as. The `[login-token]` must be present for this app to authenticate you, since all future candidate dashboard API calls are secured by a JSON Web Token (JWT) which is provided once the `[login-token]` has been verified on the server.

The process flow for entry/authentication therefore is:

1. Identify the route `/authenticate/:token` in the router under `App.tsx`
2. Render the `pages\Authenticate` component which tracks current status and server requests. This passed the `[login-token]` to the server to expect back a JWT.
3. On success, the JWT is saved to the client-side session storage and redirects to the dashboard page
4. On failure one of two things can happen:
    1. **Production** - redirect out of the app back to the KF signin app
    2. **Development** - show a developer error page with request error details

### Guaranteed authentication

The router components in `App.tsx` have been configured to ensure that, except for the `Authenticate` component, every other page is considered private and must have a session created in order to access the page. This is done via a [higher-order component](https://facebook.github.io/react/docs/higher-order-components.html) called `withAuthentication()` which performs two basic tasks:

1. If the session is present, render the component passed to it
2. If there is no session, redirect back to `/authenticate` - this automatically triggers the failure case and then kicks you out of the app

All future pages must also be locked down in the same way.

### Guaranteed initial state

Once authenticated, we must also ensure that the Redux store has an initial state from the server with all the candidate data required. The problem here is that we can only request the server data once authenticated (because we need a JWT to authorize), but the page could also be refreshed meaning the JWT is present but the Redux store could be empty.

To get around this we use another higher-order component called `withCandidateData()` which performs two basic tasks:

1. If the Redux store contains the candidate data then render the component passed to it
2. If there is no candidate data then send a server request to populate the store and render a loading spinner while waiting

When this is chained with the `withAuthentication()` HOC then we can guarantee that:

1. Every private page requires authentication
2. Every page requires candidate data


## CMS content:

Dashboard Manifest information: 
Name: projects.internal.kf4d.Dashboard
Server : http://s3.amazonaws.com/prod.contentcloud.com/index.html
URL to access unpublished content: http://52.205.128.33:8055/dev/contentDictionary/manifests

Site content and languages supported by site is stored in Language object within “alltext” field.
How to add content for a specific page:	
1.	Add a new category to Dashboard manifest e.g. “welcome”.
2.	Add a Prop to the category and assign text value e.g. prop Name: Title, value “Welcome to Dashboard”.
	// TODO: Update publish process.  
3.	Now you can access the category “welcome” like this. (see signin.tsx container in signin app)
componentText: state.Language.alltext.welcome

To access language object use the following (see signin-box.tsx container in signin app).
   availableLanguages: state.Language.alltext.allLanguages

Properties of allLanguages object can be found in "\TQAS\KornFerry.Cms\Models\LanguageModel.cs"

## DEV Local (to simulate deployed version without hot reload interference):

If you wish to run this app in the web server to simulate lower environments do the following:

1. Run the following command to build DEV LOCAL version and watch for changes
```
npm run build:watch:dev
```
2.  To serve application you need to run DEV LOCAL web server. Please open new terminal window and execute this command:
```
npm run start:server:dev
```
3. You would need to find a way to map remote https://testportal.kfassessment.com to https://localhost:8080. Charles Web Proxy or Proxyman tools have a great feature to Map Remote. There are some Chrome plugins that will help you with that as well.
